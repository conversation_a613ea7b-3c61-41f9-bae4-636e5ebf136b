/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.wordsfairy.base.utils

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import java.lang.reflect.Type

object GsonUtils {
    private val gson: Gson = Gson()
    private val excludeGson: Gson = GsonBuilder().excludeFieldsWithoutExposeAnnotation().create()

    fun getGson(): Gson {
        return gson
    }

    fun getExcludeFieldsWithoutExposeGson(): Gson {
        return excludeGson
    }

    fun copy(o: Any, clazz: Class<*>): Any {
        return gson.fromJson(gson.toJson(o), clazz)
    }

    fun toJson(o: Any): String? {
        return try {
            gson.toJson(o)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    fun <T> fromJson(json: String, classOfT: Class<T>): T? {
        return try {
            gson.fromJson(json, classOfT)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    fun <T> fromJson(json: String, typeOfT: Type): T? {
        return try {
            gson.fromJson(json, typeOfT)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
}
