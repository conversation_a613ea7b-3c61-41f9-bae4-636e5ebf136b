plugins {
    id("com.android.library")
//    kotlin("android")
//    kotlin("kapt")

//    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.ksp)
    alias(libs.plugins.hilt)
    alias(libs.plugins.compose.compiler)
}


android {
    // 编译 SDK 版本
//    compileSdk = BuildConfig.compileSdk
    compileSdk = libs.versions.compileSdk.get().toInt()
    namespace = "com.wordsfairy.base"

    // 资源前缀
    resourcePrefix("base")

    defaultConfig {
        // 最低支持版本
//        minSdk=BuildConfig.minSdkVersion
//        targetSdk = BuildConfig.targetSdkVersion
        minSdk = libs.versions.minSdk.get().toInt()
        targetSdk = libs.versions.targetSdk.get().toInt()
        javaCompileOptions {
            annotationProcessorOptions {
                arguments["dagger.hilt.disableModulesHaveInstallInCheck"] = "true"
            }
        }

    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = "17"
    }
}

dependencies {

    // Kotlin 支持
//    implementation(Kotlin.stdlib)
//    implementation(AndroidX.coreKtx)
//    implementation(AndroidX.DataStore.preferences)
//    implementation(AndroidX.DataStore.core)

    ksp(libs.androidx.room.compiler)
    ksp(libs.hilt.android.compiler)
    implementation(libs.androidx.core.ktx)
    implementation(libs.hilt.android)
    implementation(libs.hilt.navigation.compose)
    implementation(libs.datastore.preferences)
    implementation(libs.datastore.core)
    implementation(libs.gson)

}