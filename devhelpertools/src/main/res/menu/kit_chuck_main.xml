<?xml version="1.0" encoding="utf-8"?><!--
 ~ Copyright (C) 2017 <PERSON>.
 ~
 ~ Licensed under the Apache License, Version 2.0 (the "License");
 ~ you may not use this file except in compliance with the License.
 ~ You may obtain a copy of the License at
 ~
 ~      http://www.apache.org/licenses/LICENSE-2.0
 ~
 ~ Unless required by applicable law or agreed to in writing, software
 ~ distributed under the License is distributed on an "AS IS" BASIS,
 ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 ~ See the License for the specific language governing permissions and
 ~ limitations under the License.
 -->
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <item
        android:id="@+id/search"
        android:title="@string/kit_chuck_search"
        android:icon="@drawable/kit_chuck_ic_search_white_24dp"
        app:showAsAction="collapseActionView|ifRoom"
        app:actionViewClass="androidx.appcompat.widget.SearchView" />
    <item
        android:title="@string/kit_chuck_clear"
        android:id="@+id/clear"
        android:icon="@drawable/kit_chuck_ic_delete_white_24dp"
        app:showAsAction="always" />
    <item
        android:title="分组查询(apm/埋点...)"
        android:id="@+id/browse_sql"
        app:showAsAction="always"
        android:visible="true" />
</menu>