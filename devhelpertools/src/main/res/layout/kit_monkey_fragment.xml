<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="8dp">

    <TextView
        android:padding="5dp"
        android:gravity="center_vertical"
        android:textColor="#FFF"
        android:background="#00BCD4"
        android:id="@+id/mTips"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text=" 开发者选项 中,将 【允许通过USB调试修改权限或模拟点击】 打开,《点击查看》详细步骤"
        android:textSize="12dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <EditText
        android:inputType="number"
        android:id="@+id/mEditHour"
        android:layout_width="0dp"
        app:layout_constraintHorizontal_weight="1"
        android:layout_height="50dp"
        app:layout_constraintEnd_toStartOf="@+id/mTextHour"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mTips" />

    <TextView
        android:id="@+id/mTextHour"
        android:layout_width="0dp"
        app:layout_constraintHorizontal_weight="1"
        android:layout_height="30dp"
        android:text="时"
        android:textSize="14dp"
        android:textColor="#000"
        android:textStyle="bold"
        android:gravity="center"
        app:layout_constraintEnd_toStartOf="@+id/mEditMinute"
        app:layout_constraintStart_toEndOf="@+id/mEditHour"
        app:layout_constraintTop_toBottomOf="@+id/mTips" />

    <EditText
        android:inputType="number"
        android:id="@+id/mEditMinute"
        android:layout_width="0dp"
        app:layout_constraintHorizontal_weight="1"
        android:layout_height="50dp"
        app:layout_constraintEnd_toStartOf="@+id/mTextMinute"
        app:layout_constraintStart_toEndOf="@+id/mTextHour"
        app:layout_constraintTop_toBottomOf="@+id/mTips" />

    <TextView
        android:id="@+id/mTextMinute"
        android:layout_width="0dp"
        android:gravity="center"
        app:layout_constraintHorizontal_weight="1"
        android:layout_height="30dp"
        android:text="分"
        android:textSize="14dp"
        android:textColor="#000"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/mEditSecond"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/mEditMinute"
        app:layout_constraintTop_toBottomOf="@+id/mTips" />

    <EditText
        android:inputType="number"
        android:id="@+id/mEditSecond"
        android:layout_width="0dp"
        app:layout_constraintHorizontal_weight="1"
        android:layout_height="50dp"
        app:layout_constraintEnd_toStartOf="@+id/mTextSecond"
        app:layout_constraintStart_toEndOf="@+id/mTextMinute"
        app:layout_constraintTop_toBottomOf="@+id/mTips" />

    <TextView
        android:id="@+id/mTextSecond"
        android:layout_width="0dp"
        app:layout_constraintHorizontal_weight="1"
        android:layout_height="30dp"
        android:text="秒"
        android:textSize="14dp"
        android:textColor="#000"
        android:textStyle="bold"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/mEditSecond"
        app:layout_constraintTop_toBottomOf="@+id/mTips" />


    <TextView
        android:id="@+id/mStart"
        android:background="#337CC4"
        android:layout_marginTop="20dp"
        android:text="启动测试"
        android:textSize="14dp"
        android:textColor="#FFF"
        android:padding="5dp"
        android:gravity="center"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mTextSecond"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

</androidx.constraintlayout.widget.ConstraintLayout>
