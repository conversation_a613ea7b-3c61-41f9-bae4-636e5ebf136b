<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:paddingBottom="5dp"
    android:paddingLeft="10dp"
    android:paddingRight="10dp"
    android:paddingTop="5dp"
    >

    <TextView
        android:id="@+id/name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#ccc"
        android:textSize="@dimen/kit_info_text_size"
        tools:text="text"
        />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:gravity="center_vertical|right"
        >

        <View
            android:id="@+id/color"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_marginRight="5dp"
            />

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@null"
            android:textColor="#fff"
            android:textSize="@dimen/kit_info_text_size"
            tools:text="112222"
            tools:textColor="#999"
            />


    </LinearLayout>

</LinearLayout>