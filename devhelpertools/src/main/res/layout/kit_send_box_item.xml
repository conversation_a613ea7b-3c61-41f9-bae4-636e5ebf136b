<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="100dip"
    android:orientation="vertical"
    android:paddingLeft="10dip"
    android:paddingRight="10dip">

    <LinearLayout
        android:id="@+id/mRootView"
        android:layout_width="match_parent"
        android:layout_height="0dip"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/mIcon"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:scaleType="center"
            android:src="@drawable/kit_dk_dir_icon" />

        <TextView
            android:id="@+id/mContent"
            android:layout_width="0dip"
            android:layout_height="match_parent"
            android:layout_marginLeft="10dip"
            android:layout_weight="1"
            android:gravity="center_vertical" />

        <ImageView
            android:id="@+id/mMore"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/kit_dk_more_icon" />
    </LinearLayout>
</LinearLayout>
