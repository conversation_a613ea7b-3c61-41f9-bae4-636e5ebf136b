<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <SurfaceView
            android:id="@+id/scanner_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center" />

        <com.twf.develophelpertools.zxing.view.ViewfinderView
            android:id="@+id/viewfinder_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:dkCornerColor="#337CC4"
            app:dkFrameColor="#90FFFFFF"
            app:dkLabelText=""
            app:dkLabelTextColor="#333333"
            app:dkLaserColor="#337CC4"
            app:dkMaskColor="#60000000"
            app:dkResultColor="#000000"
            app:dkResultPointColor="#60000000" />

    </FrameLayout>

</LinearLayout>