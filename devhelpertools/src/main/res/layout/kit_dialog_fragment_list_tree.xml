<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#80000000">

    <com.twf.develophelpertools.view.RegionView
            android:id="@+id/region"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

    <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/container"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_centerInParent="true"/>

    <LinearLayout
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#B0FF0000"
            android:layout_marginBottom="48dp"
            android:gravity="center"
            android:padding="5dp">

        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="13dp"
                android:textColor="#FFF"
                android:text="点击带下划线的 fragment 会选中对应布局区域"/>

        <androidx.appcompat.widget.AppCompatCheckBox
                android:id="@+id/checkbox"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="16dp"
                android:textColor="#FFF"
                android:text="显示完整包名"/>

    </LinearLayout>

</RelativeLayout>