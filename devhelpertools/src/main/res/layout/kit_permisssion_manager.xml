<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:gravity="center"
        android:id="@+id/kit_textview3"
        android:layout_width="match_parent"
        android:text="点击可以申请权限"
        android:padding="10dp"
        android:textSize="20dp"
        android:textColor="#337CC4"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:textStyle="bold"
        android:padding="8dp"
        android:id="@+id/mImportantPermission"
        app:layout_constraintTop_toBottomOf="@+id/kit_textview3"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        tools:text="通知栏权限"
        android:textSize="15dp"
        android:textColor="#ff0000"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <ListView
        android:id="@+id/mPermissionList"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:cacheColorHint="#000000"
        android:fadingEdge="none"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mImportantPermission" />

</androidx.constraintlayout.widget.ConstraintLayout>