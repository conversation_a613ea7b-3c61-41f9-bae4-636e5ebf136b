<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dip"
        android:layout_marginRight="10dip"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:paddingBottom="20dip"
            android:paddingTop="20dip"
            android:text="模拟弱网络开关" />

        <CheckBox
            android:id="@+id/mCheckView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/mTabView"
        android:layout_width="match_parent"
        android:layout_height="40dip"
        android:layout_marginLeft="10dip"
        android:layout_marginRight="10dip"
        android:orientation="horizontal"
        android:visibility="gone">

        <RadioButton
            android:id="@+id/mInterceptNet"
            style="@style/kit_check_box_left"
            android:layout_width="0dip"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="断网" />

        <RadioButton
            android:id="@+id/mTimeOut"
            style="@style/kit_check_box_center"
            android:layout_width="0dip"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="超时" />

        <RadioButton
            android:id="@+id/mLimitSpeed"
            style="@style/kit_check_box_right"
            android:layout_width="0dip"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="限速" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/mContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="10dip"
        android:layout_marginRight="10dip"
        android:orientation="vertical" />
</LinearLayout>
