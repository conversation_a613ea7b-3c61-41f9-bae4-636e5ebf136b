<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="100dp"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:background="#4D337CC4"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:fresco="http://schemas.android.com/apk/res-auto">

    <TextView
        fresco:layout_constraintTop_toTopOf="parent"
        fresco:layout_constraintRight_toRightOf="parent"
        fresco:layout_constraintLeft_toLeftOf="parent"
        android:textSize="10dp"
        android:textStyle="bold"
        tools:text="视频"
        android:layout_marginTop="5dp"
        android:gravity="center"
        android:textColor="#FFFFFF"
        android:id="@+id/mPlayView"
        android:layout_width="match_parent"
        android:layout_height="15dp" />


    <com.facebook.drawee.view.SimpleDraweeView
        android:id="@+id/mImageView"
        android:layout_marginTop="10dp"
        fresco:layout_constraintTop_toBottomOf="@+id/mPlayView"
        fresco:layout_constraintLeft_toLeftOf="parent"
        fresco:layout_constraintRight_toRightOf="parent"
        fresco:roundedCornerRadius="10dp"
        tools:background="@drawable/kit_dk_cpu"
        android:layout_width="60dp"
        android:layout_height="60dp" />

    <View
        android:id="@+id/kit_view"
        android:layout_width="wrap_content"
        android:layout_height="10dp"
        fresco:layout_constraintBottom_toBottomOf="parent"
        fresco:layout_constraintLeft_toLeftOf="parent"
        fresco:layout_constraintTop_toBottomOf="@+id/mImageView" />


    <ImageView
        android:id="@+id/mDelete"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginEnd="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="48dp"
        android:contentDescription="TODO"
        android:src="@drawable/kit_delete_icon"
        fresco:layout_constraintBottom_toTopOf="@+id/kit_view"
        fresco:layout_constraintEnd_toEndOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
