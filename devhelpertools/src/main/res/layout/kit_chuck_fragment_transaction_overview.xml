<?xml version="1.0" encoding="utf-8"?><!--
 ~ Copyright (C) 2017 <PERSON>.
 ~
 ~ Licensed under the Apache License, Version 2.0 (the "License");
 ~ you may not use this file except in compliance with the License.
 ~ You may obtain a copy of the License at
 ~
 ~      http://www.apache.org/licenses/LICENSE-2.0
 ~
 ~ Unless required by applicable law or agreed to in writing, software
 ~ distributed under the License is distributed on an "AS IS" BASIS,
 ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 ~ See the License for the specific language governing permissions and
 ~ limitations under the License.
 -->
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFF"
    android:scrollbars="vertical">

    <TableLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:stretchColumns="1">

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                style="@style/Chuck.TextAppearance.Label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/kit_chuck_url" />

            <TextView
                android:id="@+id/url"
                style="@style/Chuck.TextAppearance.Value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="https://example.com/path/to/resource?here=might_be_really_long" />

        </TableRow>

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                style="@style/Chuck.TextAppearance.Label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/kit_chuck_params" />

            <TextView
                android:id="@+id/params"
                style="@style/Chuck.TextAppearance.Value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="参数" />

        </TableRow>

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                style="@style/Chuck.TextAppearance.Label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/kit_chuck_method" />

            <TextView
                android:id="@+id/method"
                style="@style/Chuck.TextAppearance.Value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="GET" />

        </TableRow>

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                style="@style/Chuck.TextAppearance.Label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/kit_chuck_protocol" />

            <TextView
                android:id="@+id/protocol"
                style="@style/Chuck.TextAppearance.Value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="HTTP/1.1" />

        </TableRow>

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                style="@style/Chuck.TextAppearance.Label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/kit_chuck_status" />

            <TextView
                android:id="@+id/status"
                style="@style/Chuck.TextAppearance.Value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="Complete" />

        </TableRow>

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                style="@style/Chuck.TextAppearance.Label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/kit_chuck_response" />

            <TextView
                android:id="@+id/response"
                style="@style/Chuck.TextAppearance.Value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="200 OK" />

        </TableRow>

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                style="@style/Chuck.TextAppearance.Label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/kit_chuck_ssl" />

            <TextView
                android:id="@+id/ssl"
                style="@style/Chuck.TextAppearance.Value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="Yes" />

        </TableRow>

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="8dp">

            <TextView
                style="@style/Chuck.TextAppearance.Label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/kit_chuck_request_time" />

            <TextView
                android:id="@+id/request_time"
                style="@style/Chuck.TextAppearance.Value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="05/02/17 11:52:49" />

        </TableRow>

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                style="@style/Chuck.TextAppearance.Label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/kit_chuck_response_time" />

            <TextView
                android:id="@+id/response_time"
                style="@style/Chuck.TextAppearance.Value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="05/02/17 11:52:49" />

        </TableRow>

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                style="@style/Chuck.TextAppearance.Label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/kit_chuck_duration" />

            <TextView
                android:id="@+id/duration"
                style="@style/Chuck.TextAppearance.Value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="405 ms" />

        </TableRow>

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="8dp">

            <TextView
                style="@style/Chuck.TextAppearance.Label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/kit_chuck_request_size" />

            <TextView
                android:id="@+id/request_size"
                style="@style/Chuck.TextAppearance.Value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="53 KB" />

        </TableRow>

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                style="@style/Chuck.TextAppearance.Label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/kit_chuck_response_size" />

            <TextView
                android:id="@+id/response_size"
                style="@style/Chuck.TextAppearance.Value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="148 KB" />

        </TableRow>

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                style="@style/Chuck.TextAppearance.Label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/kit_chuck_total_size" />

            <TextView
                android:id="@+id/total_size"
                style="@style/Chuck.TextAppearance.Value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="201 KB" />

        </TableRow>

    </TableLayout>

</androidx.core.widget.NestedScrollView>
