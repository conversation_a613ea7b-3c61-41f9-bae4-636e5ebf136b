<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/mTitleView"
        android:background="#00BCD4"
        android:gravity="center"
        android:textColor="#FFFFFF"
        android:text="文件预览"
        android:textSize="18dp"
        android:layout_width="match_parent"
        android:layout_height="50dp" />


    <androidx.viewpager.widget.ViewPager
        android:layout_above="@+id/mButtomLayout"
        android:layout_below="@+id/mTitleView"
        android:id="@+id/mViewPager"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:id="@+id/mButtomLayout"
        android:layout_alignParentBottom="true"
        android:background="#00BCD4"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/mBack"
            android:layout_margin="10dp"
            android:background="@drawable/bg_white_corner_divider"
            android:paddingRight="10dp"
            android:paddingLeft="10dp"
            android:paddingTop="5dp"
            android:paddingBottom="5dp"
            android:layout_alignParentBottom="true"
            android:text="返回"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="1dp" />

        <TextView
            android:id="@+id/mPlayVideo"
            android:layout_margin="10dp"
            android:background="@drawable/bg_white_corner_divider"
            android:paddingRight="10dp"
            android:paddingLeft="10dp"
            android:paddingTop="5dp"
            android:paddingBottom="5dp"
            android:layout_alignParentBottom="true"
            android:text="播放视频"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
    </LinearLayout>

</RelativeLayout>
