<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="HardcodedText,SpUsage"
    android:orientation="vertical"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clipToPadding="false"
        android:layout_weight="1"
        android:fillViewport="true">

        <LinearLayout
            android:paddingTop="20dp"
            android:paddingBottom="20dp"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/kit_linearlayout"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/kit_guideline"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.167" />

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/kit_guideline2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.5" />

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/kit_guideline3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.833" />

                <!-- 开发工具（跳转二级页） -->

                <TextView
                    android:layout_marginLeft="20dp"
                    android:id="@+id/kit_textview4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="开发工具"
                    android:textColor="#000"
                    android:textSize="20dp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:layout_marginStart="20dp" />

                <TextView
                    android:layout_marginTop="20dp"
                    android:id="@+id/mAppInfo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableTop="@drawable/kit_dk_sys_info"
                    android:gravity="center"
                    android:drawablePadding="5dip"
                    android:text="App基本信息"
                    android:textColor="#000"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="@+id/kit_guideline"
                    app:layout_constraintStart_toStartOf="@+id/kit_guideline"
                    app:layout_constraintTop_toBottomOf="@+id/kit_textview4" />

                <TextView
                    android:id="@+id/mLocalFile"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableTop="@drawable/kit_dk_file_explorer"
                    android:drawablePadding="5dip"
                    android:gravity="center"
                    android:text="文件预览"
                    android:textColor="#000"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="@+id/kit_guideline2"
                    app:layout_constraintStart_toStartOf="@+id/kit_guideline2"
                    app:layout_constraintBottom_toBottomOf="@+id/mAppInfo"
                    app:layout_constraintTop_toTopOf="@+id/mAppInfo" />

                <TextView
                    android:id="@+id/mH5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableTop="@drawable/kit_dk_web_door"
                    android:drawablePadding="5dip"
                    android:gravity="center"
                    android:text="H5任意门"
                    android:textColor="#000"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="@+id/kit_guideline3"
                    app:layout_constraintStart_toStartOf="@+id/kit_guideline3"
                    app:layout_constraintBottom_toBottomOf="@+id/mLocalFile"
                    app:layout_constraintTop_toTopOf="@+id/mLocalFile" />

                <TextView
                    android:layout_marginTop="20dp"
                    android:id="@+id/mCrash"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableTop="@drawable/kit_dk_crash_catch"
                    android:drawablePadding="5dip"
                    android:gravity="center"
                    android:text="Crash查看"
                    android:textColor="#000"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="@+id/kit_guideline"
                    app:layout_constraintStart_toStartOf="@+id/kit_guideline"
                    app:layout_constraintTop_toBottomOf="@+id/mAppInfo" />

                <TextView
                    android:id="@+id/mLogcat"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableTop="@drawable/kit_dk_log_info"
                    android:drawablePadding="5dip"
                    android:gravity="center"
                    android:text="日志查看"
                    android:textColor="#000"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="@+id/kit_guideline2"
                    app:layout_constraintStart_toStartOf="@+id/kit_guideline2"
                    app:layout_constraintBottom_toBottomOf="@+id/mCrash"
                    app:layout_constraintTop_toTopOf="@+id/mCrash" />

                <TextView
                    android:id="@+id/mWeakConnect"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableTop="@drawable/kit_dk_weak_network"
                    android:drawablePadding="5dip"
                    android:gravity="center"
                    android:text="模拟弱网"
                    android:textColor="#000"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="@+id/kit_guideline3"
                    app:layout_constraintStart_toStartOf="@+id/kit_guideline3"
                    app:layout_constraintBottom_toBottomOf="@+id/mLogcat"
                    app:layout_constraintTop_toTopOf="@+id/mLogcat" />

                <TextView
                    android:layout_marginTop="20dp"
                    android:id="@+id/mMock"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableTop="@drawable/kit_dk_log_info"
                    android:drawablePadding="5dip"
                    android:gravity="center"
                    android:text="研发mock"
                    android:textColor="#000"
                    android:textSize="14dp"
                    app:layout_constraintTop_toBottomOf="@+id/mCrash"
                    app:layout_constraintEnd_toEndOf="@+id/kit_guideline"
                    app:layout_constraintStart_toStartOf="@+id/kit_guideline" />

                <TextView
                    android:id="@+id/mAnalytise"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableTop="@drawable/kit_dk_log_info"
                    android:drawablePadding="5dip"
                    android:gravity="center"
                    android:text="打点统计"
                    android:textColor="#000"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toStartOf="@+id/kit_guideline2"
                    app:layout_constraintStart_toStartOf="@+id/kit_guideline2"
                    app:layout_constraintBottom_toBottomOf="@+id/mMock"
                    app:layout_constraintTop_toTopOf="@+id/mMock" />

                <TextView
                    android:id="@+id/mQaMock"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableTop="@drawable/kit_dk_log_info"
                    android:drawablePadding="5dip"
                    android:gravity="center"
                    android:text="mock点击"
                    android:textColor="#000"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="@+id/kit_guideline3"
                    app:layout_constraintStart_toStartOf="@+id/kit_guideline3"
                    app:layout_constraintBottom_toBottomOf="@+id/mAnalytise"
                    app:layout_constraintTop_toTopOf="@+id/mAnalytise" />

                <TextView
                    android:layout_marginTop="20dp"
                    android:id="@+id/mMiniProgram"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableTop="@drawable/kit_dk_log_info"
                    android:drawablePadding="5dip"
                    android:gravity="center"
                    android:text="小程序参数"
                    android:textColor="#000"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="@+id/kit_guideline"
                    app:layout_constraintStart_toStartOf="@+id/kit_guideline"
                    app:layout_constraintTop_toBottomOf="@+id/mMock" />

                <TextView
                    android:id="@+id/mOpenDebugChat"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableTop="@drawable/kit_dk_log_info"
                    android:drawablePadding="5dip"
                    android:gravity="center"
                    android:text="聊天卡片"
                    android:textColor="#000"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toStartOf="@+id/kit_guideline2"
                    app:layout_constraintStart_toStartOf="@+id/kit_guideline2"
                    app:layout_constraintBottom_toBottomOf="@+id/mMiniProgram"
                    app:layout_constraintTop_toTopOf="@+id/mMiniProgram" />

                <TextView
                    android:id="@+id/mSwitchEnvironment"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableTop="@drawable/kit_dk_log_info"
                    android:drawablePadding="5dip"
                    android:gravity="center"
                    android:text="切换环境"
                    android:textColor="#000"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="@+id/kit_guideline3"
                    app:layout_constraintStart_toStartOf="@+id/kit_guideline3"
                    app:layout_constraintBottom_toBottomOf="@+id/mOpenDebugChat"
                    app:layout_constraintTop_toTopOf="@+id/mOpenDebugChat" />

                <TextView
                    android:id="@+id/mTargetActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableTop="@drawable/kit_dk_log_info"
                    android:drawablePadding="5dip"
                    android:gravity="center"
                    android:text="跳转指定页面"
                    app:layout_constraintStart_toStartOf="@+id/kit_guideline"
                    app:layout_constraintEnd_toEndOf="@+id/kit_guideline"
                    android:textColor="#000"
                    android:textSize="14dp"
                    app:layout_constraintTop_toBottomOf="@+id/mMiniProgram"
                    android:layout_marginTop="20dp" />

                <TextView
                    android:id="@+id/mJiraManager"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableTop="@drawable/kit_dk_log_info"
                    android:drawablePadding="5dip"
                    android:gravity="center"
                    android:text="Issue管理"
                    android:textColor="#000"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toStartOf="@+id/kit_guideline2"
                    app:layout_constraintStart_toStartOf="@+id/kit_guideline2"
                    app:layout_constraintBottom_toBottomOf="@+id/mTargetActivity"
                    app:layout_constraintTop_toTopOf="@+id/mTargetActivity" />

                <TextView
                    android:id="@+id/apmSettings"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:layout_marginRight="8dp"
                    android:drawablePadding="5dip"
                    android:drawableTop="@drawable/kit_dk_log_info"
                    android:gravity="center"
                    android:text="性能监控"
                    android:textColor="#000"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toStartOf="@+id/kit_guideline3"
                    app:layout_constraintStart_toStartOf="@+id/kit_guideline3"
                    app:layout_constraintBottom_toBottomOf="@+id/mJiraManager"
                    app:layout_constraintTop_toTopOf="@+id/mJiraManager" />

                <!-- 开发工具（开关选框） -->

                <TextView
                    android:id="@+id/mpermissionManager"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="8dp"
                    android:layout_marginRight="8dp"
                    android:drawableTop="@drawable/kit_dk_log_info"
                    android:drawablePadding="5dip"
                    android:gravity="center"
                    android:text="权限统计"
                    android:textColor="#000"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toStartOf="@+id/kit_guideline"
                    app:layout_constraintStart_toStartOf="@+id/kit_guideline"
                    app:layout_constraintTop_toBottomOf="@+id/mTargetActivity" />


                <TextView
                    android:id="@+id/mLibrary"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableTop="@drawable/kit_dk_log_info"
                    android:drawablePadding="5dip"
                    android:gravity="center"
                    android:text="三方库信息"
                    android:textColor="#000"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toStartOf="@+id/kit_guideline2"
                    app:layout_constraintStart_toStartOf="@+id/kit_guideline2"
                    app:layout_constraintBottom_toBottomOf="@+id/mpermissionManager"
                    app:layout_constraintTop_toTopOf="@+id/mpermissionManager" />
                <LinearLayout
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:id="@+id/checkBoxLayout"
                    app:layout_constraintTop_toBottomOf="@+id/mpermissionManager"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:orientation="horizontal"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:orientation="vertical"
                        android:layout_weight="1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content">

                        <CheckBox
                            android:layout_marginTop="30dp"
                            android:id="@+id/mApiReport"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="接口上报"
                            android:textColor="#000"
                            android:textSize="14dp"
                            app:layout_constraintStart_toStartOf="@+id/kit_guideline4"
                            app:layout_constraintTop_toBottomOf="@+id/mTargetActivity" />

                        <CheckBox
                            android:id="@+id/mTopPageInfo"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="页面信息"
                            android:textColor="#000"
                            android:textSize="14dp"
                            app:layout_constraintStart_toStartOf="@+id/kit_guideline6"
                            app:layout_constraintBottom_toBottomOf="@+id/apmSettings"
                            app:layout_constraintTop_toTopOf="@+id/apmSettings" />

                        <CheckBox
                            android:id="@+id/mMockLocation"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="模拟位置"
                            android:textColor="#000"
                            android:textSize="14dp"
                            app:layout_constraintStart_toStartOf="@+id/kit_guideline4"
                            app:layout_constraintTop_toBottomOf="@+id/mApiReport" />

                        <CheckBox
                            android:id="@+id/mApiErorToast"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="接口异常 Toast"
                            android:textColor="#000"
                            android:textSize="14dp"
                            app:layout_constraintTop_toTopOf="@+id/mMockLocation"
                            app:layout_constraintBottom_toBottomOf="@+id/mMockLocation"
                            app:layout_constraintStart_toStartOf="@+id/kit_guideline5" />

                        <CheckBox
                            android:id="@+id/kit_textview"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="false"
                            android:text="H5 页面提示"
                            app:layout_constraintStart_toStartOf="@+id/kit_guideline6"
                            app:layout_constraintBottom_toBottomOf="@+id/mApiErorToast"
                            app:layout_constraintTop_toTopOf="@+id/mApiErorToast" />

                        <CheckBox
                            android:id="@+id/mTextUserId"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="TestUserId"
                            android:textColor="#000"
                            android:textSize="14dp"
                            app:layout_constraintStart_toStartOf="@+id/kit_guideline4"
                            app:layout_constraintTop_toBottomOf="@+id/mMockLocation" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_weight="1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content">

                        <CheckBox
                            android:id="@+id/mJumpAd"
                            android:layout_marginTop="30dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="跳过广告"
                            android:textColor="#000"
                            android:textSize="14dp"
                            app:layout_constraintStart_toStartOf="@+id/kit_guideline4"
                            app:layout_constraintTop_toBottomOf="@+id/mTargetActivity" />
                    </LinearLayout>
                </LinearLayout>

                <!-- 视觉工具 -->

                <TextView
                    android:id="@+id/kit_textview17"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="40dp"
                    android:text="视觉工具"
                    android:layout_marginLeft="20dp"
                    android:textColor="#000"
                    android:textSize="20dip"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/checkBoxLayout"
                    android:layout_marginStart="20dp" />

                <TextView
                    android:layout_marginTop="20dp"
                    android:id="@+id/mColorFilter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableTop="@drawable/kit_dk_color_picker"
                    android:drawablePadding="5dip"
                    android:gravity="center"
                    android:text="取色器"
                    android:textColor="#000"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toStartOf="@+id/kit_guideline"
                    app:layout_constraintStart_toStartOf="@+id/kit_guideline"
                    app:layout_constraintTop_toBottomOf="@+id/kit_textview17" />

                <TextView
                    android:id="@+id/mRulerLine"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableTop="@drawable/kit_dk_align_ruler"
                    android:drawablePadding="5dip"
                    android:gravity="center"
                    android:text="对齐标尺"
                    android:textColor="#000"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="@+id/kit_guideline2"
                    app:layout_constraintStart_toStartOf="@+id/kit_guideline2"
                    app:layout_constraintBottom_toBottomOf="@+id/mColorFilter"
                    app:layout_constraintTop_toTopOf="@+id/mColorFilter" />

                <TextView
                    android:id="@+id/mLayoutBorder"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableTop="@drawable/kit_dk_view_border"
                    android:drawablePadding="5dip"
                    android:gravity="center"
                    android:text="布局边框"
                    android:textColor="#000"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="@+id/kit_guideline3"
                    app:layout_constraintStart_toStartOf="@+id/kit_guideline3"
                    app:layout_constraintBottom_toBottomOf="@+id/mRulerLine"
                    app:layout_constraintTop_toTopOf="@+id/mRulerLine" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

    </ScrollView>

    <TextView
        android:id="@+id/mExit"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:background="#222222"
        android:gravity="center"
        android:text="退出"
        android:layout_margin="20dp"
        android:textColor="#FFFFFF"
        android:textSize="15dip"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</LinearLayout>