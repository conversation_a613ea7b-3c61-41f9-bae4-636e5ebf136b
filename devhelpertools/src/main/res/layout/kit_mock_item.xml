<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:descendantFocusability="afterDescendants"
    android:clickable="true"
    android:background="#FFFFFF"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/mUrl"
        android:layout_width="0dip"
        android:layout_height="50dip"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:paddingLeft="10dip"
        android:textColor="#000000"
        android:textSize="15dp" />

    <CheckBox
        android:id="@+id/mCheckView"
        android:layout_width="40dip"
        android:layout_height="40dip" />
</LinearLayout>
