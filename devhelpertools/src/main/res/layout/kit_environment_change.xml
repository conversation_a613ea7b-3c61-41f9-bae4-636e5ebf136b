<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/activity_custom_config"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFFFFF"
    android:paddingLeft="20dp"
    android:paddingRight="20dp">

    <LinearLayout
        android:id="@+id/bottom_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="20dp"
        android:orientation="vertical">

        <Button
            android:id="@+id/bt_reset"
            android:layout_width="match_parent"
            android:layout_height="55dp"
            android:gravity="center"
            android:padding="15dp"
            android:text="重置"
            android:textColor="#337CC4"
            android:textSize="18sp" />

        <Button
            android:id="@+id/bt_save"
            android:layout_width="match_parent"
            android:layout_height="55dp"
            android:gravity="center"
            android:padding="15dp"
            android:text="保存"
            android:textColor="#337CC4"
            android:textSize="18sp" />

    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/bottom_layout"
        android:layout_alignParentStart="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="当前api环境: "
                        android:textColor="#303030"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_current_api_host"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="www.baidu.com" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="当前聊天环境: "
                        android:textColor="#303030"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_current_chat_host"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="www.baidu.com" />
                </LinearLayout>

            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:orientation="vertical">

                <CheckBox
                    android:id="@+id/cb_buildin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    android:text="内置环境"
                    android:textColor="#303030"
                    android:textSize="14sp" />

                <Spinner
                    android:id="@+id/spinner_buildin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#303030"
                    android:textSize="14sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="api环境: "
                        android:textColor="#303030"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_buildin_api_host"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="www.baidu.com" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="聊天环境: "
                        android:textColor="#303030"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_buildin_chat_host"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="www.baidu.com" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="zp_tag: "
                        android:textColor="#303030"
                        android:textSize="14sp" />

                    <EditText
                        android:id="@+id/et_zp_tag"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="#FFFFFF"
                        android:hint="请输入内容"
                        android:paddingLeft="5dp"
                        android:textSize="14dp" />

                    <Button
                        android:id="@+id/tv_zp_tag_save"
                        android:layout_width="50dp"
                        android:layout_height="match_parent"
                        android:background="#808080"
                        android:gravity="center"
                        android:text="保存" />
                </LinearLayout>

            </LinearLayout>


            <!--<LinearLayout-->
            <!--android:layout_width="match_parent"-->
            <!--android:layout_height="wrap_content"-->
            <!--android:layout_marginTop="20dp"-->
            <!--android:orientation="vertical">-->

            <!--<CheckBox-->
            <!--android:id="@+id/cb_custom"-->
            <!--android:layout_width="wrap_content"-->
            <!--android:layout_height="wrap_content"-->
            <!--android:text="自定义环境(未实现)"-->
            <!--android:textColor="#303030"-->
            <!--android:textSize="14sp" />-->

            <!--<TextView-->
            <!--android:layout_width="wrap_content"-->
            <!--android:layout_height="wrap_content"-->
            <!--android:text="api环境: (未实现)"-->
            <!--android:textColor="#303030"-->
            <!--android:textSize="14sp" />-->

            <!--<EditText-->
            <!--android:id="@+id/et_custom_api_host"-->
            <!--android:layout_width="match_parent"-->
            <!--android:layout_height="wrap_content"-->
            <!--android:hint="请输入api环境地址(未实现)"-->
            <!--android:maxLines="1"-->
            <!--android:textSize="14sp" />-->

            <!--<TextView-->
            <!--android:layout_width="wrap_content"-->
            <!--android:layout_height="wrap_content"-->
            <!--android:layout_marginTop="10dp"-->
            <!--android:text="聊天环境: (未实现)"-->
            <!--android:textColor="#303030"-->
            <!--android:textSize="14sp" />-->

            <!--<EditText-->
            <!--android:id="@+id/et_custom_chat_host"-->
            <!--android:layout_width="match_parent"-->
            <!--android:layout_height="wrap_content"-->
            <!--android:hint="请输入聊天环境地址(未实现)"-->
            <!--android:maxLines="1"-->
            <!--android:textSize="14sp" />-->
            <!--</LinearLayout>-->

        </LinearLayout>

    </ScrollView>


</RelativeLayout>
