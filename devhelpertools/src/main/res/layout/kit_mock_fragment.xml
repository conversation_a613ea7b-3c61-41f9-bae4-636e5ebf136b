<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_margin="5dp"
        android:gravity="center"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/mSelectAll"
            android:textColor="#FFFFFF"
            android:gravity="center"
            android:layout_weight="1"
            android:text="全部选择"
            android:layout_width="0dp"
            android:layout_height="30dp" />

        <TextView
            android:id="@+id/mCacnelAll"
            android:textColor="#FFFFFF"
            android:layout_marginLeft="5dp"
            android:gravity="center"
            android:layout_weight="1"
            android:text="全部取消"
            android:layout_width="0dp"
            android:layout_height="30dp" />
    </LinearLayout>

    <EditText
        android:id="@+id/mEditText"
        android:layout_width="match_parent"
        android:layout_height="50dip"
        android:background="#FFFFFF"
        android:hint="搜索接口"
        android:paddingLeft="10dip"
        android:textSize="15dp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.3dip"
        android:background="#000000" />

    <ListView
        android:id="@+id/mListView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:cacheColorHint="#000000" />
</LinearLayout>
