<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="10dip">

    <EditText
        android:id="@+id/mInput"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FFFFFF"
        android:gravity="top"
        android:hint="输入网址,点击按钮跳转"
        android:minHeight="200dip"
        android:padding="10dip" />

    <TextView
        android:id="@+id/mJump_scan"
        android:layout_width="match_parent"
        android:layout_height="40dip"
        android:background="#0000ff"
        android:gravity="center"
        android:text="二维码扫描"
        android:textColor="#FFFFFF" />

    <TextView
        android:layout_marginTop="10dp"
        android:id="@+id/mJump"
        android:layout_width="match_parent"
        android:layout_height="40dip"
        android:background="#0000ff"
        android:gravity="center"
        android:text="点击跳转"
        android:textColor="#FFFFFF" />
    <TextView
        android:id="@+id/mClear"
        android:padding="5dip"
        android:gravity="center"
        android:text="清除历史数据"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />
    <ListView
        android:id="@+id/mHistory"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:cacheColorHint="#000000" />
</LinearLayout>
