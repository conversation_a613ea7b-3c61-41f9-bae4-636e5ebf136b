<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingTop="10dip">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="超时时间："
        android:textColor="#000000"
        android:textSize="15dp" />

    <EditText
        android:id="@+id/mTimeOut"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dip"
        android:layout_marginRight="10dip"
        android:background="#FFFFFF"
        android:hint="2000"
        android:inputType="number"
        android:textColor="#0000ff"
        android:textSize="15dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="ms"
        android:textColor="#000000"
        android:textSize="15dp" />
</LinearLayout>
