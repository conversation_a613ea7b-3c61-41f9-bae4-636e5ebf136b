<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">


    <TextView
        android:background="#37C2BC"
        android:id="@+id/mTitleView"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:text="ISSUE管理"
        android:textColor="#FFFFFF"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <CheckBox
        android:id="@+id/mCheckBox"
        android:textColor="#FFFFFF"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="@+id/mTitleView"
        app:layout_constraintTop_toTopOf="@+id/mTitleView"
        android:text="开启涂鸦"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <ListView
        android:scrollbars="none"
        android:id="@+id/mRecycleView"
        tools:listitem="@layout/kit_jira_manager_item"
        tools:itemCount="10"
        android:divider="#000000"
        android:dividerHeight="1dp"
        android:fadingEdge="none"
        android:cacheColorHint="#000000"
        android:layout_marginRight="20dp"
        android:layout_marginLeft="20dp"
        app:layout_constraintBottom_toTopOf="@+id/mCreateRecord"
        app:layout_constraintTop_toBottomOf="@+id/mTitleView"
        android:layout_width="match_parent"
        android:layout_height="0dp" />

    <TextView
        android:layout_marginBottom="20dp"
        android:id="@+id/mCreateRecord"
        android:layout_marginRight="20dp"
        android:layout_marginLeft="20dp"
        android:textStyle="bold"
        android:textColor="#FFFFFF"
        android:gravity="center"
        android:text="创建记录"
        android:textSize="15dp"
        android:background="#34CC5F"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_width="match_parent"
        android:layout_height="50dp" />

</androidx.constraintlayout.widget.ConstraintLayout>
