<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dip"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="请求限速："
            android:textColor="#000000"
            android:textSize="15dp" />

        <EditText
            android:inputType="number"
            android:id="@+id/mRequestLimit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="#FFFFFF"
            android:hint="1"
            android:paddingLeft="10dip"
            android:paddingRight="10dip"
            android:textColor="#0000ff"
            android:textSize="15dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="K/s"
            android:textColor="#000000"
            android:textSize="15dp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dip"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="响应限速："
            android:textColor="#FFFFFF"
            android:textSize="15dp" />

        <EditText
            android:id="@+id/mResponseLimit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="#FFFFFF"
            android:hint="1"
            android:inputType="number"
            android:paddingLeft="10dip"
            android:paddingRight="10dip"
            android:textColor="#0000ff"
            android:textSize="15dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="K/s"
            android:textColor="#000000"
            android:textSize="15dp" />
    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dip"
        android:text="请求限速会在上传时限速,响应限速会在下载时限速,0则不限速"
        android:textSize="14dp" />
</LinearLayout>
