<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#FFFFFF"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="100dip"
        android:layout_height="80dip"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tag"
            android:layout_width="match_parent"
            android:layout_height="0dip"
            android:layout_weight="1"
            android:gravity="center"
            android:textSize="15dp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dip"
            android:background="#ff0000" />

        <TextView
            android:id="@+id/time"
            android:layout_width="match_parent"
            android:layout_height="0dip"
            android:layout_weight="1"
            android:gravity="center"
            android:textSize="15dp" />
    </LinearLayout>

    <View
        android:layout_width="1dip"
        android:layout_height="match_parent"
        android:background="#ff0000" />

    <LinearLayout
        android:layout_width="50dip"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="15dp" />

        <TextView
            android:id="@+id/pId"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="15dp" />
    </LinearLayout>

    <View
        android:layout_width="1dip"
        android:layout_height="match_parent"
        android:background="#ff0000" />

    <TextView
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textSize="15dp" />
</LinearLayout>
