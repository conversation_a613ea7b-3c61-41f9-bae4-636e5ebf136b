<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#FFFFFF"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:background="#37C2BC"
        android:id="@+id/mTitleView"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:text="创建ISSUE"
        android:textColor="#FFFFFF"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:scrollbars="none"
        android:fillViewport="true"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        app:layout_constraintBottom_toTopOf="@+id/kit_textview2"
        app:layout_constraintTop_toBottomOf="@+id/mTitleView">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:orientation="vertical">

            <EditText
                android:gravity="top"
                android:textSize="10dp"
                android:id="@+id/mEditTextTitle"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="10dp"
                android:background="@drawable/bg_edittext_selector"
                android:hint="输入标题"
                android:padding="5dp"
                app:layout_constraintTop_toTopOf="parent" />

            <EditText
                android:textSize="11dp"
                android:id="@+id/mEditTextDesc"
                android:layout_width="match_parent"
                android:layout_height="100dp"
                android:layout_marginTop="20dp"
                android:background="@drawable/bg_edittext_selector"
                android:gravity="top"
                android:hint="输入描述"
                android:padding="5dp"
                app:layout_constraintTop_toBottomOf="@+id/mEditTextTitle" />

            <TextView
                android:id="@+id/mDeveGroupText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginRight="20dp"
                android:text="研发同学"
                android:textColor="#000"
                android:textSize="15dp"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/mEditTextDesc" />

            <ImageView
                android:id="@+id/mAddDevMate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@android:drawable/ic_menu_add"
                app:layout_constraintBottom_toBottomOf="@+id/mDeveGroupText"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/mDeveGroupText" />

            <com.twf.develophelpertools.view.DevMateExpandGridView
                android:id="@+id/mDevNames"
                android:scrollbars="none"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:numColumns="4"
                android:verticalSpacing="5dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/mAddDevMate"
                tools:text="张磊" />

            <TextView
                android:id="@+id/addVideoTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="上传视频文件"
                android:textColor="#000"
                android:textSize="15dp"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/mDevNames" />

            <com.twf.develophelpertools.view.ImageExpandGridView
                android:id="@+id/mRecycleView"
                android:layout_width="match_parent"
                android:layout_height="1000dp"
                android:scrollbars="none"
                android:gravity="center"
                android:horizontalSpacing="10dp"
                android:verticalSpacing="10dp"
                android:layout_marginTop="10dp"
                app:layout_constraintVertical_bias="0"
                app:layout_constraintBottom_toBottomOf="parent"
                android:numColumns="3"
                app:layout_constraintTop_toBottomOf="@+id/addVideoTitle"
                tools:layout_editor_absoluteX="20dp" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>


    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <CheckBox
            android:layout_marginTop="10dp"
            android:checked="true"
            android:layout_marginBottom="10dp"
            android:id="@+id/mCheckView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            app:layout_constraintBottom_toTopOf="@+id/mSubmit"
            app:layout_constraintLeft_toLeftOf="parent" />

        <TextView
            android:id="@+id/kit_textview2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="微信通知研发同学"
            app:layout_constraintBottom_toBottomOf="@+id/mCheckView"
            app:layout_constraintLeft_toRightOf="@+id/mCheckView"
            app:layout_constraintTop_toTopOf="@+id/mCheckView" />
    </LinearLayout>


    <TextView
        android:id="@+id/mSubmit"
        android:layout_marginRight="20dp"
        android:layout_marginLeft="20dp"
        android:textStyle="bold"
        android:textColor="#FFFFFF"
        android:gravity="center"
        android:text="提交数据"
        android:layout_marginBottom="20dp"
        android:textSize="15dp"
        android:background="#34CC5F"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_width="match_parent"
        android:layout_height="50dp" />
</LinearLayout>
