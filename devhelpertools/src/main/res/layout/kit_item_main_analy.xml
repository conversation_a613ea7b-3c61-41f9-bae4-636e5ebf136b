<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="100dip"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/mActionName"
            android:layout_width="0dip"
            android:layout_height="100dip"
            android:layout_marginLeft="10dip"
            android:layout_weight="1"
            android:gravity="center_vertical"
            tools:text="action名字"
            android:textColor="#337CC4"
            android:textSize="15dip" />

        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:background="#337CC4" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="100dip"
            android:layout_marginLeft="10dip"
            android:minWidth="100dip"
            android:orientation="vertical">

            <TextView
                android:id="@+id/mSuccess"
                android:layout_width="match_parent"
                android:layout_height="0dip"
                android:layout_weight="1"
                android:gravity="center_vertical"
                tools:text="成功"
                android:textColor="#212121" />


        </LinearLayout>
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#337CC4" />
</LinearLayout>
