<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:forceDarkAllowed="false"
    android:background="#FFFFFF"
    tools:targetApi="q"
    android:layout_height="match_parent"
    >

    <LinearLayout
        android:background="#FFFFFF"
        android:orientation="horizontal"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:id="@+id/mTabView"
        android:layout_width="match_parent"
        android:layout_height="50dp" />

    <View
        app:layout_constraintTop_toBottomOf="@+id/mTabView"
        android:layout_width="match_parent"
        android:layout_height="1dp" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/mViewPager"
        app:layout_constraintBottom_toTopOf="@+id/mExit"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mTabView" />


    <TextView
        android:textSize="20dp"
        android:textStyle="bold"
        android:textColor="#FFFFFF"
        android:gravity="center"
        android:text="退出"
        android:background="#222222"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:id="@+id/mExit"
        android:layout_width="match_parent"
        android:layout_height="50dp" />
</androidx.constraintlayout.widget.ConstraintLayout>