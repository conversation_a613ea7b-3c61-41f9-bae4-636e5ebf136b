<?xml version="1.0" encoding="utf-8"?><!--
 ~ Copyright (C) 2017 <PERSON>.
 ~
 ~ Licensed under the Apache License, Version 2.0 (the "License");
 ~ you may not use this file except in compliance with the License.
 ~ You may obtain a copy of the License at
 ~
 ~      http://www.apache.org/licenses/LICENSE-2.0
 ~
 ~ Unless required by applicable law or agreed to in writing, software
 ~ distributed under the License is distributed on an "AS IS" BASIS,
 ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 ~ See the License for the specific language governing permissions and
 ~ limitations under the License.
 -->
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFF"
    android:scrollbars="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/headers"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textIsSelectable="true" />

        <com.twf.develophelpertools.chunk.internal.view.ExpandCombineLayout
            android:id="@+id/mExpandCombineView"
            android:layout_height="wrap_content"
            android:layout_width="match_parent"
            android:orientation="vertical" />


    </LinearLayout>

</androidx.core.widget.NestedScrollView>
