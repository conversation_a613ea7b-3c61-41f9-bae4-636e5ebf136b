<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:background="@drawable/kit_info_background"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/color"
            android:layout_width="14dp"
            android:layout_height="14dp"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="16dp"
            android:src="#333333" />

        <TextView
            android:id="@+id/color_hex"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_weight="1"
            android:gravity="left" />

        <ImageView
            android:id="@+id/close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="15dp"
            android:src="@drawable/kit_close_icon" />
    </LinearLayout>
</FrameLayout>
