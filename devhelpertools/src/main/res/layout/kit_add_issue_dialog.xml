<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="#337CC4"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <androidx.constraintlayout.widget.ConstraintLayout
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_dialog_white"
        android:layout_margin="20dp"
        android:padding="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="20dp">

        <TextView
            android:textStyle="bold"
            android:id="@+id/mTitleView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="反馈问题"
            android:textColor="#000000"
            android:textSize="15dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/mCloseView"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:src="@drawable/kit_icon_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <ListView
            android:scrollbars="none"
            android:layout_marginBottom="10dp"
            android:layout_marginTop="10dp"
            app:layout_constraintBottom_toTopOf="@+id/mCreateView"
            tools:listitem="@layout/kit_add_issue_item"
            android:id="@+id/mListView"
            app:layout_constraintTop_toBottomOf="@+id/mTitleView"
            android:layout_width="match_parent"
            android:layout_height="200dp" />

        <TextView
            android:id="@+id/mCreateView"
            android:textColor="#FFFFFF"
            android:background="#34CC5F"
            android:layout_margin="10dp"
            android:gravity="center"
            android:text="不存在,新建问题"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            android:layout_width="match_parent"
            android:layout_height="50dp" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
