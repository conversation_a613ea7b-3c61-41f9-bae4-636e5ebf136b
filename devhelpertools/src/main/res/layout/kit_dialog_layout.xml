<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clickable="true"
    tools:ignore="ResourceName">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="400dp"
        android:layout_gravity="center"
        android:background="@drawable/bg_dialog_white"
        android:padding="10dp"
        android:orientation="vertical" >

        <LinearLayout
            android:id="@+id/content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:paddingTop="5dp"
            android:gravity="center"
            android:orientation="vertical">
            <ListView
                android:id="@+id/message_lv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:divider="@null"
                android:gravity="left|center"
                android:minHeight="50dp"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                android:layout_marginBottom="50dp"/>
        </LinearLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true">
            <LinearLayout
                android:id="@+id/twoButtonLayout"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_gravity="bottom"
                android:gravity="center"
                android:orientation="horizontal"
                android:visibility="visible">
                <Button
                    android:id="@+id/positiveButton"
                    style="@style/text_15_black_sdw"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="45dp"
                    android:gravity="center" />

                <Button
                    android:id="@+id/negativeButton"
                    style="@style/text_15_666666_sdw"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="45dp"
                    android:gravity="center" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/singleButtonLayout"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_gravity="bottom"
                android:paddingLeft="50dp"
                android:paddingRight="50dp"
                android:paddingBottom="10dp"
                android:gravity="center">
                <Button
                    android:id="@+id/singleButton"
                    style="@style/text_15_black_sdw"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:gravity="center" />
            </LinearLayout>
        </FrameLayout>

    </RelativeLayout>

</FrameLayout>