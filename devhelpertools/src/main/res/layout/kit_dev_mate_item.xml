<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="10dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <CheckBox
        android:id="@+id/mCheckView"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_width="40dp"
        android:layout_height="40dp" />


    <TextView
        android:textColor="#000000"
        android:textSize="15dp"
        android:layout_marginLeft="10dp"
        tools:text="张详东"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/mCheckView"
        android:id="@+id/mName"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <TextView
        android:textSize="12dp"
        android:layout_marginTop="10dp"
        android:layout_marginLeft="10dp"
        tools:text="研发七组"
        app:layout_constraintTop_toBottomOf="@+id/mName"
        app:layout_constraintLeft_toRightOf="@+id/mCheckView"
        android:id="@+id/mGroup"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <View
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@+id/mGroup"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:background="#337CC4"
        android:layout_width="match_parent"
        android:layout_height="0.5dp" />

</androidx.constraintlayout.widget.ConstraintLayout>
