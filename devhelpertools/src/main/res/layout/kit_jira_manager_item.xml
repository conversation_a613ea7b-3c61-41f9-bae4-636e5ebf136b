<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_height="wrap_content">


    <LinearLayout
        android:orientation="vertical"
        android:padding="10dp"
        android:minHeight="100dp"
        android:layout_margin="10dp"
        android:background="@drawable/bg_white_corner_divider"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:ellipsize="end"
                android:maxLines="4"
                android:id="@+id/mTitle"
                android:textColor="#000"
                android:textSize="12dp"
                android:layout_marginRight="10dp"
                tools:text="标标题标标题标题标题标题题标标题标题标题标题题标标题标题标题标题题标标题标题标题标题题标标题标题标题标题题标标题标题标题标题题标标题标题标题标题题标题标题标题题"
                android:textStyle="bold"
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="wrap_content" />

            <ImageView
                android:id="@+id/mEdit"
                android:src="@drawable/ic_edit"
                android:layout_width="30dp"
                android:layout_height="30dp" />

        </LinearLayout>

        <TextView
            android:ellipsize="end"
            android:maxLines="3"
            android:id="@+id/mDesc"
            android:layout_marginTop="10dp"
            android:textColor="#333333"
            android:textSize="12dp"
            tools:text="描描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述述"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/mDevMates"
            android:layout_marginTop="10dp"
            android:textColor="#000"
            android:textSize="12dp"
            tools:text="开发同学:AAA,BB,CC"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <com.twf.develophelpertools.view.IssueItemImageLayout
            android:scrollbars="none"
            android:numColumns="5"
            android:id="@+id/mImageFiles"
            android:layout_marginTop="20dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />


    </LinearLayout>

</LinearLayout>
