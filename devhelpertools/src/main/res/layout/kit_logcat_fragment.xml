<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingLeft="10dip"
    android:paddingRight="10dip">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dip"
        android:background="#FFFFFF"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/mInputText"
                android:layout_width="0dip"
                android:layout_height="wrap_content"
                android:layout_margin="5dip"
                android:layout_weight="1"
                android:background="#FFFFFF"
                android:hint="输入筛选关键字"
                android:minHeight="50dip"
                android:paddingLeft="5dip" />

            <CheckBox
                android:id="@+id/mCheckView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="自动滑动" />

            <Button
                android:id="@+id/mClear"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dip"
                android:text="清空列表" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/mContainer"
            android:layout_width="match_parent"
            android:layout_height="50dip"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/mVerbose"
                style="@style/kit_check_box_left"
                android:layout_width="0dip"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:checked="true"
                android:gravity="center"
                android:text="Verbose"
                android:textSize="15dip" />

            <RadioButton
                android:id="@+id/mDebug"
                style="@style/kit_check_box_center"
                android:layout_width="0dip"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="Debug"
                android:textSize="15dip" />

            <RadioButton
                android:id="@+id/mInfo"
                style="@style/kit_check_box_center"
                android:layout_width="0dip"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="Info"
                android:textSize="15dip" />

            <RadioButton
                android:id="@+id/mWarn"
                style="@style/kit_check_box_center"
                android:layout_width="0dip"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="Warn"
                android:textSize="15dip" />

            <RadioButton
                android:id="@+id/mError"
                style="@style/kit_check_box_right"
                android:layout_width="0dip"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="Error"
                android:textSize="15dip" />
        </LinearLayout>

    </LinearLayout>

    <ListView
        android:id="@+id/mListView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:cacheColorHint="#000000"
        android:divider="#ff0000"
        android:dividerHeight="1dp" />
</LinearLayout>
