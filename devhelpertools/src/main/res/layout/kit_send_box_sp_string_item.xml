<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="top"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/mKeyName"
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:paddingBottom="15dip"
            android:paddingTop="15dip"
            android:textColor="#000"
            android:textSize="15dip" />

        <TextView
            android:id="@+id/mValueClass"
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="#C4C4C4"
            android:textSize="15dip" />

        <EditText
            android:id="@+id/mValue"
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="#000"
            android:textSize="15dip" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dip"
        android:background="#CCC" />
</LinearLayout>
