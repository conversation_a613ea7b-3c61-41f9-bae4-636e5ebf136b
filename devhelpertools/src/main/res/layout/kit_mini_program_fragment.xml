<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:paddingLeft="10dp"
    android:paddingTop="10dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="替换小程序类型"
        android:textColor="#212121"
        android:textSize="18dp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/mChooseType"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        tools:text="已选择：体验版"
        android:textColor="#212121"
        android:textSize="18dp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/mRelease"
        android:layout_width="100dp"
        android:layout_height="50dp"
        android:layout_marginTop="20dp"
        android:background="#0000ff"
        android:gravity="center"
        android:text="正式版"
        android:textColor="#FFFFFF"
        android:textSize="15dp" />

    <TextView
        android:id="@+id/mDevelop"
        android:layout_width="100dp"
        android:layout_height="50dp"
        android:layout_marginTop="20dp"
        android:background="#0000ff"
        android:gravity="center"
        android:text="开发版"
        android:textColor="#FFFFFF"
        android:textSize="15dp" />

    <TextView
        android:id="@+id/mExperience"
        android:layout_width="100dp"
        android:layout_height="50dp"
        android:layout_marginTop="20dp"
        android:background="#0000ff"
        android:gravity="center"
        android:text="体验版"
        android:textColor="#FFFFFF"
        android:textSize="15dp" />
</LinearLayout>
