<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Kit_UI_Theme.AppCompat.Translucent" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="Kit_UI_Theme.Holo.Dialog.background.Translucent" parent="@android:style/Theme.Holo.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>

    <style name="kit_TreeNodeStyleCustom">
        <item name="android:paddingLeft">20dp</item>
    </style>
</resources>