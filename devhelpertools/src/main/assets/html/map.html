<!DOCTYPE html>
<html>

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <style type="text/css">
    * {
      margin: 0px;
      padding: 0px;
    }

    body,
    button,
    input,
    select,
    textarea {
      font: 12px/16px Verdana, Helvetica, Arial, sans-serif;
    }

    #container {
      min-width: 600px;
      min-height: 800px;
    }
  </style>
  <script charset="utf-8" src="https://map.qq.com/api/js?v=2.exp"></script>
  <script>
    var init = function () {
      var map = new qq.maps.Map(document.getElementById("container"), {
        center: new qq.maps.LatLng(39.916527, 116.397128),
        zoom: 13
      });
      qq.maps.event.addListener(map, 'mousemove', function (event) {
        var latLng = event.latLng,
          lat = latLng.getLat().toFixed(5),
          lng = latLng.getLng().toFixed(5);
        window.location = "doraemon://invokeNative/sendLocation?lat=" + lat + "&lng=" + lng;
      });
    }
  </script>
</head>

<body onload="init()">
  <div id="container"></div>
</body>

</html>