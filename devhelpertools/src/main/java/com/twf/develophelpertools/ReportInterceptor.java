package com.twf.develophelpertools;

import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

//import com.hpbr.apm.common.net.analysis.SimpleNetEventListener;
import com.kit.baselibrary.ApiReportConfigBean;
import com.kit.baselibrary.AppCallBackItf;
import com.kit.baselibrary.AppInfoBean;
import com.twf.develophelpertools.model.fragment.SwitchConfigFragment;
import com.twf.develophelpertools.util.ActivityNameStackUtils;

import java.io.EOFException;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.UnsupportedCharsetException;
import java.util.List;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.FormBody;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.internal.http.HttpHeaders;
import okio.Buffer;
import okio.BufferedSource;
import okio.GzipSource;
import okio.Okio;

/**
 * create by guofeng
 * date on 2019-12-28
 */
public class ReportInterceptor implements Interceptor {

    public static long maxContentLength = Long.MAX_VALUE;

    private static final Charset UTF8 = Charset.forName("UTF-8");


    @Override
    public Response intercept(Chain chain) throws IOException {
        Response response = null;
        try {
            Request request = chain.request();
            response = chain.proceed(request);

            AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();

            if (appCallBackItf != null && !appCallBackItf.isInterceptRequest(request.headers(), response.body(), request.url().toString())) {
                postToServer(request, response);
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
        return response;
    }

    private void postToServer(Request request, Response response) throws IOException {


        boolean isOpen = SwitchConfigFragment.isOpenApiReport();

        if (isOpen) {
            final StringBuilder params = new StringBuilder();
            if (request.method().equalsIgnoreCase("POST")) {
                RequestBody body1 = request.body();
                if (body1 instanceof FormBody) {
                    FormBody body = (FormBody) body1;
                    if (body != null) {
                        int size = body.size();
                        for (int i = 0; i < size; i++) {
                            String key = body.name(i);
                            String value = body.value(i);
                            if (TextUtils.isEmpty(key) || TextUtils.isEmpty(value)) continue;
                            params.append(key);
                            params.append("=");
                            params.append(value);
                            params.append("&");
                        }
                    }
                    String url = request.url().toString();

                    String result = getResponseString(response);
                    reportToServer(result, url, response.code(), params.toString(), request.method());
                }
            } else {
                HttpUrl hUrl = request.url();
                boolean https = hUrl.isHttps();
                List<String> pathList = hUrl.pathSegments();
                StringBuilder stringBuilder = new StringBuilder();
                if (pathList != null) {
                    for (String p : pathList) {
                        stringBuilder.append(p);
                        stringBuilder.append("/");
                    }
                }

                String url;
                if (https) {
                    url = "https://" + hUrl.host() + "/" + stringBuilder.toString();
                } else {
                    url = "http://" + hUrl.host() + "/" + stringBuilder.toString();
                }
                String result = getResponseString(response);
                reportToServer(result, url, response.code(), hUrl.query(), request.method());
            }

        }
    }

    /**
     * http://*************:8085/boss/log   post请求    参数：url、userId、parameter、method、result、status
     *
     * @param result
     * @param url
     */
    //上传接口数据到QA后端，实时预览
    private void reportToServer(String result, String url, int status, String parameter, String methdoType) {

        String reportUrl = "";
        AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
        if (appCallBackItf != null) {
            ApiReportConfigBean apiReportConfigBean = appCallBackItf.getApiReportConfigBean();
            if (apiReportConfigBean != null) {
                reportUrl = apiReportConfigBean.getReportUrl();
            }

        }

        if (TextUtils.isEmpty(reportUrl)) return;

        String userId = null;
        if (appCallBackItf != null) {
            AppInfoBean appInfo = appCallBackItf.getAppInfo();
            if (appInfo != null) {
                userId = appInfo.getUserId();
            }
        }

        FormBody.Builder builder = new FormBody.Builder();
        if (methdoType != null) {
            builder.add("method", methdoType);
        }
        if (result != null) {
            builder.add("result", result);
        }
        if (url != null) {
            builder.add("url", url);
        }


        builder.add("device", "android");

        builder.add("status", String.valueOf(status));

        if (parameter != null) {

            if (parameter.contains("sp=")
                    && parameter.contains("&")) {
                String[] split = parameter.split("&");

                StringBuilder stringBuilder = new StringBuilder();

                for (String item : split) {
                    String[] subItem = item.split("=");
                    if (subItem.length == 2) {
                        String k = subItem[0];
                        String v = subItem[1];
                        if ("sp".equals(k)) {
                            String decryValue = appCallBackItf.getReportDecryParams(v, url);
                            if (!TextUtils.isEmpty(stringBuilder.toString())) {
                                stringBuilder.append("&");
                            }
                            stringBuilder.append(decryValue);
                        } else {
                            if (!TextUtils.isEmpty(stringBuilder.toString())) {
                                stringBuilder.append("&");
                            }
                            stringBuilder.append(k);
                            stringBuilder.append("=");
                            stringBuilder.append(v);
                        }
                    }
                }
                Log.d(TAG, "reportToServer:parameter= " + stringBuilder.toString());
                builder.add("parameter", stringBuilder.toString());
            } else {
                builder.add("parameter", parameter);
                Log.d(TAG, "reportToServer:parameter= " + parameter);
            }

            builder.add("encryptionParameter", parameter);
        }

        if (userId != null) {
            builder.add("userId", userId);
        }
        String activityInfo = ActivityNameStackUtils.getInstance().getActivityInfo();

        builder.add("activity",activityInfo);

        Log.d(TAG, "reportToServer:encryptionParameter= " + parameter);


        Request request = new Request.Builder()
                .url(reportUrl)
                .post(builder.build())
                .build();


        if (appCallBackItf != null) {
            appCallBackItf.reportHttpResponse(request);
        }


        Log.d(TAG, "parameter: " + parameter);
        Log.d(TAG, "status: " + status);
        Log.d(TAG, "url: " + url);
        Log.d(TAG, "result: " + result);
        Log.d(TAG, "activitys: " + activityInfo);
        Log.d(TAG, "=======================================================");
    }


    private static final String TAG = "ReportInterceptor";

    private String getResponseString(Response response) throws IOException {
        String responseString = null;
        ResponseBody responseBody = response.body();
        if (HttpHeaders.hasBody(response)) {
            BufferedSource source = getNativeSource(response);
            source.request(Long.MAX_VALUE);
            Buffer buffer = source.buffer();
            Charset charset = UTF8;
            MediaType contentType = responseBody.contentType();
            if (contentType != null) {
                try {
                    charset = contentType.charset(UTF8);
                } catch (UnsupportedCharsetException e) {
                    return "";
                }
            }
            if (isPlaintext(buffer)) {
                responseString = readFromBuffer(buffer.clone(), charset);
            }
        }
        return responseString;
    }


    private String readFromBuffer(Buffer buffer, Charset charset) {
        long bufferSize = buffer.size();
        long maxBytes = Math.min(bufferSize, maxContentLength);
        String body = "";
        try {
            body = buffer.readString(maxBytes, charset);
        } catch (EOFException e) {
        }
        return body;
    }

    private BufferedSource getNativeSource(Response response) throws IOException {
        if (bodyGzipped(response.headers())) {
            BufferedSource source = response.peekBody(maxContentLength).source();
            if (source.buffer().size() < maxContentLength) {
                return getNativeSource(source, true);
            } else {
            }
        }
        return response.body().source();
    }


    private boolean bodyGzipped(Headers headers) {
        String contentEncoding = headers.get("Content-Encoding");
        return "gzip".equalsIgnoreCase(contentEncoding);
    }

    private BufferedSource getNativeSource(BufferedSource input, boolean isGzipped) {
        if (isGzipped) {
            GzipSource source = new GzipSource(input);
            return Okio.buffer(source);
        } else {
            return input;
        }
    }

    private boolean isPlaintext(Buffer buffer) {
        try {
            Buffer prefix = new Buffer();
            long byteCount = buffer.size() < 64 ? buffer.size() : 64;
            buffer.copyTo(prefix, 0, byteCount);
            for (int i = 0; i < 16; i++) {
                if (prefix.exhausted()) {
                    break;
                }
                int codePoint = prefix.readUtf8CodePoint();
                if (Character.isISOControl(codePoint) && !Character.isWhitespace(codePoint)) {
                    return false;
                }
            }
            return true;
        } catch (EOFException e) {
            return false; // Truncated UTF-8 sequence.
        }
    }

}
