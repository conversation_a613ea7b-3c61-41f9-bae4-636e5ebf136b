package com.twf.develophelpertools.binder;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.twf.develophelpertools.item.AttrsDialogItemViewBinder;
import com.twf.develophelpertools.item.TitleItem;
import com.twf.develophelpertools.view.KitAttrsDialog;

/**
 * @author: weishenhong <a href="mailto:<EMAIL>">contact me.</a>
 * @date: 2019-07-08 23:46
 */
public class TitleItemBinder extends AttrsDialogItemViewBinder<TitleItem, KitAttrsDialog.Adapter.TitleViewHolder> {
    @NonNull
    @Override
    public KitAttrsDialog.Adapter.TitleViewHolder onCreateViewHolder(@NonNull LayoutInflater inflater, @NonNull ViewGroup parent, RecyclerView.Adapter adapter) {
        return KitAttrsDialog.Adapter.TitleViewHolder.newInstance(parent);
    }

    @Override
    public void onBindViewHolder(@NonNull KitAttrsDialog.Adapter.TitleViewHolder holder, @NonNull TitleItem item) {
        holder.bindView(item);
    }
}
