package com.twf.develophelpertools.binder;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.twf.develophelpertools.item.AttrsDialogItemViewBinder;
import com.twf.develophelpertools.item.EditTextItem;
import com.twf.develophelpertools.view.KitAttrsDialog;


/**
 * @author: weishenhong <a href="mailto:<EMAIL>">contact me.</a>
 * @date: 2019-07-08 23:46
 */
public class EditTextItemBinder extends AttrsDialogItemViewBinder<EditTextItem, KitAttrsDialog.Adapter.EditTextViewHolder<EditTextItem>> {
    @NonNull
    @Override
    public KitAttrsDialog.Adapter.EditTextViewHolder onCreateViewHolder(@NonNull LayoutInflater inflater, @NonNull ViewGroup parent, RecyclerView.Adapter adapter) {
        return KitAttrsDialog.Adapter.EditTextViewHolder.newInstance(parent);
    }

    @Override
    public void onBindViewHolder(@NonNull KitAttrsDialog.Adapter.EditTextViewHolder holder, @NonNull EditTextItem item) {
        holder.bindView(item);
    }
}
