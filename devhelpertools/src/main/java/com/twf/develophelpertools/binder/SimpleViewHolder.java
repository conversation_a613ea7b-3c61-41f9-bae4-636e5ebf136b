package com.twf.develophelpertools.binder;

import android.content.Context;
import android.view.View;
import android.widget.TextView;

import com.twf.develophelpertools.item.TreeNode;

public class SimpleViewHolder extends TreeNode.BaseNodeViewHolder<Object> {
    public SimpleViewHolder(Context context) {
        super(context);
    }

    public View createNodeView(TreeNode node, Object value) {
        TextView tv = new TextView(this.context);
        tv.setText(String.valueOf(value));
        return tv;
    }

    public void toggle(boolean active) {
    }
}
