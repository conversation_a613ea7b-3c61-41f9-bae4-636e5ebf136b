package com.twf.develophelpertools.binder;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.twf.develophelpertools.item.AddMinusEditItem;
import com.twf.develophelpertools.item.AttrsDialogItemViewBinder;
import com.twf.develophelpertools.view.KitAttrsDialog;


/**
 * @author: weishenhong <a href="mailto:<EMAIL>">contact me.</a>
 * @date: 2019-07-08 23:46
 */
public class AddMinusEditTextItemBinder extends AttrsDialogItemViewBinder<AddMinusEditItem, KitAttrsDialog.Adapter.AddMinusEditViewHolder> {
    @NonNull
    @Override
    public KitAttrsDialog.Adapter.AddMinusEditViewHolder onCreateViewHolder(@NonNull LayoutInflater inflater, @NonNull ViewGroup parent, RecyclerView.Adapter adapter) {
        return KitAttrsDialog.Adapter.AddMinusEditViewHolder.newInstance(parent);
    }

    @Override
    public void onBindViewHolder(@NonNull KitAttrsDialog.Adapter.AddMinusEditViewHolder holder, @NonNull AddMinusEditItem item) {
        holder.bindView(item);
    }
}
