package com.twf.develophelpertools.binder;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.twf.develophelpertools.item.AttrsDialogItemViewBinder;
import com.twf.develophelpertools.view.KitAttrsDialog;
import com.twf.develophelpertools.item.BitmapItem;

/**
 * @author: weishenhong <a href="mailto:<EMAIL>">contact me.</a>
 * @date: 2019-07-08 23:46
 */
public class BitmapItemBinder extends AttrsDialogItemViewBinder<BitmapItem, KitAttrsDialog.Adapter.BitmapInfoViewHolder> {
    @NonNull
    @Override
    public KitAttrsDialog.Adapter.BitmapInfoViewHolder onCreateViewHolder(@NonNull LayoutInflater inflater, @NonNull ViewGroup parent, RecyclerView.Adapter adapter) {
        return KitAttrsDialog.Adapter.BitmapInfoViewHolder.newInstance(parent);
    }

    @Override
    public void onBindViewHolder(@NonNull KitAttrsDialog.Adapter.BitmapInfoViewHolder holder, @NonNull BitmapItem item) {
        holder.bindView(item);
    }
}
