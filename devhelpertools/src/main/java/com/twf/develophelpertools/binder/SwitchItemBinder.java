package com.twf.develophelpertools.binder;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.twf.develophelpertools.item.AttrsDialogItemViewBinder;
import com.twf.develophelpertools.item.BriefDescItem;
import com.twf.develophelpertools.item.SwitchItem;
import com.twf.develophelpertools.view.KitAttrsDialog;

/**
 * @author: weishenhong <a href="mailto:<EMAIL>">contact me.</a>
 * @date: 2019-07-08 23:46
 */
public class SwitchItemBinder extends AttrsDialogItemViewBinder<SwitchItem, KitAttrsDialog.Adapter.SwitchViewHolder> {
    @NonNull
    @Override
    public KitAttrsDialog.Adapter.SwitchViewHolder onCreateViewHolder(@NonNull LayoutInflater inflater, @NonNull ViewGroup parent, RecyclerView.Adapter adapter) {
        return KitAttrsDialog.Adapter.SwitchViewHolder.newInstance(parent, getAttrDialogCallback(adapter));
    }

    @Override
    public void onBindViewHolder(@NonNull KitAttrsDialog.Adapter.SwitchViewHolder holder, @NonNull SwitchItem item) {
        holder.bindView(item);
    }
}
