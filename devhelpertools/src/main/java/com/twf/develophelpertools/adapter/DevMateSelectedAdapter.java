package com.twf.develophelpertools.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.base.BaseSingleCardAdapter;
import com.twf.develophelpertools.bean.DevMateBean;
import com.twf.develophelpertools.util.KitDialogUtil;
import com.twf.develophelpertools.util.KitForegroundUtil;

/**
 * create by guofeng
 * date on 2021/5/8
 */

public class DevMateSelectedAdapter extends BaseSingleCardAdapter<DevMateBean> {

    public interface OnDeleteMateCallBack{
        void onDeleteMateListener(DevMateBean devMateBean);
    }

    private OnDeleteMateCallBack callBack;

    public void setCallBack(OnDeleteMateCallBack callBack) {
        this.callBack = callBack;
    }

    @Override
    public View getView(int position, View view, ViewGroup parent) {

        ViewHolder holder;

        if (view == null) {
            view = LayoutInflater.from(Kit.getInstance().getApplication()).inflate(R.layout.kit_dev_mate_selected_item, null);
            holder = new ViewHolder(view);
            view.setTag(holder);
        } else {
            holder = (ViewHolder) view.getTag();
        }

        final DevMateBean item = getItem(position);
        holder.mName.setText(item.getName());
        holder.mDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                KitDialogUtil kitDialogUtil = new KitDialogUtil(KitForegroundUtil.getInstance().getTopActivity());
                kitDialogUtil.setTitle("温馨提示?");
                kitDialogUtil.setMessage("确定删除?");
                kitDialogUtil.setPositiveListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                        if (callBack != null) {
                            callBack.onDeleteMateListener(item);
                        }

                    }
                });
                kitDialogUtil.show();
            }
        });

        return view;

    }


    private static class ViewHolder {

        private ImageView mDelete;

        private TextView mName;

        public ViewHolder(View convertView) {
            this.mName = convertView.findViewById(R.id.mName);
            this.mDelete = convertView.findViewById(R.id.mDelete);
        }
    }

}