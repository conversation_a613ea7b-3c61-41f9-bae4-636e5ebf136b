package com.twf.develophelpertools.adapter.base;

import android.content.Context;
import androidx.annotation.NonNull;

import android.view.View;
import android.view.ViewGroup;

import com.twf.develophelpertools.adapter.ViewAndHolderItem;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by guofeng
 * on 2019/5/30.
 * ListView多卡片适配器
 */
public abstract class BaseMultiCardAdapter extends BaseSingleCardAdapter {


    private final List<ViewAndHolderItem> itemTypeList = new ArrayList<>();

    private Context mContext;

    public BaseMultiCardAdapter(Context context) {
        this.mContext = context;
    }

    public void registerSupportItem(List<ViewAndHolderItem> itemList) {
        itemTypeList.clear();
        if (itemList != null) {
            itemTypeList.addAll(itemList);
        }
    }


    @Override
    public int getViewTypeCount() {
        return itemTypeList.size();
    }

    @Override
    public int getItemViewType(int position) {
        return findSupportIndex(position);
    }

    private int findSupportIndex(int position) {
        Object item = getItem(position);
        for (int i = 0; i < itemTypeList.size(); i++) {
            ViewAndHolderItem holderItem = itemTypeList.get(i);
            if (holderItem == null) continue;
            if (holderItem.isSupport(item)) {
                return i;
            }
        }
        return 0;
    }


    private boolean instanceOfObject(ViewAndHolderItem holderItem, Class<?> objectClass) {
        Type genericSuperclass = holderItem.getClass().getGenericInterfaces()[0];
        if (genericSuperclass instanceof ParameterizedType) {
            ParameterizedType type = (ParameterizedType) genericSuperclass;
            Type[] actualTypeArguments = type.getActualTypeArguments();
            Type actualTypeArgument = actualTypeArguments[1];
            return ((Class) actualTypeArgument).isAssignableFrom(objectClass);
        }
        return false;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup viewGroup) {
        ViewAndHolderItem holderItem = itemTypeList.get(getItemViewType(position));
        BaseKitHolder holder;
        if (convertView != null && convertView.getTag() != null && instanceOfObject(holderItem, convertView.getTag().getClass())) {
            holder = (BaseKitHolder) convertView.getTag();
        } else {
            convertView = holderItem.createConvertView();
            holder = holderItem.createHolder(convertView);
            convertView.setTag(holder);
        }
        holder.initValue(getItem(position));
        return convertView;
    }


    public Context getmContext() {
        return mContext;
    }
}
