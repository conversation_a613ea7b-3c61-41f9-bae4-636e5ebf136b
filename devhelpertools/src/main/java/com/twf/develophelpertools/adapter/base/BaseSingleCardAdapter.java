package com.twf.develophelpertools.adapter.base;

import android.widget.BaseAdapter;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by guofeng
 * on 2019/5/30.
 * ListView单卡片适配器
 */
public abstract class BaseSingleCardAdapter<D> extends BaseAdapter {

    private final List<D> sourceData = new ArrayList<>();

    public List<D> getSourceData() {
        return sourceData;
    }

    @Override
    public int getCount() {
        return sourceData.size();
    }


    @Override
    public D getItem(int i) {
        int count = getCount();
        if (i > (count - 1)) {
            return null;
        }
        return sourceData.get(i);
    }

    @Override
    public long getItemId(int i) {
        return i;
    }

    public void addData(D d) {
        sourceData.add(d);
        notifyDataSetChanged();
    }

    public void addAll(List<D> data) {
        if (data != null) {
            sourceData.addAll(data);
            notifyDataSetChanged();
        }
    }


    public void setData(List<D> data) {
        sourceData.clear();
        if (data != null) {
            sourceData.addAll(data);
        }
        notifyDataSetChanged();
    }

}
