package com.twf.develophelpertools.adapter.sendbox;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.base.BaseSingleCardAdapter;

/**
 * Created by guofeng
 * on 2019/6/6.
 */

public class ShowTextAdapter extends BaseSingleCardAdapter<String> {

    private Context context;

    public ShowTextAdapter(Context context) {
        this.context = context;
    }

    @Override
    public View getView(int position, View view, ViewGroup viewGroup) {
        ViewHolder holder;
        if (view == null) {
            view = LayoutInflater.from(context).inflate(R.layout.kit_show_text_item, null);
            holder = new ViewHolder(view);
            view.setTag(holder);
        } else {
            holder = (ViewHolder) view.getTag();
        }
        String item = getItem(position);
        holder.mTextView.setText(item);
        return view;
    }

    private static class ViewHolder {

        private TextView mTextView;

        public ViewHolder(View view) {
            this.mTextView = view.findViewById(R.id.mTextView);
        }
    }
}
