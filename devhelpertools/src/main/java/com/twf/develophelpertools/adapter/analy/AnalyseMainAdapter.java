package com.twf.develophelpertools.adapter.analy;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.base.BaseSingleCardAdapter;
import com.twf.develophelpertools.bean.AnalyseInfoBean;

public class AnalyseMainAdapter extends BaseSingleCardAdapter<AnalyseInfoBean> {


    @Override
    public View getView(int position, View view, ViewGroup viewGroup) {
        ViewHolder holder;

        if (view == null) {
            view = LayoutInflater.from(Kit.getInstance().getApplication()).inflate(R.layout.kit_item_main_analy, null);
            holder = new ViewHolder(view);
            view.setTag(holder);
        } else {
            holder = (ViewHolder) view.getTag();
        }
        AnalyseInfoBean item = getItem(position);
        if (item != null) {
            holder.mActionName.setText(item.actionKey);
            holder.mSuccess.setText(item.successDesc);
        }
        return view;
    }


    private static class ViewHolder {

        private TextView mActionName;
        private TextView mSuccess;

        public ViewHolder(View convertView) {
            this.mActionName = convertView.findViewById(R.id.mActionName);
            this.mSuccess = convertView.findViewById(R.id.mSuccess);
        }
    }
}
