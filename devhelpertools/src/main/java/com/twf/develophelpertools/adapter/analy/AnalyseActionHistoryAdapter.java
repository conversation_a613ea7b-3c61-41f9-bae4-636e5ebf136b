package com.twf.develophelpertools.adapter.analy;

import android.text.SpannableString;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.base.BaseSingleCardAdapter;

public class AnalyseActionHistoryAdapter extends BaseSingleCardAdapter<SpannableString> {
    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        if (convertView == null) {
            convertView = LayoutInflater.from(Kit.getInstance().getApplication()).inflate(R.layout.kit_analyse_action_history_item, null);
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }
        holder.mHistory.setText(getItem(position));
        return convertView;
    }

    private static class ViewHolder {

        private TextView mHistory;

        public ViewHolder(View convertView) {
            this.mHistory = convertView.findViewById(R.id.mTextView);
        }
    }
}
