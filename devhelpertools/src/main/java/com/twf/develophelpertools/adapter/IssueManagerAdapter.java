package com.twf.develophelpertools.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.base.BaseSingleCardAdapter;
import com.twf.develophelpertools.bean.DevMateBean;
import com.twf.develophelpertools.bean.JiraItemBean;
import com.twf.develophelpertools.view.IssueItemImageLayout;

import java.util.List;

/**
 * create by guofeng
 * date on 2021/5/8
 */

public class IssueManagerAdapter extends BaseSingleCardAdapter<JiraItemBean> {



    public interface OnEditCallBack {
        void onEditListener(int index);
    }

    private OnEditCallBack onEditCallBack;

    public void setOnEditCallBack(OnEditCallBack onEditCallBack) {
        this.onEditCallBack = onEditCallBack;
    }

    @Override
    public View getView(final int position, View view, ViewGroup viewGroup) {
        ViewHolder holder;

        if (view == null) {
            view = LayoutInflater.from(Kit.getInstance().getApplication()).inflate(R.layout.kit_jira_manager_item, null);
            holder = new ViewHolder(view);
            view.setTag(holder);
        } else {
            holder = (ViewHolder) view.getTag();
        }
        JiraItemBean item = getItem(position);
        if (item != null) {
            holder.mTitle.setText(item.getTitle());
            holder.mDesc.setText(item.getDesc());
            holder.mDevMates.setText(getMateText(item.getMateList()));
            holder.mEdit.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    //跳转到编辑页面
                    if (onEditCallBack != null) {
                        onEditCallBack.onEditListener(position);
                    }
                }
            });
            holder.mImageFiles.setJiraImageBeanList(item.getImageList());
        }
        return view;
    }

    private String getMateText(List<DevMateBean> mateList) {
        if (mateList == null || mateList.isEmpty()) {
            return "开发同学";
        }
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("开发同学");
        stringBuilder.append(":");
        for (DevMateBean devMateBean : mateList) {
            stringBuilder.append(devMateBean.getName());
            stringBuilder.append(',');
        }
        return stringBuilder.toString();
    }


    private static class ViewHolder {

        private TextView mTitle;
        private ImageView mEdit;
        private TextView mDesc;
        private TextView mDevMates;
        private IssueItemImageLayout mImageFiles;

        public ViewHolder(View convertView) {
            this.mTitle = convertView.findViewById(R.id.mTitle);
            this.mEdit = convertView.findViewById(R.id.mEdit);
            this.mDesc = convertView.findViewById(R.id.mDesc);
            this.mDevMates = convertView.findViewById(R.id.mDevMates);
            this.mImageFiles = convertView.findViewById(R.id.mImageFiles);
        }
    }

}