package com.twf.develophelpertools.adapter.logcat;

import androidx.core.content.ContextCompat;

import android.graphics.Color;
import android.text.Editable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.base.BaseSingleCardAdapter;
import com.twf.develophelpertools.bean.LogcatBean;
import com.twf.develophelpertools.util.KitLogcatUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by guofeng
 * on 2019/6/24.
 */

public class LogcatAdapter extends BaseSingleCardAdapter<LogcatBean> {


    private int level = Log.VERBOSE;

    private final List<LogcatBean> allLogcatList = new ArrayList<>();

    public void setLevel(int level) {
        this.level = level;
        //过滤全部满足条件的集合
        final List<LogcatBean> result = new ArrayList<>();
        for (LogcatBean logcatBean : allLogcatList) {
            if (logcatBean == null) continue;
            LogcatBean filterLogcatList = getFilterLogcatList(logcatBean);
            if (filterLogcatList == null) continue;
            result.add(filterLogcatList);
        }
        //刷新缓存区域
        setData(result);
    }

    //筛选的文案
    private String filterText;

    //监控输入框
    public void setInputEditText(EditText mInputText) {
        mInputText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                filterText = s.toString();
                setLevel(level);
            }
        });
    }

    //获得过滤后的数据集合
    private LogcatBean getFilterLogcatList(LogcatBean logcatBean) {
        //筛选日志级别
        if (logcatBean != null && logcatBean.getLogLevel() >= level) {
            //筛选输入框内容
            if (!TextUtils.isEmpty(filterText)) {
                if (logcatBean.getTag().contains(filterText)) {
                    return logcatBean;
                }
                return null;
            }
            return logcatBean;
        }
        return null;
    }


    @Override
    public void addData(LogcatBean logcatBean) {
        //添加到总日志里面
        if (logcatBean != null) {
            allLogcatList.add(logcatBean);
        }
        //过滤当前日志
        LogcatBean filterLogcatList = getFilterLogcatList(logcatBean);
        if (filterLogcatList != null) {
            super.addData(logcatBean);
        }
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        if (convertView == null) {
            convertView = LayoutInflater.from(Kit.getInstance().getApplication()).inflate(R.layout.kit_logcat_item, null);
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }


       final LogcatBean item = getItem(position);
        if (item != null) {
            //筛选文案高亮
            if (!TextUtils.isEmpty(filterText)) {
                String tagText = item.getTag();
                String logOutput = item.getLogOutput();
                int startTagHighIndex = tagText.indexOf(filterText);
                int startContentHighIndex = logOutput.indexOf(filterText);
                //tag标签
                if (startTagHighIndex == -1) {
                    holder.tag.setText(tagText);
                } else {
                    initHighLight(holder.tag, tagText, startTagHighIndex);
                }
                //内容
                if (startContentHighIndex == -1) {
                    holder.content.setText(KitLogcatUtil.logcatFormatter(logOutput));
                } else {
                    initHighLight(holder.content, KitLogcatUtil.logcatFormatter(logOutput), startContentHighIndex);
                }
                holder.time.setText(item.getTimestamp());
                holder.pId.setText(String.valueOf(item.getProcessId()));
                holder.type.setText(item.getLogLevelText());

            } else {
                holder.tag.setText(item.getTag());
                holder.time.setText(item.getTimestamp());
                holder.pId.setText(String.valueOf(item.getProcessId()));
                holder.type.setText(item.getLogLevelText());
                holder.content.setText(item.getLogOutput());
            }
            holder.content.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (callBack != null) {
                        String logOutput = item.getLogOutput();
                        callBack.onClickListener(logOutput);
                    }
                }
            });
        }
        return convertView;
    }

    private void initHighLight(TextView textView, String text, int startTagHighIndex) {
        SpannableString tagSPan = new SpannableString(text);
        ForegroundColorSpan colorSpan = new ForegroundColorSpan(Color.parseColor("#ff0000"));
        tagSPan.setSpan(colorSpan, startTagHighIndex, startTagHighIndex + filterText.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        textView.setText(tagSPan);
    }

    private OnClickCallBack callBack;

    public void setCallBack(OnClickCallBack callBack) {
        this.callBack = callBack;
    }

    public interface OnClickCallBack {
        void onClickListener(String text);
    }

    private static class ViewHolder {

        private TextView tag;
        private TextView time;
        private TextView pId;
        private TextView type;
        private TextView content;

        public ViewHolder(View convertView) {
            tag = convertView.findViewById(R.id.tag);
            time = convertView.findViewById(R.id.time);
            pId = convertView.findViewById(R.id.pId);
            type = convertView.findViewById(R.id.type);
            content = convertView.findViewById(R.id.content);
        }
    }

}
