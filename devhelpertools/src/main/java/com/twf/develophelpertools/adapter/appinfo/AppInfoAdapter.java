package com.twf.develophelpertools.adapter.appinfo;

import android.content.Context;

import com.twf.develophelpertools.adapter.EmptyHolderView;
import com.twf.develophelpertools.adapter.ViewAndHolderItem;
import com.twf.develophelpertools.adapter.base.BaseMultiCardAdapter;
import com.twf.develophelpertools.bean.AppInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by guofeng
 * on 2019/5/30.
 */

public class AppInfoAdapter extends BaseMultiCardAdapter {

    public AppInfoAdapter(Context context) {
        super(context);
        registerSupportItem(regAllSupportCard());
    }

    private List<ViewAndHolderItem> regAllSupportCard() {
        List<ViewAndHolderItem> cardList = new ArrayList<>();
        cardList.add(new AppTitleHolderView());
        cardList.add(new AppItemHolderView());
        cardList.add(new EmptyHolderView());
        return cardList;
    }

}
