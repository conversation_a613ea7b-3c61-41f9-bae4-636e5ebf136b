package com.twf.develophelpertools.adapter.database;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.base.BaseSingleCardAdapter;
import com.twf.develophelpertools.util.KitScaleUtil;

import java.util.List;

/**
 * Created by guofeng
 * on 2019/6/21.
 */

public class TableValuesAdapter extends BaseSingleCardAdapter<List<String>> {


    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder viewHolder;
        if (convertView == null) {
            convertView = LayoutInflater.from(Kit.getInstance().getApplication()).inflate(R.layout.kit_table_value_item, null);
            viewHolder = new ViewHolder(convertView, getColunmCount(position));
            convertView.setTag(viewHolder);
        } else {
            viewHolder = (ViewHolder) convertView.getTag();
        }
        List<String> valueList = getItem(position);
        if (valueList != null) {
            int size = valueList.size();
            for (int i = 0; i < size; i++) {
                View child = viewHolder.mContainer.getChildAt(i);
                if (child == null) continue;
                if (child instanceof TextView) {
                    final TextView childView = ((TextView) child);
                    childView.setText(valueList.get(i));
                    childView.setSingleLine(true);
                    childView.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            childView.setSingleLine(false);
                        }
                    });
                }
            }
        }
        return convertView;
    }

    private int getColunmCount(int position) {
        List<String> item = getItem(position);
        return item == null ? 0 : item.size();
    }

    private static class ViewHolder {

        private LinearLayout mContainer;

        private LinearLayout.LayoutParams params;

        public ViewHolder(View convertView, int count) {
            mContainer = convertView.findViewById(R.id.mContainer);
            mContainer.removeAllViews();
            for (int i = 0; i < count; i++) {
                TextView textView = new TextView(convertView.getContext());
                if (params == null) {
                    params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);

                    if (count <= 4) {
                        params.width = KitScaleUtil.getWidthPixels(Kit.getInstance().getApplication()) / count;
                    } else {
                        params.width = KitScaleUtil.getWidthPixels(Kit.getInstance().getApplication()) / 3;
                    }

                }
                int padding = KitScaleUtil.dip2px(3);
                textView.setPadding(padding, padding, padding, padding);
                textView.setSingleLine();
                mContainer.addView(textView, params);
            }
        }
    }


}
