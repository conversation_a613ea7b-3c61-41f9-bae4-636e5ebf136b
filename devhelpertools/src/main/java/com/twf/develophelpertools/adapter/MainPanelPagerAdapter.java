package com.twf.develophelpertools.adapter;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import java.util.ArrayList;
import java.util.List;

/**
 * create by guofeng
 * date on 2021/8/4
 */

public class MainPanelPagerAdapter extends FragmentPagerAdapter {


    private final List<Fragment> result = new ArrayList<>();

    public void setData(List<Fragment> data) {
        result.clear();
        if (data != null) {
            result.addAll(data);
        }
        notifyDataSetChanged();
    }


    public MainPanelPagerAdapter(FragmentManager fm) {
        super(fm);
    }

    public Fragment getItem(int position) {
        return result.get(position);
    }

    @Override
    public int getCount() {
        return result.size();
    }
}