package com.twf.develophelpertools.adapter;

import android.view.View;
import android.widget.TextView;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.adapter.base.BaseKitHolder;

/**
 * Created by guo<PERSON>
 * on 2019/5/30.
 */

public class EmptyHolderView implements ViewAndHolderItem {

    @Override
    public boolean isSupport(Object o) {
        return true;
    }

    @Override
    public BaseKitHolder createHolder(View convertView) {
        return new EmptyHolder();
    }

    @Override
    public View createConvertView() {
        return new TextView(Kit.getInstance().getApplication());
    }

    public class EmptyHolder extends BaseKitHolder {

        @Override
        protected void initValue(Object o) {

        }
    }
}
