package com.twf.develophelpertools.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.TextView;
import android.widget.Toast;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.base.BaseSingleCardAdapter;
import com.twf.develophelpertools.bean.DevMateBean;
import com.twf.develophelpertools.bean.JiraItemBean;
import com.twf.develophelpertools.util.KitForegroundUtil;

import java.util.List;

/**
 * create by guofeng
 * date on 2021/5/17
 */

public class AddIssueAdapter extends BaseSingleCardAdapter<JiraItemBean> {

    public interface OnItemCheckCallBack {
        void onCheckListener(JiraItemBean itemBean,int position);
    }

    private OnItemCheckCallBack callBack;

    public void setCallBack(OnItemCheckCallBack callBack) {
        this.callBack = callBack;
    }

    @Override
    public View getView(final int position, View view, ViewGroup parent) {
        ViewHolder holder;

        if (view == null) {
            view = LayoutInflater.from(Kit.getInstance().getApplication()).inflate(R.layout.kit_add_issue_item, null);
            holder = new ViewHolder(view);
            view.setTag(holder);
        } else {
            holder = (ViewHolder) view.getTag();
        }
        final JiraItemBean item = getItem(position);
        if (item != null) {
            holder.mTitle.setText(item.getTitle());
            holder.mDesc.setText(item.getDesc());
            holder.mDevMates.setText(getMateText(item.getMateList()));
            holder.mCheckView.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    if (isChecked) {
                        Toast.makeText(KitForegroundUtil.getInstance().getTopActivity(), "保存成功", Toast.LENGTH_SHORT).show();
                        if (callBack != null) {
                            callBack.onCheckListener(item,position);
                        }
                    }
                }
            });
        }

        return view;
    }


    private String getMateText(List<DevMateBean> mateList) {
        if (mateList == null || mateList.isEmpty()) {
            return "开发同学";
        }
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("开发同学");
        stringBuilder.append(":");
        for (DevMateBean devMateBean : mateList) {
            stringBuilder.append(devMateBean.getName());
            stringBuilder.append(',');
        }
        return stringBuilder.toString();
    }



    private static class ViewHolder {

        private TextView mTitle;
        private TextView mDesc;
        private TextView mDevMates;
        private CheckBox mCheckView;

        public ViewHolder(View convertView) {
            this.mTitle = convertView.findViewById(R.id.mTitle);
            this.mDesc = convertView.findViewById(R.id.mDesc);
            this.mDevMates = convertView.findViewById(R.id.mDevMates);
            this.mCheckView = convertView.findViewById(R.id.mCheckView);
        }
    }
}