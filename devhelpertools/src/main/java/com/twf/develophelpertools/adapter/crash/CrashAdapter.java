package com.twf.develophelpertools.adapter.crash;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.base.BaseSingleCardAdapter;

import java.io.File;

/**
 * Created by guofeng
 * on 2019/6/24.
 */

public class CrashAdapter extends BaseSingleCardAdapter<File> {

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        if (convertView == null) {
            convertView = LayoutInflater.from(Kit.getInstance().getApplication()).inflate(R.layout.kit_crash_menu_item, null);
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }
        File item = getItem(position);
        holder.mTextView.setText(item.getName());
        return convertView;
    }

    private static class ViewHolder {

        private TextView mTextView;

        public ViewHolder(View convertView) {
            this.mTextView = convertView.findViewById(R.id.mTextView);
        }
    }
}
