package com.twf.develophelpertools.adapter.sendbox;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.base.BaseSingleCardAdapter;
import com.twf.develophelpertools.util.KitFileUtil;

import java.io.File;

/**
 * Created by guo<PERSON>
 * on 2019/6/5.
 */

public class FilePreviewAdapter extends BaseSingleCardAdapter<File> {

    private Context context;

    public FilePreviewAdapter(Context context) {
        this.context = context;
    }

    @Override
    public View getView(int i, View view, ViewGroup viewGroup) {
        Holder holder;
        if (view == null) {
            view = LayoutInflater.from(context).inflate(R.layout.kit_send_box_item, null);
            holder = new Holder(view);
            view.setTag(holder);
        } else {
            holder = (Holder) view.getTag();
        }

        File item = getItem(i);
        if (item != null) {
            holder.mContent.setText(item.getName());
            if (item.isDirectory()) {
                holder.mIcon.setImageResource(R.drawable.kit_dk_dir_icon);
                holder.mMore.setVisibility(View.VISIBLE);
            } else {
                holder.mMore.setVisibility(View.GONE);
                String suffix = KitFileUtil.getSuffix(item);
                if (suffix.equals(KitFileUtil.JPG)) {
                    holder.mIcon.setImageResource(R.drawable.kit_dk_jpg_icon);
                } else if (suffix.equals(KitFileUtil.TXT)) {
                    holder.mIcon.setImageResource(R.drawable.kit_dk_txt_icon);
                } else if (suffix.equals(KitFileUtil.DB)) {
                    holder.mIcon.setImageResource(R.drawable.kit_dk_file_db);
                } else {
                    holder.mIcon.setImageResource(R.drawable.kit_dk_file_icon);
                }
            }
        }
        return view;
    }


    private static class Holder {

        private ImageView mIcon;
        private ImageView mMore;
        private TextView mContent;
        private LinearLayout mRootView;

        public Holder(View convertView) {
            mIcon = convertView.findViewById(R.id.mIcon);
            mMore = convertView.findViewById(R.id.mMore);
            mContent = convertView.findViewById(R.id.mContent);
            mRootView = convertView.findViewById(R.id.mRootView);
        }
    }
}
