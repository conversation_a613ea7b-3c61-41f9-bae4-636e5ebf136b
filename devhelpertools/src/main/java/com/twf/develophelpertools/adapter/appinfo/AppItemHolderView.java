package com.twf.develophelpertools.adapter.appinfo;

import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.ViewAndHolderItem;
import com.twf.develophelpertools.adapter.base.BaseKitHolder;
import com.twf.develophelpertools.bean.AppInfo;

/**
 * Created by guofeng
 * on 2019/5/30.
 */

public class AppItemHolderView implements ViewAndHolderItem<AppInfo, AppItemHolderView.AppItemHolder> {


    @Override
    public boolean isSupport(AppInfo appInfo) {

        return appInfo.getType()==AppInfo.TYPE_CONTENT;
    }

    @Override
    public AppItemHolder createHolder(View convertView) {

        return new AppItemHolder(convertView);
    }


    @Override
    public View createConvertView() {
        return LayoutInflater.from(Kit.getInstance().getApplication()).inflate(R.layout.kit_app_info_title_item_item, null);
    }


    public static class AppItemHolder extends BaseKitHolder<AppInfo> {

        private TextView mTitleView;
        private TextView mDescView;

        public AppItemHolder(View convertView) {
            mTitleView = convertView.findViewById(R.id.mTitle);
            mDescView = convertView.findViewById(R.id.mDesc);
        }

        @Override
        protected void initValue(AppInfo appInfo) {
            mTitleView.setText(appInfo.getTitle());
            mDescView.setText(appInfo.getDesc());
        }

    }
}
