package com.twf.develophelpertools.adapter;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.TextView;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.base.BaseSingleCardAdapter;
import com.twf.develophelpertools.bean.DevMateBean;
import com.twf.develophelpertools.model.DevMateListViewModel;

import java.util.List;

/**
 * create by guofeng
 * date on 2021/5/8
 */

public class DevMateMemberAdapter extends BaseSingleCardAdapter<DevMateBean> {

    @Override
    public View getView(int position, View view, ViewGroup parent) {

        ViewHolder holder;

        if (view == null) {
            view = LayoutInflater.from(Kit.getInstance().getApplication()).inflate(R.layout.kit_dev_mate_item, null);
            holder = new ViewHolder(view);
            view.setTag(holder);
        } else {
            holder = (ViewHolder) view.getTag();
        }

        final DevMateBean item = getItem(position);

        holder.mName.setText(item.getName());
        holder.mGroup.setText(item.getmGroupName());
        holder.mCheckView.setChecked(item.isSelect());
        holder.mCheckView.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {

                List<DevMateBean> value1 = DevMateListViewModel.createViewModel().sourceDevMateViewModel.getValue();
                if (value1 != null) {
                    for (DevMateBean mateBean : value1) {
                        if (mateBean == null) continue;
                        if (TextUtils.equals(mateBean.getName(), item.getName())) {
                            mateBean.setSelect(isChecked);
                        }
                    }
                }

            }
        });

        return view;
    }


    private static class ViewHolder {

        private TextView mName;
        private TextView mGroup;
        private CheckBox mCheckView;

        public ViewHolder(View convertView) {
            this.mName = convertView.findViewById(R.id.mName);
            this.mGroup = convertView.findViewById(R.id.mGroup);
            this.mCheckView = convertView.findViewById(R.id.mCheckView);
        }
    }
}