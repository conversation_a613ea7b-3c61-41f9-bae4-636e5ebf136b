package com.twf.develophelpertools.adapter.analy;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.kit.baselibrary.AppCallBackItf;
import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.bean.AnalyseInfoBean;
import com.twf.develophelpertools.model.fragment.AnalyseMainFragment;
import com.twf.develophelpertools.util.KitSpUtil;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import okhttp3.FormBody;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class BgAnalyseIterceptor implements Interceptor {

    private static final String TAG = "BgAnalyseIterceptor";

    public static final String ANALYSE_KEY = "ANALYSE_KEY";
    public static final String JSON_ACTION = "action";
    public static final String JSON_VALUE = "value";
    public static final String JSON_SUCCESS = "success";
    public static final String JSON_FAILED = "failed";
    public static final String JSON_TIME = "time";


    private static List<AnalyseInfoBean> analyseInfoBeans = new ArrayList<>();



    public static List<AnalyseInfoBean> getAnalyseInfoBeans() {

        return new ArrayList<>(analyseInfoBeans);
    }



    @Override
    public Response intercept(Chain chain) throws IOException {
        Response response;
        try {
            final Request request = chain.request();
            response = chain.proceed(request);
            AnalyseInfoBean analyseInfoBean = new AnalyseInfoBean();

            AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
            if (appCallBackItf != null && !appCallBackItf.isInterceptRequest(request.headers(), response.body(), request.url().toString())) {
                //属于打点的url
                if (isAnalyseUrl(request)) {
                    final HttpUrl url = request.url();

                    //获得key对应的Value集合
                    //打点成功
                    if (response.isSuccessful()) {
                        //保存成功数据到集合
                        //输出成功日志
                        systemOutSuccessLog(request, url);
                        //Toast提示成功信息
                        final String encryValue = getActionMessage(request);
                        final String decryBgAction = appCallBackItf.getDecryBgAction(encryValue, url.toString());

                        showSucceedToast(new Runnable() {
                            @Override
                            public void run() {
                                if (!TextUtils.isEmpty(decryBgAction)) {
                                    Toast.makeText(Kit.getInstance().getApplication(), decryBgAction, Toast.LENGTH_SHORT).show();
                                }
                            }
                        });

                        analyseInfoBean.actionKey = url.toString();
                        analyseInfoBean.successDesc = "成功";
                    } else {//打点失败
                        //输出失败日志
                        systemOutErrorLog(response);
                        //保存失败数据到集合
                        //Toast提示失败信息
                        //Toast提示成功信息
                        final String encryValue = getActionMessage(request);
                        final String decryBgAction = appCallBackItf.getDecryBgAction(encryValue, url.toString());
                        analyseInfoBean.successDesc = "失败";
                        analyseInfoBean.actionKey = decryBgAction;
                    }
                    analyseInfoBeans.add(analyseInfoBean);
                }
            }


        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }

        return response;

    }



    //获得打点的信息
    private String getActionMessage(Request request) {
        RequestBody body = request.body();
        if (body instanceof FormBody) {
            FormBody formBody = (FormBody) body;
            if (formBody.size() > 0) {
                return formBody.value(0);
            }
        }
        return "";
    }


    //Toast提示成功信息
    private void showSucceedToast(Runnable runnable) {
        if (showToast()) {
            mHandler.post(runnable);
        }
    }

    private final Handler mHandler = new Handler(Looper.getMainLooper());

    //输出失败日志
    private void systemOutErrorLog(Response response) {
        String errorMsg = response.message();
        Log.d(TAG, "errorMsg: " + errorMsg);
    }

    //输出成功日志
    private void systemOutSuccessLog(Request request, HttpUrl url) {
        Headers headers = request.headers();
        String method = request.method();
        Log.d(TAG, "headers: " + headers);
        Log.d(TAG, "method: " + method);
        Log.d(TAG, "url: " + url);
    }




    //是否属于打点的url
    private boolean isAnalyseUrl(Request request) {
        List<String> analyseUrls = null;
        AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
        if (appCallBackItf != null) {
            analyseUrls = appCallBackItf.getAnalysisUrlList();
        }
        if (analyseUrls != null) {
            HttpUrl url = request.url();
            for (String item : analyseUrls) {
                if (url.url().toString().equals(item)) {
                    return true;
                }
            }
        }
        return false;
    }


    //是否显示Toast
    private boolean showToast() {
        return KitSpUtil.getBoolean(AnalyseMainFragment.SP_SHOW_TOAST, false);
    }


}
