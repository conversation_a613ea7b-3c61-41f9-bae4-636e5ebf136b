package com.twf.develophelpertools.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.base.BaseSingleCardAdapter;
import com.twf.develophelpertools.bean.PermissionBean;

/**
 * create by guofeng
 * date on 2021/7/31
 */

public class PermissionAdapter extends BaseSingleCardAdapter<PermissionBean> {

    private Context context;

    public PermissionAdapter(Context context) {
        this.context = context;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {

        ViewHolder holder;

        if (convertView == null) {
            convertView = LayoutInflater.from(context).inflate(R.layout.kit_permission_item, null);
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }
        PermissionBean permissionBean = getSourceData().get(position);
        holder.mName.setText(permissionBean.permissionName);
        holder.mContent.setText(permissionBean.permissionContent);

        if (permissionBean.hasPermission) {
            holder.mPermissionStatus.setText("权限:允许");
        } else {
            holder.mPermissionStatus.setText("权限:禁止");
        }
        return convertView;
    }

    private static class ViewHolder {

        private TextView mName;

        private TextView mContent;

        private TextView mPermissionStatus;

        public ViewHolder(View convertView) {
            this.mName = convertView.findViewById(R.id.mName);
            this.mContent = convertView.findViewById(R.id.mContent);
            this.mPermissionStatus = convertView.findViewById(R.id.mPermissionStatus);
        }
    }
}