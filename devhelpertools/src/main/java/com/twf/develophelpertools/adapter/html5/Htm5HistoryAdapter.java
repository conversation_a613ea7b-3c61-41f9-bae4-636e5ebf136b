package com.twf.develophelpertools.adapter.html5;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.base.BaseSingleCardAdapter;

/**
 * Created by guofeng
 * on 2019/6/24.
 */

public class Htm5HistoryAdapter extends BaseSingleCardAdapter<String> {

    @Override
    public View getView(final int position, View convertView, ViewGroup parent) {

        ViewHolder viewHolder;

        if (convertView == null) {
            convertView = LayoutInflater.from(Kit.getInstance().getApplication()).inflate(R.layout.kit_html_url_item, null);
            viewHolder = new ViewHolder(convertView);
            convertView.setTag(viewHolder);
        } else {
            viewHolder = (ViewHolder) convertView.getTag();
        }
        viewHolder.mUrl.setText(getItem(position));
        viewHolder.mUrl.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemCallBack != null) {
                    onItemCallBack.onItemClickListener(getItem(position));
                }
            }
        });

        return convertView;
    }

    private static class ViewHolder {

        private TextView mUrl;

        public ViewHolder(View convertView) {
            this.mUrl = convertView.findViewById(R.id.mUrl);
        }
    }

    private OnItemCallBack onItemCallBack;

    public void setOnItemCallBack(OnItemCallBack onItemCallBack) {
        this.onItemCallBack = onItemCallBack;
    }

    public interface OnItemCallBack {
        void onItemClickListener(String url);
    }
}
