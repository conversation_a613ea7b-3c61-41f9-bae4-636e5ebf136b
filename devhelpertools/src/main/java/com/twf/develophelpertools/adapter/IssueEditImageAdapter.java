package com.twf.develophelpertools.adapter;

import android.app.Activity;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.facebook.drawee.view.SimpleDraweeView;
import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.base.BaseSingleCardAdapter;
import com.twf.develophelpertools.bean.JiraImageBean;
import com.twf.develophelpertools.util.KitForegroundUtil;
import com.twf.develophelpertools.util.KitFrescoUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * create by guofeng
 * date on 2021/5/8
 */

public class IssueEditImageAdapter extends BaseSingleCardAdapter<JiraImageBean> {


    public interface OnPickImageCallBack {

        void onPickImageListener();

        void onDeleteImageListener(JiraImageBean imageBean);
    }

    private OnPickImageCallBack onPickImageCallBack;

    public void setOnPickImageCallBack(OnPickImageCallBack onPickImageCallBack) {
        this.onPickImageCallBack = onPickImageCallBack;
    }


    private final List<JiraImageBean> result = new ArrayList<>();


    @Override
    public void setData(List<JiraImageBean> data) {

        result.clear();

        if (data != null) {
            result.addAll(data);
        }

        //添加新的add图标
        JiraImageBean imageBean = new JiraImageBean();
        imageBean.setAdd(true);
        result.add(imageBean);

        super.setData(result);
    }



    @Override
    public View getView(final int position, View view, ViewGroup parent) {

        ViewHolder holder;

        if (view == null) {
            view = LayoutInflater.from(Kit.getInstance().getApplication()).inflate(R.layout.kit_jira_item_large_imageview, null);
            holder = new ViewHolder(view);
            view.setTag(holder);
        } else {
            holder = (ViewHolder) view.getTag();
        }
        final JiraImageBean item = getItem(position);
        if (item != null) {

            view.setOnClickListener(null);
            holder.mIconView.setOnClickListener(null);
            holder.mVideoText.setVisibility(View.INVISIBLE);

            if (item.isAdd()) {
                holder.mDelete.setVisibility(View.GONE);
                holder.mIconView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (onPickImageCallBack != null) {
                            onPickImageCallBack.onPickImageListener();
                        }
                    }
                });
                KitFrescoUtils.getInstance().loadRes(holder.mIconView, R.drawable.kit_add_image);

            } else {

                holder.mVideoText.setVisibility(View.VISIBLE);
                if (TextUtils.equals(item.getFileType(), JiraImageBean.VIDEO)) {
                    holder.mVideoText.setText("视频");
                } else {
                    holder.mVideoText.setText("图片");
                }

                holder.mDelete.setVisibility(View.VISIBLE);
                holder.mDelete.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (onPickImageCallBack != null) {
                            onPickImageCallBack.onDeleteImageListener(item);
                        }
                    }
                });

                holder.mIconView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        //跳转到预览界面
                        jumpToPreviewPage(position);
                    }
                });

                String localPath = item.getLocalPath();
                File file = new File(localPath);//加载本地
                if (file.exists()) {
                    KitFrescoUtils.getInstance().loadLocalFile(holder.mIconView, localPath);
                    return view;
                }

                if (!TextUtils.isEmpty(item.getUrl())) {//加载网络
                    KitFrescoUtils.getInstance().loadUri(holder.mIconView, item.getUrl());
                }
            }
        }
        return view;
    }

    /**
     * 跳转到预览界面
     *
     * @param index
     */
    private void jumpToPreviewPage(int index) {
        Activity topActivity = KitForegroundUtil.getInstance().getTopActivity();
        if (topActivity == null) return;

        //跳转到预览界面
        final List<JiraImageBean> result = new ArrayList<>();
        List<JiraImageBean> sourceData = getSourceData();
        if (sourceData != null) {
            for (JiraImageBean sourceDatum : sourceData) {
                if (sourceDatum.isAdd()) continue;
                result.add(sourceDatum);
            }
        }
//        PreviewMedialActivity.start(topActivity, result, index);
    }


    private static class ViewHolder {

        private SimpleDraweeView mIconView;

        private ImageView mDelete;

        private TextView mVideoText;

        public ViewHolder(View convertView) {
            this.mIconView = convertView.findViewById(R.id.mImageView);
            this.mDelete = convertView.findViewById(R.id.mDelete);
            this.mVideoText = convertView.findViewById(R.id.mPlayView);
        }
    }

}