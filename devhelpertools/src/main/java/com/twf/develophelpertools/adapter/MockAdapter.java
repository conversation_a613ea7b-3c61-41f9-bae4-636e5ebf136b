package com.twf.develophelpertools.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.TextView;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.base.BaseSingleCardAdapter;
import com.twf.develophelpertools.model.mock.DevelopMockUtil;

/**
 * Created by guofeng
 * on 2019/7/18.
 */

public class MockAdapter extends BaseSingleCardAdapter<String> {


    private Context context;

    public MockAdapter(Context context) {
        this.context = context;
    }


    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        if (convertView == null) {
            convertView = LayoutInflater.from(context).inflate(R.layout.kit_mock_item, null);
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }

        final String url = getItem(position);
        holder.mTextView.setText(url);

        holder.mCheckView.setOnCheckedChangeListener(null);

        holder.mCheckView.setChecked(DevelopMockUtil.containMock(url));

        holder.mCheckView.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (buttonView.isPressed()) {

                    if (isChecked) {
                        DevelopMockUtil.getInstance().reflateSelectMockUrl(url);
                    } else {
                        DevelopMockUtil.getInstance().reflateUnSelectMockUrl(url);
                    }

                    notifyDataSetChanged();
                }
            }
        });


        return convertView;
    }

    private static class ViewHolder {

        private TextView mTextView;
        private CheckBox mCheckView;

        public ViewHolder(View convertView) {
            this.mTextView = convertView.findViewById(R.id.mUrl);
            this.mCheckView = convertView.findViewById(R.id.mCheckView);
        }
    }


}
