package com.twf.develophelpertools.adapter.sendbox;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.Editable;
import android.text.InputType;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.ViewAndHolderItem;
import com.twf.develophelpertools.adapter.base.BaseKitHolder;
import com.twf.develophelpertools.bean.BaseSpInfo;
import com.twf.develophelpertools.bean.FloatSpInfo;
import com.twf.develophelpertools.bean.IntegerSpInfo;
import com.twf.develophelpertools.bean.LongSpInfo;

/**
 * Created by guofeng
 * on 2019/6/6.
 */

public class LongViewHolderFactory implements ViewAndHolderItem<BaseSpInfo, LongViewHolderFactory.LongViewHolder> {

    private Context context;

    private SharedPreferences.Editor editor;

    public LongViewHolderFactory(Context context, SharedPreferences.Editor editor) {
        this.context = context;
        this.editor = editor;
    }

    @Override
    public boolean isSupport(BaseSpInfo baseSpInfo) {
        return baseSpInfo instanceof LongSpInfo ||
                baseSpInfo instanceof FloatSpInfo ||
                baseSpInfo instanceof IntegerSpInfo;
    }

    @Override
    public LongViewHolder createHolder(View convertView) {
        return new LongViewHolder(convertView, editor);
    }

    @Override
    public View createConvertView() {
        return LayoutInflater.from(context).inflate(R.layout.kit_send_box_sp_long_item, null);
    }

    public static class LongViewHolder extends BaseKitHolder<BaseSpInfo> {


        private SharedPreferences.Editor editor;
        private final TextView mKeyName;
        private final TextView mValueClass;
        private final EditText mValue;

        public LongViewHolder(View convertView, SharedPreferences.Editor editor) {
            this.editor = editor;
            mKeyName = convertView.findViewById(R.id.mKeyName);
            mValueClass = convertView.findViewById(R.id.mValueClass);
            mValue = convertView.findViewById(R.id.mValue);
            mValue.setInputType(InputType.TYPE_CLASS_NUMBER);
        }

        @Override
        protected void initValue(final BaseSpInfo baseSpInfo) {

            if (baseSpInfo instanceof LongSpInfo) {
                final LongSpInfo longSpInfo = (LongSpInfo) baseSpInfo;
                mKeyName.setText(longSpInfo.getKey());
                mValueClass.setText("Long");
                mValue.setText(String.valueOf(longSpInfo.getValue()));
                mValue.addTextChangedListener(new TextWatcher() {
                    @Override
                    public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                    }

                    @Override
                    public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                    }

                    @Override
                    public void afterTextChanged(Editable editable) {
                        try {
                            String text = editable.toString();
                            long lResult = Long.parseLong(text);
                            longSpInfo.setValue(lResult);
                            editor.putLong(longSpInfo.getKey(), lResult);
                        } catch (Exception e) {

                        }
                    }
                });
            }
            if (baseSpInfo instanceof FloatSpInfo) {
                final FloatSpInfo floatSpInfo = (FloatSpInfo) baseSpInfo;
                mKeyName.setText(floatSpInfo.getKey());
                mValueClass.setText("Float");
                mValue.setText(String.valueOf(floatSpInfo.getValue()));
                mValue.addTextChangedListener(new TextWatcher() {
                    @Override
                    public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                    }

                    @Override
                    public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                    }

                    @Override
                    public void afterTextChanged(Editable editable) {
                        try {
                            String text = editable.toString();
                            Float fResult = Float.parseFloat(text);
                            floatSpInfo.setValue(fResult);
                            editor.putFloat(floatSpInfo.getKey(), fResult);
                        } catch (Exception e) {

                        }
                    }
                });
            }
            if (baseSpInfo instanceof IntegerSpInfo) {
                final IntegerSpInfo integerSpInfo = (IntegerSpInfo) baseSpInfo;
                mKeyName.setText(integerSpInfo.getKey());
                mValueClass.setText("Integer");
                mValue.setText(String.valueOf(integerSpInfo.getValue()));
                mValue.addTextChangedListener(new TextWatcher() {
                    @Override
                    public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                    }

                    @Override
                    public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                    }

                    @Override
                    public void afterTextChanged(Editable editable) {
                        try {
                            String text = editable.toString();
                            Integer iResult = Integer.parseInt(text);
                            integerSpInfo.setValue(iResult);
                            editor.putInt(integerSpInfo.getKey(), iResult);
                        } catch (Exception e) {

                        }
                    }
                });
            }


        }
    }

}
