package com.twf.develophelpertools.adapter.database;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.base.BaseSingleCardAdapter;

/**
 * Created by guofeng
 * on 2019/6/21.
 */

public class TableNameAdapter extends BaseSingleCardAdapter<String> {

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder viewHolder;
        if (convertView == null) {
            convertView = LayoutInflater.from(Kit.getInstance().getApplication()).inflate(R.layout.kit_table_name_item, null);
            viewHolder = new ViewHolder(convertView);
            convertView.setTag(viewHolder);
        } else {
            viewHolder = (ViewHolder) convertView.getTag();
        }
        viewHolder.tableName.setText(getItem(position));
        return convertView;
    }

    private static class ViewHolder {

        TextView tableName;

        public ViewHolder(View convertView) {
            this.tableName = convertView.findViewById(R.id.mTableName);
        }
    }
}
