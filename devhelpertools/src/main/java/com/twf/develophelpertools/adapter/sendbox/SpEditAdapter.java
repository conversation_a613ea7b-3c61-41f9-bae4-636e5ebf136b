package com.twf.develophelpertools.adapter.sendbox;

import android.content.Context;
import android.content.SharedPreferences;

import com.twf.develophelpertools.adapter.EmptyHolderView;
import com.twf.develophelpertools.adapter.ViewAndHolderItem;
import com.twf.develophelpertools.adapter.base.BaseMultiCardAdapter;
import com.twf.develophelpertools.bean.BaseSpInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by guofeng
 * on 2019/6/6.
 */

public class SpEditAdapter extends BaseMultiCardAdapter {


    public SpEditAdapter(Context context, SharedPreferences.Editor editor) {
        super(context);
        registerSupportItem(regAllSupportCard(editor));
    }


    private List<ViewAndHolderItem> regAllSupportCard(SharedPreferences.Editor editor) {
        List<ViewAndHolderItem> result = new ArrayList<>();
        result.add(new BooleanViewHolderFactory(getm<PERSON>ontext(), editor));
        result.add(new StringViewHolderFactory(getm<PERSON>ontext(), editor));
        result.add(new LongViewHolderFactory(getmContext(), editor));
        result.add(new EmptyHolderView());
        return result;
    }

}
