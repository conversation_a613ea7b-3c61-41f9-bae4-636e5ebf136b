package com.twf.develophelpertools.longterm;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.view.MotionEvent;
import androidx.appcompat.widget.ContentFrameLayout;

/**
 * 模拟长文本，代理ContentView，拦截touch事件
 *
 * <AUTHOR> on 2021/12/07.
 */
@SuppressLint("RestrictedApi")
public class ProxyContentLayout extends ContentFrameLayout {

    private OnLongTermTouchEventCallback callback;

    public ProxyContentLayout(Activity activity, OnLongTermTouchEventCallback callback) {
        super(activity);
        this.callback = callback;
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent event) {
        if (callback != null) {
            callback.dispatchTouchEvent(event);
        }
        return super.onInterceptTouchEvent(event);
    }

}
