package com.twf.develophelpertools.longterm;

import android.app.Activity;
import android.app.Application;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.util.KitSpUtil;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.twf.develophelpertools.model.fragment.SwitchConfigFragment.SP_LONG_TERM_SIMULATE;

/**
 * 模拟长文本，touch事件处理
 *
 * <AUTHOR> on 2021/12/07.
 */
public class LongTermTouchEventWatcher {

    private boolean inited = false;
    private DecorViewLifecycle decorViewLifecycle;

    private int mDownX;
    private int mDownY;

    private Map<String, Set<TextViewBean>> activityMap = new HashMap<>();
    private Set<TextViewBean> curActivityBeanSet;

    private static final String APPEND_STRING = "测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试";

    public void init(Application application) {
        if (!inited) {
            decorViewLifecycle = new DecorViewLifecycle(new OnLongTermTouchEventCallback() {
                @Override
                public void dispatchTouchEvent(MotionEvent event) {
                    boolean isOpenLongTermSimulate = KitSpUtil.getBoolean(SP_LONG_TERM_SIMULATE, false);
                    if (!isOpenLongTermSimulate) return;

                    switch (event.getAction()) {
                        case MotionEvent.ACTION_DOWN:
                            mDownX = (int) event.getX();
                            mDownY = (int) event.getY();

                            Activity topActivity = decorViewLifecycle.getTopActivity();
                            ViewGroup decorView = (ViewGroup) topActivity.getWindow().getDecorView();
                            // 遍历decorView找到当前手指位置所在的TextView子类
                            Rect rect = new Rect();

                            for (String mActivityName : activityMap.keySet()) {
                                if (TextUtils.equals(mActivityName, topActivity.getClass().getCanonicalName())) {
                                    curActivityBeanSet = activityMap.get(mActivityName);
                                    break;
                                }
                            }

                            if (curActivityBeanSet == null) {
                                curActivityBeanSet = new HashSet<>();
                            }

                            TextViewBean foundBean = null;
                            for (TextViewBean mBean : curActivityBeanSet) {
                                if (mBean == null || mBean.textView == null) continue;
                                mBean.textView.getGlobalVisibleRect(rect);
                                if (rect.contains(mDownX, mDownY)) {// 再次点击还原
                                    mBean.textView.setText(mBean.originalContent);
                                    setForeground(mBean.textView, null);
                                    foundBean = mBean;
                                    break;
                                }
                            }

                            if (foundBean != null) {
                                curActivityBeanSet.remove(foundBean);
                            } else {
                                findTargetTextView(decorView, rect);
                            }
                            break;
                        default:
                            break;
                    }
                }

                @Override
                public void onActivityDestroyed(String activityName) {
                    for (String mActivityName : activityMap.keySet()) {
                        if (TextUtils.equals(mActivityName, activityName)) {
                            activityMap.remove(activityName);
                            break;
                        }
                    }
                }
            });
            application.registerActivityLifecycleCallbacks(decorViewLifecycle);
            inited = true;
        }
    }

    /**
     * 遍历root view，找到touch点所在的TextView
     */
    private void findTargetTextView(ViewGroup root, Rect rect) {
        for (int i = 0; i < root.getChildCount(); i++) {
            View child = root.getChildAt(i);
            if (child instanceof TextView) {
                child.getGlobalVisibleRect(rect);
                if (rect.contains(mDownX, mDownY)) {// 找到了目标View
                    TextView targetTextView = (TextView) child;

                    curActivityBeanSet.add(new TextViewBean(targetTextView, targetTextView.getText().toString()));
                    targetTextView.setText(targetTextView.getText() + APPEND_STRING);
                    setForeground(targetTextView, root.getResources().getDrawable(R.drawable.view_border_long_term));
                }
            } else if (child instanceof ViewGroup) {
                findTargetTextView((ViewGroup) child, rect);
            }
        }
    }

    private void setForeground(TextView textView, Drawable foreground) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            textView.setForeground(foreground);
        }
    }

    private class TextViewBean {
        public TextView textView;
        public String originalContent;

        public TextViewBean(TextView textView, String originalContent) {
            this.textView = textView;
            this.originalContent = originalContent;
        }
    }
}
