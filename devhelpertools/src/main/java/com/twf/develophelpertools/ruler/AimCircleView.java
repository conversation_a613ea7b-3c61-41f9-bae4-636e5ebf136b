package com.twf.develophelpertools.ruler;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.View;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.util.KitScaleUtil;

/**
 * Created by wa<PERSON><PERSON><PERSON> on 2018/12/3.
 */

public class AimCircleView extends View {


    private Paint mPaint;

    public AimCircleView(Context context) {
        super(context);
        init();
    }

    public AimCircleView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public AimCircleView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    public void init() {
        mPaint = new Paint();
        mPaint.setTextSize(KitScaleUtil.dip2px(13));
        mPaint.setAntiAlias(true);
    }


    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        drawCircle(canvas);
    }

    private void drawCircle(Canvas canvas) {
        float cx = getWidth() / 2;
        float cy = getWidth() / 2;
        float radius = getWidth() / 2;

        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setColor(Color.parseColor("#FFFFFF"));
        canvas.drawCircle(cx, cy, radius, mPaint);
        radius = KitScaleUtil.dip2px(40) / 2;
        mPaint.setColor(Color.parseColor("#30CC3A4B"));
        canvas.drawCircle(cx, cy, radius, mPaint);
        radius = KitScaleUtil.dip2px(20) / 2;
        mPaint.setColor(Color.parseColor("#CC3A4B"));
        canvas.drawCircle(cx, cy, radius, mPaint);

        radius = getWidth() / 2;
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(4);
        mPaint.setColor(Color.parseColor("#337CC4"));
        canvas.drawCircle(cx, cy, radius - 2, mPaint);

        canvas.drawText("双击关闭", 0, cy, mPaint);
    }
}