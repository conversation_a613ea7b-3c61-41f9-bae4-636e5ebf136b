package com.twf.develophelpertools.toppageinfo;

import android.content.Context;
import android.graphics.Point;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.twf.develophelpertools.toppageinfo.node.InfoQueue;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.util.KitMainHandler;

final class FloatView extends LinearLayout {
    private Context mContext;
    private TextView packageName, className;
    private LinearLayout ll_contentl;
    private Point downPoint, movePoint;
    private WindowManager mWindowManager;
    private long lastClick = 0;
    private InfoQueue queue = new InfoQueue(3);
    private String lastStr;
    private boolean isShowMore = true;
    private String nextLine = "\n";

    public FloatView(Context context) {
        super(context);
        mContext = context;
        mWindowManager = (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
        initView();
    }

    private void initView() {
        inflate(mContext, R.layout.kit_top_info_float_window, this);
        ll_contentl = findViewById(R.id.ll_content);
        packageName = findViewById(R.id.package_name);
        className = findViewById(R.id.class_name);

        ll_contentl.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                onViewClick();
            }
        });
        ll_contentl.setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        downPoint = new Point((int) event.getRawX(), (int) event.getRawY());
                        break;

                    case MotionEvent.ACTION_MOVE:
                        movePoint = new Point((int) event.getRawX(), (int) event.getRawY());
                        int dx = movePoint.x - downPoint.x;
                        int dy = movePoint.y - downPoint.y;
                        WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) FloatView.this.getLayoutParams();
                        layoutParams.x += dx;
                        layoutParams.y += dy;
                        mWindowManager.updateViewLayout(FloatView.this, layoutParams);
                        downPoint = movePoint;
                        return true;
                    case MotionEvent.ACTION_UP:
                        break;
                }
                return v.onTouchEvent(event);
            }
        });
//        className.setOnLongClickListener(new OnLongClickListener() {
//            @Override
//            public boolean onLongClick(View v) {
//                //切换显示
//                isShowMore = !isShowMore;
//                Toast.makeText(mContext, "显示最近三条：" + isShowMore, Toast.LENGTH_SHORT).show();
//                _PageInfoFinder.getInstance().refresh();
//
//                return true;
//            }
//        });

    }


    public void updateDisplay(String packageNameStr, String detailStr) {
        //保持悬浮窗显示最近三条信息
        if (lastStr != null && lastStr.equals(detailStr)) return;
        lastStr = detailStr;
        updateContent(packageNameStr, detailStr);
    }

    private void updateContent(String packageNameStr, String detailStr) {
        queue.add(detailStr);
        if (!isShowMore) {
            packageName.setText(packageNameStr);
            className.setText(removeLastNextLine(detailStr));
        } else {
            packageName.setText(packageNameStr);
            className.setText(removeLastNextLine(queue.getAllStr()));
        }
    }

    private String removeLastNextLine(String content) {
        return content.substring(0, content.length() - nextLine.length());
    }

    //根据2秒内点击次数，选择对应的操作
    private int count = 0;
    private long firstTime = 0;
    private long interval = 2000;

    private void onViewClick() {
        long secondTime = System.currentTimeMillis();
        // 判断每次点击的事件间隔是否符合连击的有效范围
        // 不符合时，有可能是连击的开始，否则就仅仅是单击
        if (secondTime - firstTime <= interval) {
            ++count;
        } else {
            count = 1;
        }
        // 延迟，用于判断用户的点击操作是否结束
        delay();
        firstTime = secondTime;
    }

    private Runnable mClickTask = new Runnable() {
        @Override
        public void run() {
            if (count <= 1) {
                _PageInfoFinder.getInstance().refresh();
            } else if (count >= 3) {
                isShowMore = !isShowMore;
                Toast.makeText(mContext, "显示最近三条：" + isShowMore, Toast.LENGTH_SHORT).show();
                _PageInfoFinder.getInstance().refresh();
            }
        }
    };

    private void delay() {
        KitMainHandler.getMainHandler().removeCallbacks(mClickTask);
        KitMainHandler.getMainHandler().postDelayed(mClickTask, 1000);
    }
}
