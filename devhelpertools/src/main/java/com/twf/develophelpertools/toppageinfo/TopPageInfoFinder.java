package com.twf.develophelpertools.toppageinfo;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.PixelFormat;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.view.Gravity;
import android.view.WindowManager;
import android.view.WindowManager.LayoutParams;
import android.widget.Toast;

import com.twf.develophelpertools.Kit;


public class TopPageInfoFinder {
    private static final LayoutParams LAYOUTPARAMS;

    static {
        LayoutParams layoutParams = new LayoutParams();
        layoutParams.x = 0;
        layoutParams.y = 0;
        layoutParams.width = LayoutParams.WRAP_CONTENT;
        layoutParams.height = LayoutParams.WRAP_CONTENT;
        layoutParams.gravity = Gravity.LEFT | Gravity.TOP;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            layoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else {
            layoutParams.type = WindowManager.LayoutParams.TYPE_PHONE;
        }
        layoutParams.format = PixelFormat.RGBA_8888;
        layoutParams.flags = LayoutParams.FLAG_NOT_TOUCH_MODAL | LayoutParams.FLAG_NOT_FOCUSABLE;
        LAYOUTPARAMS = layoutParams;
    }

    private final int PERMISSION_CODE = 1;
    public boolean isOpen = false;
    _PageInfoFinder.LifeCycleCallback LIFE_CYCLE_CALLBACK = new _PageInfoFinder.LifeCycleCallback();
    private Context mContext;
    private WindowManager mWindowManager;
    private FloatView mFloatView;

    private TopPageInfoFinder() {
//        mContext = Utils.getApp();
        mContext = Kit.getInstance().getApplication();
        mWindowManager = (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
    }

    public static TopPageInfoFinder getInstance() {
        return SingletonHolder.INSTANCE;
    }


    public boolean switchFloat(Activity activity) {
        if (!isOpen) {
            isOpen = openFloat(activity);
        } else {
            isOpen = false;
            closeFloat();
        }
        return isOpen;

    }

    private boolean openFloat(Activity activity) {
        boolean addFloatView = false;
        if (checkAlerWindowPermission(activity)) {
            addFloatView = TopPageInfoFinder.getInstance().addFloatView();
            Kit.getInstance().getApplication().registerActivityLifecycleCallbacks(LIFE_CYCLE_CALLBACK);
            updateDisplay("", "   使用说明\n   2秒内N次点击\n1次：刷新信息\n3次：切换最近一条或三条\n拖拽：View可以拖拽\n");
        }
        return addFloatView;
    }

    private void closeFloat() {
        Kit.getInstance().getApplication().unregisterActivityLifecycleCallbacks(LIFE_CYCLE_CALLBACK);
        TopPageInfoFinder.getInstance().removeFloatView();
    }


    private boolean addFloatView() {
        try {
            if (mFloatView == null) {
                mFloatView = new FloatView(mContext);
                mWindowManager.addView(mFloatView, LAYOUTPARAMS);
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }


    private void removeFloatView() {
        if (mFloatView != null) {
            mWindowManager.removeView(mFloatView);
            mFloatView = null;
        }
    }

    void updateDisplay(String packageNameStr, String classNameStr) {
        if (mFloatView != null)
            mFloatView.updateDisplay(packageNameStr, classNameStr);
    }

    private boolean checkAlerWindowPermission(Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(activity)) {
                Toast.makeText(activity, "当前无悬浮窗权限，请授权！", Toast.LENGTH_SHORT).show();
                Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse("package:" +
                        activity.getPackageName()));
                activity.startActivityForResult(intent, PERMISSION_CODE);
                return false;
            }
        }
        return true;
    }

    private static final class SingletonHolder {
        static final TopPageInfoFinder INSTANCE = new TopPageInfoFinder();
    }
}
