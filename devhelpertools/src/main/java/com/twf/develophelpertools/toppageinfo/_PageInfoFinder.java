package com.twf.develophelpertools.toppageinfo;

import android.app.Activity;
import android.app.Application;
import android.os.Bundle;
import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import com.twf.develophelpertools.toppageinfo.node.MyTreeNode;

import java.util.List;

@Keep
final class _PageInfoFinder {
    String GLIDE_FRAGMENT = "SupportRequestManagerFragment";
    String RXPERMISSION_FRAGMENT = "RxPermissionsFragment";
    private Activity currentActivity;
    private volatile boolean showFullName = false;

    @SuppressWarnings("unused")
    @Keep
    public void setShowFullName(boolean showFullName) {
        this.showFullName = showFullName;
    }

    private _PageInfoFinder() {

    }

    @Keep
    public static _PageInfoFinder getInstance() {
        return Singleton.INSTANCE;
    }

    public void refresh() {
        if (currentActivity == null) return;
        findTopPageInfo(currentActivity);
    }

    public void findTopPageInfo(Activity activity) {
        currentActivity = activity;
        boolean isCanHandler = isFragmentActivity(activity);
        if (!isCanHandler) {
            String activityName = getClassName(activity.getClass());
            addInfoToView(activity.getPackageName(), activityName);
            return;
        }

        String str = findActivityWithAllFragment((FragmentActivity) activity);
        addInfoToView(activity.getPackageName(), str);

    }

    /**
     * 遍历Actvity中所有的Fragment 及嵌套childFragment
     *
     * @param activity
     * @return
     */
    @Keep
    public String findActivityWithAllFragment(@NonNull FragmentActivity activity) {
        MyTreeNode rootNode = new MyTreeNode();
        rootNode.name = getClassName(activity.getClass());
        //获取这个Activity的类目、所有的fragment的类名
        getAllInfoInActivity(rootNode, activity);

        //按照指定格式打印ThreeNode信息
        StringBuilder sb = new StringBuilder();
        printContentFormTreeNode(sb, rootNode, 0);
        return sb.toString();
    }

    /**
     * 按照指定格式打印ThreeNode信息
     *
     * @param sb
     * @param myTreeNode
     * @param level
     */
    private void printContentFormTreeNode(StringBuilder sb, MyTreeNode myTreeNode, int level) {
        if (!isTargetNode(myTreeNode)) return;

        for (int i = 0; i < level; i++) {
            sb.append(">");
        }
        sb.append(myTreeNode.name);
        sb.append("\n");

        List<MyTreeNode> children = myTreeNode.children;
        if (children == null) return;
        for (MyTreeNode child : children) {
            printContentFormTreeNode(sb, child, level + 1);
        }
    }

    /**
     * 过滤掉 三方库 glide、rx等 添加的透明Activity
     *
     * @param myTreeNode
     * @return
     */
    private boolean isTargetNode(MyTreeNode myTreeNode) {
        if (myTreeNode == null) return false;
        String name = myTreeNode.name;
        if (GLIDE_FRAGMENT.equals(name) || RXPERMISSION_FRAGMENT.equals(name)) {
            return false;
        }
        return true;
    }


    /**
     * 获取这个Activity的类目、所有的fragment的类名
     *
     * @param rootMyTreeNode
     * @param activity
     */
    private void getAllInfoInActivity(MyTreeNode rootMyTreeNode, FragmentActivity activity) {
        FragmentManager manager = activity.getSupportFragmentManager();
        List<Fragment> fragments = manager.getFragments();
        if (fragments == null || fragments.isEmpty()) {
            return;
        }

        for (int i = 0; i < fragments.size(); i++) {
            Fragment fragment = fragments.get(i);
            if (fragment != null && fragment.isAdded() && !fragment.isHidden() && fragment.getUserVisibleHint()) {
                MyTreeNode myTreeNode = new MyTreeNode();
                getNodeInfo(myTreeNode, fragment);
                getAllChildrenInFragment(myTreeNode, fragment.getChildFragmentManager());
                rootMyTreeNode.addChild(myTreeNode);
            }
        }
    }

    /**
     * 获取Fragment中所有嵌套的ChildFragment
     *
     * @param parentMyTreeNode
     * @param childFragmentManager
     */
    private void getAllChildrenInFragment(MyTreeNode parentMyTreeNode, FragmentManager childFragmentManager) {
        if (parentMyTreeNode == null || childFragmentManager == null) return;
        List<Fragment> fragments = childFragmentManager.getFragments();

        if (fragments == null || fragments.isEmpty()) {
            return;
        }

        for (int i = 0; i < fragments.size(); i++) {
            Fragment fragment = fragments.get(i);
            if (isVisibleToUser(fragment)) {
                MyTreeNode myTreeNode = new MyTreeNode();
                getNodeInfo(myTreeNode, fragment);
                getAllChildrenInFragment(myTreeNode, fragment.getChildFragmentManager());
                parentMyTreeNode.addChild(myTreeNode);
            }
        }
    }

    private boolean isVisibleToUser(Fragment fragment) {
        return (fragment != null && fragment.isVisible());
//        return fragment != null && fragment.isAdded() && !fragment.isHidden() && fragment.getUserVisibleHint();

    }

    private void getNodeInfo(MyTreeNode myTreeNode, Fragment fragment) {
        myTreeNode.name = getClassName(fragment.getClass());
    }

    private String getClassName(@NonNull Class<?> clazz) {
        return showFullName ? clazz.getName() : clazz.getSimpleName();
    }

    private void addInfoToView(String packageName, String info) {

        TopPageInfoFinder.getInstance().updateDisplay(packageName, info);
    }


    private boolean isFragmentActivity(Activity context) {
        try {
            boolean canHandle =
                    context instanceof FragmentActivity;
            return canHandle;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    private static class Singleton {
        static final _PageInfoFinder INSTANCE = new _PageInfoFinder();
    }

    //在进行Activity切换是自动更新信息，由于部分Fragment是后添加的，有些页面需要手动刷新
    static class LifeCycleCallback implements Application.ActivityLifecycleCallbacks {
        @Override
        public void onActivityCreated(Activity activity, Bundle savedInstanceState) {

        }

        @Override
        public void onActivityStarted(Activity activity) {

        }

        @Override
        public void onActivityResumed(Activity activity) {
            _PageInfoFinder.getInstance().findTopPageInfo(activity);
        }

        @Override
        public void onActivityPaused(Activity activity) {

        }

        @Override
        public void onActivityStopped(Activity activity) {

        }

        @Override
        public void onActivitySaveInstanceState(Activity activity, Bundle outState) {

        }

        @Override
        public void onActivityDestroyed(Activity activity) {

        }
    }
}
