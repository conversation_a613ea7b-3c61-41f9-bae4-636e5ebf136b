package com.twf.develophelpertools.toppageinfo.node;

public class InfoQueue {
    private int capacity = 3;
    private int size;//当前容量
    private InfoNode head;

    public InfoQueue(int capacity) {
        this.capacity = capacity;
    }

    public void add(String info) {
        InfoNode newNode;
        if (size < capacity) {
            newNode = new InfoNode();
            size++;
        } else {
            newNode = head;
            head = head.next;
        }


        newNode.content = info;
        newNode.next = null;

        if (head == null) {
            head = newNode;
            return;
        }

        InfoNode temp = head;
        while (temp.next != null) {
            temp = temp.next;
        }
        temp.next = newNode;
    }


    public String getAllStr() {
        StringBuffer buffer = new StringBuffer();
        InfoNode temp = head;
        while (temp != null) {
            buffer.append(temp.content);
            buffer.append("----\n");
            temp = temp.next;
        }
        return buffer.toString();
    }
}
