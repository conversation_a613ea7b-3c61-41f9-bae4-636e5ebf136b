package com.twf.develophelpertools.service;

import android.app.Activity;
import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.graphics.Rect;
import android.os.IBinder;

import androidx.annotation.Nullable;

import android.util.Log;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.thread.KitThreadPool;
import com.twf.develophelpertools.util.KitForegroundUtil;
import com.twf.develophelpertools.util.KitMainHandler;
import com.twf.develophelpertools.util.KitMockClickUtil;
import com.twf.develophelpertools.util.KitStatusBarUtil;

import java.util.Random;

/**
 * create by guofeng
 * date on 2021/3/24
 */

public class MockClickService extends Service {



    private static final String KEY_DURATION = "DURATION";

    private int duration;


    public static Rect flowArea = new Rect();


    /**
     * 启动模拟点击服务
     *
     * @param context
     * @param duration
     */
    public static void startMockClickService(Context context, int duration) {
        Intent intent = new Intent(context, MockClickService.class);
        intent.putExtra(KEY_DURATION, duration);
        context.startService(intent);
    }


    @Override
    public void onCreate() {
        super.onCreate();
    }

    private int getScreenWidth() {
        return KitForegroundUtil.getInstance().getTopActivity().getWindow().getDecorView().getMeasuredWidth();
    }

    private int getScreenHeight() {
        return KitForegroundUtil.getInstance().getTopActivity().getWindow().getDecorView().getMeasuredHeight();
    }



    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        duration = intent.getIntExtra(KEY_DURATION, 0);
        Log.d(TAG, "onStartCommand:" + duration);
        if (duration > 0) {
            //开启虚拟点击是事件
            startMockClickListener();
            //在一个界面停留超过15s，手动执行下返回操作
            startMonitorTopActivity();
        }
        return super.onStartCommand(intent, flags, startId);
    }



    /**
     * 判断app是否在后台,如果在后台重新启动
     */

    private boolean checkAppBackToForeground() {
        if (!KitForegroundUtil.getInstance().isAppForeground()) {
            Activity rootActivity = KitForegroundUtil.getInstance().getRootActivity();
            Intent intent = new Intent("android.intent.action.MAIN");
            intent.setComponent(new ComponentName(getApplicationContext().getPackageName(), rootActivity.getClass().getName()));
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            rootActivity.startActivity(intent);
            Log.d(TAG, "app当前在后台:快速启动中 ");
            return true;
        }
        return false;
    }


    public static final int statusHeight = KitStatusBarUtil.getStatusBarHeight(Kit.getInstance().getApplication());


    private boolean isBackToForeground() {
        //app在后台先切换回来
        if (checkAppBackToForeground()) {
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            return true;
        }
        return false;
    }

    /**
     * 开启虚拟点击是事件
     */
    private void startMockClickListener() {
        KitThreadPool.executor(new Runnable() {
            @Override
            public void run() {
                while (duration >= 0) {

                    int clickLoop = 10;

                    while (clickLoop > 0) {
                        if (isBackToForeground()) continue;
                        int randomX = getRandomX();
                        int randomY = getRandomY();
                        if (isClickFloatView(randomX, randomY)) continue;
                        KitMockClickUtil.onClickEvent(randomX, randomY);
                        try {
                            Thread.sleep(100);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                        switch (mRandom.nextInt(4)) {
                            case 1:
                                KitMockClickUtil.onFlingerLeft(randomX, randomY);
                                break;
                            case 2:
                                KitMockClickUtil.onFlingerRight(randomX, randomY);
                                break;
                            case 3:
                                KitMockClickUtil.onFlingerDown(randomX, randomY);
                                break;
                            default: {
                                KitMockClickUtil.onFlingerUp(randomX, randomY);
                            }
                        }
                        clickLoop--;
                    }
                    Log.d(TAG, "run: 执行中");
                    duration--;
                }

                Log.d(TAG, "全部执行完毕: " + duration + "s");
            }
        });
    }


    private boolean isClickFloatView(int randomX, int randomY) {
        if (randomX >= flowArea.left - statusHeight
                && randomX <= flowArea.right + statusHeight
                && randomY >= flowArea.top - statusHeight
                && randomY <= flowArea.bottom + statusHeight
        ) {
            Log.d(TAG, "点击了悬浮球区域: " + randomX + ":" + randomY);
            return true;
        }
        return false;
    }


    private Activity topActivity;

    /**
     * 在一个界面停留超过5s，手动执行下返回操作
     */
    private void startMonitorTopActivity() {
        if (topActivity == null) {
            topActivity = KitForegroundUtil.getInstance().getTopActivity();
        } else {
            Activity currentTopActivity = KitForegroundUtil.getInstance().getTopActivity();
            //在一个页面停留时间太长了，手动执行下返回操作
            if (topActivity == currentTopActivity) {
                if (KitForegroundUtil.getInstance().getTaskActivity() > 1) {
                    if (KitForegroundUtil.getInstance().isRootActivity(topActivity)) return;
                    KitThreadPool.executor(new Runnable() {
                        @Override
                        public void run() {
                            //app在后台先切换回来
                            if (checkAppBackToForeground()) {
                                try {
                                    Thread.sleep(2000);
                                } catch (InterruptedException e) {
                                    e.printStackTrace();
                                }
                            }
                            final Activity preActivity = topActivity;
                            KitMockClickUtil.onPressBackListener();
                            //如果activity没有关闭成功,直接close关闭
                            KitMainHandler.getMainHandler().postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    if (KitForegroundUtil.getInstance().getTopActivity() == preActivity) {
                                        if (topActivity != null) {
                                            topActivity.finish();
                                        }
                                    }
                                }
                            }, 200);

                        }
                    });
                }
            }
            topActivity = currentTopActivity;
        }
        KitMainHandler.getMainHandler().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (duration > 0) {
                    startMonitorTopActivity();
                }
            }
        }, 5000);
    }



    private final Random mRandom = new Random();

    /**
     * 获得随机x坐标
     *
     * @return
     */
    private int getRandomX() {
        if (getScreenWidth() > 0) {
            return mRandom.nextInt(getScreenWidth());
        }
        return 0;
    }

    private final Rect rect = new Rect();

    /**
     * 获得随机的Y坐标
     *
     * @return
     */
    private int getRandomY() {
        if (getScreenHeight() > 0) {
            int statusBarHeight = KitStatusBarUtil.getStatusBarHeight(getBaseContext());
            KitForegroundUtil.getInstance().getTopActivity().getWindow().getDecorView().getRootView().getWindowVisibleDisplayFrame(rect);
            int visibleHeight = rect.bottom;
            int bound = visibleHeight - statusBarHeight;
            if (bound > 0) {
                return statusBarHeight + mRandom.nextInt(bound);
            }
        }
        return 0;
    }

    public static final String TAG = "MockClickService";


    @Override
    public boolean onUnbind(Intent intent) {
        return super.onUnbind(intent);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}