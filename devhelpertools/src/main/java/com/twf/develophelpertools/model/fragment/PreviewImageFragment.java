package com.twf.develophelpertools.model.fragment;

import android.graphics.Bitmap;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.constant.Constants;
import com.twf.develophelpertools.thread.KitThreadPool;
import com.twf.develophelpertools.util.KitBitmapUtil;

import java.io.File;

/**
 * Created by guofeng
 * on 2019/6/6.
 */

public class PreviewImageFragment extends BaseKitFragment {

    private File mFile;
    private ImageView mImageView;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_preview_image_fragment, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initIntent();
        initView(view);
        loadImage();
    }

    private void loadImage() {
        if (mFile != null) {
            KitThreadPool.executor(new Runnable() {
                @Override
                public void run() {
                    final Bitmap bitmap = KitBitmapUtil.decodeSampledBitmapFromFilePath(mFile.getPath(), 1080, 1920);
                    mImageView.post(new Runnable() {
                        @Override
                        public void run() {
                            mImageView.setImageBitmap(bitmap);
                        }
                    });
                }
            });

        }
    }

    private void initView(@NonNull View view) {
        mImageView = view.findViewById(R.id.mImageView);
    }

    private void initIntent() {
        Bundle arguments = getArguments();
        if (arguments != null) {
            mFile = (File) arguments.getSerializable(Constants.BUNDLE_FILE_KEY);
        }
    }
}
