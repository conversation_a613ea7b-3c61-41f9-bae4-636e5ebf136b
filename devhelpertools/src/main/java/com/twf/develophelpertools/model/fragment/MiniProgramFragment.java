package com.twf.develophelpertools.model.fragment;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.kit.baselibrary.AppCallBackItf;
import com.kit.baselibrary.MiniProgramBean;
import com.kit.baselibrary.OnMiniProgramChangeCallBack;
import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;

import java.lang.reflect.Field;

/**
 * Created by guofeng
 * on 2019/6/5.
 */

public class MiniProgramFragment extends BaseKitFragment {


    private TextView mChooseType;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_mini_program_fragment, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mChooseType = view.findViewById(R.id.mChooseType);
        View mRelease = view.findViewById(R.id.mRelease);
        View mDevelop = view.findViewById(R.id.mDevelop);
        View mExperience = view.findViewById(R.id.mExperience);
        mRelease.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onReleaseListener();
            }
        });

        mDevelop.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onDeveloperListener();
            }
        });

        mExperience.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onExperienceListener();
            }
        });

        mChooseType.setText(getValue());
    }

    private void onExperienceListener() {
        mChooseType.setText("已选择：体验版");
        reflateValue(MiniProgramBean.EXPERIENCE);
    }

    private void onDeveloperListener() {
        mChooseType.setText("已选择：开发版");
        reflateValue(MiniProgramBean.DEVELOP);
    }

    private void onReleaseListener() {
        mChooseType.setText("已选择：正式版");
        reflateValue(MiniProgramBean.RELEASE);
    }



    private String getValue() {

        AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
        if (appCallBackItf == null) return null;

        MiniProgramBean miniProgramBean = appCallBackItf.getMiniProgramBean();
        if (miniProgramBean == null) return null;

        String getCurrentType = miniProgramBean.getGetCurrentType();

        if (TextUtils.equals(String.valueOf(MiniProgramBean.DEVELOP), getCurrentType)) {
            return "已选择：开发版";
        }
        if (TextUtils.equals(String.valueOf(MiniProgramBean.RELEASE), getCurrentType)) {
            return "已选择：正式版";
        }
        if (TextUtils.equals(String.valueOf(MiniProgramBean.EXPERIENCE), getCurrentType)) {
            return "已选择：体验版";
        }

        return null;
    }

    private void reflateValue(int value) {
        try {

            AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
            if (appCallBackItf == null) return;

            MiniProgramBean miniProgramBean = appCallBackItf.getMiniProgramBean();
            if (miniProgramBean == null) return;

            OnMiniProgramChangeCallBack onMiniProgramChangeCallBack = miniProgramBean.getOnMiniProgramChangeCallBack();
            if (onMiniProgramChangeCallBack != null) {
                onMiniProgramChangeCallBack.onMiniProgramChangeListener(String.valueOf(value));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
