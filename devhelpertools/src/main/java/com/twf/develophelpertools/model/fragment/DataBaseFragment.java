package com.twf.develophelpertools.model.fragment;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.HorizontalScrollView;
import android.widget.ListView;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.database.TableNameAdapter;
import com.twf.develophelpertools.adapter.database.TableValuesAdapter;
import com.twf.develophelpertools.constant.Constants;
import com.twf.develophelpertools.util.KitDataBaseUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by guofeng
 * on 2019/6/12.
 */

public class DataBaseFragment extends BaseKitFragment {

    private SQLiteDatabase sqLiteDatabase;
    private HorizontalScrollView mHorScrollView;
    private ListView mContent;
    private ListView mTableName;

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        initFile();
    }

    private void initFile() {
        Bundle arguments = getArguments();
        if (arguments != null) {
            File mFile = (File) arguments.getSerializable(Constants.BUNDLE_FILE_KEY);
            if (mFile != null) {
                sqLiteDatabase = SQLiteDatabase.openOrCreateDatabase(mFile, null);
            }
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_data_base_fragment, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initView(view);
        showTableNames();
        initTables();
    }


    private void initView(@NonNull View view) {
        mTableName = view.findViewById(R.id.mTableName);
        mContent = view.findViewById(R.id.mContent);
        mHorScrollView = view.findViewById(R.id.mHorScrollView);
    }

    //显示TableName
    private void initTables() {
        List<String> tableNameList = KitDataBaseUtil.queryAllTables(sqLiteDatabase);
        TableNameAdapter tableNameAdapter = new TableNameAdapter();
        mTableName.setAdapter(tableNameAdapter);
        tableNameAdapter.setData(tableNameList);
        mTableName.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                String tableName = (String) parent.getItemAtPosition(position);
                //数据库表名称
                List<String> tableNameList = KitDataBaseUtil.queryTableColumnName(sqLiteDatabase, tableName);
                List<List<String>> valuesList = KitDataBaseUtil.queryTableValue(sqLiteDatabase, tableName);
                List<List<String>> result = new ArrayList<>();
                if (tableNameList != null) {
                    result.add(tableNameList);
                }
                if (valuesList != null) {
                    result.addAll(valuesList);
                }
                TableValuesAdapter adapter = new TableValuesAdapter();
                mContent.setAdapter(adapter);
                adapter.setData(result);
                //显示数据库表
                showTableDetails();
            }
        });
    }


    //显示数据库表名称
    private void showTableNames() {
        mTableName.setVisibility(View.VISIBLE);
        mHorScrollView.setVisibility(View.GONE);
    }

    //显示数据库表内容
    private void showTableDetails() {
        mTableName.setVisibility(View.GONE);
        mHorScrollView.setVisibility(View.VISIBLE);
    }

    //点击返回键 true表示关闭 false表示不关闭
    @Override
    public boolean onBackPressListener() {
        if (mHorScrollView.getVisibility() == View.VISIBLE) {
            showTableNames();
            return false;
        }
        return super.onBackPressListener();
    }
}
