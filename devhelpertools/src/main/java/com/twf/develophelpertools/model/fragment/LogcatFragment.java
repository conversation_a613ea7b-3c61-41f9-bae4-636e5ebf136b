package com.twf.develophelpertools.model.fragment;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.RadioButton;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.logcat.LogcatAdapter;
import com.twf.develophelpertools.bean.LogcatBean;
import com.twf.develophelpertools.constant.Constants;
import com.twf.develophelpertools.itf.LoadTargetFragmentItf;
import com.twf.develophelpertools.thread.KitThreadPool;
import com.twf.develophelpertools.util.KitLogcatUtil;
import com.twf.develophelpertools.util.KitMainHandler;
import com.twf.develophelpertools.util.KitSpUtil;

import java.io.BufferedReader;
import java.io.InputStreamReader;

/**
 * Created by guofeng
 * on 2019/6/24.
 */

public class LogcatFragment extends BaseKitFragment {

    private static final String SP_AUTO_SMOOTH = "SP_AUTO_SMOOTH";
    private ListView mListView;
    private LogcatAdapter adapter;

    private EditText mInputText;
    private boolean canLoop = true;


    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.kit_logcat_fragment, container, false);
        //初始化View
        initView(view);
        //刷新日志
        startLogcat();
        return view;
    }


    private boolean getSmoothSwitch() {
        return KitSpUtil.getBoolean(SP_AUTO_SMOOTH, true);
    }

    private void initView(@NonNull View view) {
        mListView = view.findViewById(R.id.mListView);
        mInputText = view.findViewById(R.id.mInputText);
        RadioButton mVerbose = view.findViewById(R.id.mVerbose);
        RadioButton mDebug = view.findViewById(R.id.mDebug);
        RadioButton mInfo = view.findViewById(R.id.mInfo);
        RadioButton mWarn = view.findViewById(R.id.mWarn);
        RadioButton mError = view.findViewById(R.id.mError);
        CheckBox mCheckView = view.findViewById(R.id.mCheckView);
        view.findViewById(R.id.mClear).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                clearAdapter();
            }
        });
        mCheckView.setChecked(getSmoothSwitch());
        mCheckView.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                KitSpUtil.putBoolean(SP_AUTO_SMOOTH, isChecked);
            }
        });

        final LinearLayout mContainer = view.findViewById(R.id.mContainer);
        mVerbose.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    if (adapter != null) {
                        adapter.setLevel(Log.VERBOSE);
                    }
                    //其它反选
                    setRadioButtonOtherUnSelect(buttonView, mContainer);
                }
            }
        });
        mDebug.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    if (adapter != null) {
                        adapter.setLevel(Log.DEBUG);
                    }
                    //其它反选
                    setRadioButtonOtherUnSelect(buttonView, mContainer);
                }
            }
        });
        mInfo.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    if (adapter != null) {
                        adapter.setLevel(Log.INFO);
                    }
                    //其它反选
                    setRadioButtonOtherUnSelect(buttonView, mContainer);
                }
            }
        });
        mWarn.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    if (adapter != null) {
                        adapter.setLevel(Log.WARN);
                    }
                    //其它反选
                    setRadioButtonOtherUnSelect(buttonView, mContainer);
                }
            }
        });
        mError.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    if (adapter != null) {
                        adapter.setLevel(Log.ERROR);
                    }
                    //其它反选
                    setRadioButtonOtherUnSelect(buttonView, mContainer);
                }
            }
        });
    }

    //设置其它RadioButton反选
    private void setRadioButtonOtherUnSelect(CompoundButton buttonView, LinearLayout mContainer) {
        int childCount = mContainer.getChildCount();
        for (int i = 0; i < childCount; i++) {
            View child = mContainer.getChildAt(i);
            if (child == buttonView) continue;
            if (child instanceof RadioButton) {
                RadioButton radioButton = (RadioButton) child;
                radioButton.setChecked(false);
            }
        }
    }

    /**
     * 清空列表
     */
    private void clearAdapter() {
        if (adapter != null) {
            adapter.setData(null);
        }
    }


    //刷新列表
    private void refreshAdapter(LogcatBean logcatBean) {
        if (adapter == null) {
            adapter = new LogcatAdapter();
            adapter.setInputEditText(mInputText);
            mListView.setAdapter(adapter);
            adapter.setCallBack(new LogcatAdapter.OnClickCallBack() {
                @Override
                public void onClickListener(String text) {
                    showText(text);
                }
            });
        }
        adapter.addData(logcatBean);
        //是否自动滑动
        boolean smoothSwitch = getSmoothSwitch();
        if (smoothSwitch) {
            mListView.setSelection(adapter.getCount() - 1);
        }
    }


    //启动日志
    private void startLogcat() {
        KitThreadPool.executor(logcatLoop);
    }

    private Process logcatProcess;

    //循环输出日志
    private Runnable logcatLoop = new Runnable() {
        @Override
        public void run() {
            try {
                logcatProcess = KitLogcatUtil.getLogcatProcess();
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(logcatProcess
                        .getInputStream()), 8192);

                String line;
                while ((line = bufferedReader.readLine()) != null && canLoop) {
                    final LogcatBean logcatBean = LogcatBean.newLogLine(line);
                    //同步刷新列表
                    syncRefreshAdapter(logcatBean);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    };

    //同步刷新列表
    private void syncRefreshAdapter(final LogcatBean logcatBean) {
        if (logcatBean == null) return;
        KitMainHandler.getMainHandler().post(new Runnable() {
            @Override
            public void run() {
                //只能同步刷新UI
                refreshAdapter(logcatBean);
            }
        });
    }


    private void showText(String text) {
        //默认文本文件兜底
        LoadTargetFragmentItf loadTargetFragmentItf = loadTargetFragmentItf();
        if (loadTargetFragmentItf != null) {
            Bundle bundle = new Bundle();
            bundle.putSerializable(Constants.BUNDLE_FILE_KEY, text);
            loadTargetFragmentItf.loadFragment(ShowTextFragment1.class, bundle);
        }
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        //是否可以轮训日志
        canLoop = false;
        //关闭日志进程
        if (logcatProcess != null) {
            logcatProcess.destroy();
        }
    }

}
