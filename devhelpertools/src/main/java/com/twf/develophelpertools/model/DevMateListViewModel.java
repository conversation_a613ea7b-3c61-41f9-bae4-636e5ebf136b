package com.twf.develophelpertools.model;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.bean.DevMateBean;
import com.twf.develophelpertools.bean.JiraItemBean;
import com.twf.develophelpertools.model.fragment.DevMateListFragment;

import java.util.ArrayList;
import java.util.List;

/**
 * create by guofeng
 * date on 2021/5/10
 */

public class DevMateListViewModel extends ViewModel {


    /**
     * 获得全部开发好友列表
     */
    public final MutableLiveData<List<DevMateBean>> sourceDevMateViewModel = new MutableLiveData<>();



    /**
     * 初始化研发全部好友数据
     */
    public void initAllDevMateViewModel(int from) {

        final List<DevMateBean> value = new ArrayList<>();
        for (int i = 0; i < 60; i++) {
            final DevMateBean mateBean = new DevMateBean();
            mateBean.setName("张祥东" + i);
            mateBean.setmGroupName("研发七组");
            mateBean.setEmail("12345");
            mateBean.setWeixinNumber("567887");
            value.add(mateBean);
        }

        List<DevMateBean> createJiraMateList = getCreateJiraMateList(from);
        if (createJiraMateList != null) {
            for (DevMateBean selectMateBean : createJiraMateList) {
                if (selectMateBean == null) continue;
                for (DevMateBean itemBean : value) {
                    if (itemBean == null) continue;

                    if (itemBean.isSameMateBean(selectMateBean)) {
                        itemBean.setSelect(true);
                    }
                }
            }
        }

        sourceDevMateViewModel.setValue(value);
    }


    /**
     * 获得已经选择创建开发好友
     *
     * @return
     */
    private List<DevMateBean> getCreateJiraMateList(int from) {
        //创建页面
        if (from == DevMateListFragment.FROM_EDIT) {
            MutableLiveData<JiraItemBean> currentViewMode = IssueEditTaskViewModel.createViewModel().currentViewMode;
            JiraItemBean jiraItemBean = currentViewMode.getValue();
            if (jiraItemBean != null) {
                return jiraItemBean.getMateList();
            }
            return null;
        }

        //编辑页面
        if (from == DevMateListFragment.FROM_CREATE) {
            MutableLiveData<JiraItemBean> currentViewMode = IssueCreateTaskViewModel.createViewModel().currentViewMode;
            JiraItemBean jiraItemBean = currentViewMode.getValue();
            if (jiraItemBean != null) {
                return jiraItemBean.getMateList();
            }
            return null;
        }

        return null;

    }


    private static DevMateListViewModel devMateListViewModel;

    /**
     * 获得全局数据
     *
     * @return
     */
    public static DevMateListViewModel createViewModel() {
        if (devMateListViewModel == null) {
            devMateListViewModel = ViewModelProvider.AndroidViewModelFactory.getInstance(Kit.getInstance().getApplication()).create(DevMateListViewModel.class);
        }
        return devMateListViewModel;
    }

} 