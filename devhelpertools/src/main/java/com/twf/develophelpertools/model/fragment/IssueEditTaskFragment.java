package com.twf.develophelpertools.model.fragment;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.DevMateSelectedAdapter;
import com.twf.develophelpertools.adapter.IssueEditImageAdapter;
import com.twf.develophelpertools.bean.DevMateBean;
import com.twf.develophelpertools.bean.JiraImageBean;
import com.twf.develophelpertools.bean.JiraItemBean;
import com.twf.develophelpertools.constant.Constants;
import com.twf.develophelpertools.model.IssueEditTaskViewModel;
import com.twf.develophelpertools.util.KitDialogUtil;
import com.twf.develophelpertools.util.KitForegroundUtil;
import com.twf.develophelpertools.util.KitMediaSelectUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import static android.app.Activity.RESULT_OK;
import static android.content.pm.PackageManager.PERMISSION_GRANTED;
//import static com.stone.cold.screenrecorder.fileexplorer.ui.MatisseActivity.EXTRA_RESULT_MEDIA_TYPE;
//import static com.stone.cold.screenrecorder.fileexplorer.ui.MatisseActivity.EXTRA_RESULT_SELECTION_PATH;

/**
 * create by guofeng
 * date on 2021/5/10
 */

public class IssueEditTaskFragment extends BaseKitFragment {

    public static final int REQUEST_CHOOSE_IMAGE = 100;

    public static final int EXTERN_REQUEST_CODE = 101;

    private EditText mEditTextTitle;
    private EditText mEditTextDesc;
    private GridView mDevNames;
    private GridView mImageFiles;
    private CheckBox mCheckView;


    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {

        return inflater.inflate(R.layout.kit_edit_task_fragment, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        mEditTextTitle = view.findViewById(R.id.mEditTextTitle);
        mEditTextDesc = view.findViewById(R.id.mEditTextDesc);
        ImageView mAddDevMate = view.findViewById(R.id.mAddDevMate);
        mDevNames = view.findViewById(R.id.mDevNames);
        mImageFiles = view.findViewById(R.id.mRecycleView);
        mCheckView = view.findViewById(R.id.mCheckView);
        TextView mSubmit = view.findViewById(R.id.mSubmit);

        mEditTextTitle.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                JiraItemBean currentViewModel = IssueEditTaskViewModel.createViewModel().currentViewMode.getValue();
                if (currentViewModel != null) {
                    currentViewModel.setTitle(mEditTextTitle.getText().toString());
                }
            }
        });

        mEditTextDesc.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                JiraItemBean currentViewModel = IssueEditTaskViewModel.createViewModel().currentViewMode.getValue();
                if (currentViewModel != null) {
                    currentViewModel.setDesc(mEditTextDesc.getText().toString());
                }
            }
        });

        mSubmit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                JiraItemBean currentViewModel = IssueEditTaskViewModel.createViewModel().currentViewMode.getValue();
                if (currentViewModel == null) return;

                if (checkError(currentViewModel)) return;

                //保存数据到缓存
                IssueEditTaskViewModel.createViewModel().currentViewMode.setValue(currentViewModel);

                //保存数据到SP数据库
                IssueEditTaskViewModel.createViewModel().saveAllJiraList();

                //返回上一个页面
                getBaseActivity().onBackPressed();
            }
        });

        mAddDevMate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Bundle bundle=new Bundle();
                bundle.putInt(Constants.BUNDLE_FILE_KEY,DevMateListFragment.FROM_EDIT);
                loadTargetFragmentItf().loadFragment(DevMateListFragment.class, bundle);
            }
        });

        //添加监控数据
        IssueEditTaskViewModel.createViewModel().currentViewMode.observe((LifecycleOwner) getBaseActivity(), new Observer<JiraItemBean>() {
            @Override
            public void onChanged(JiraItemBean jiraItemBean) {

                mEditTextTitle.setText(jiraItemBean.getTitle());

                mEditTextDesc.setText(jiraItemBean.getDesc());

                //视频图片
                final List<JiraImageBean> imageList = jiraItemBean.getImageList();
                //好友列表
                final List<DevMateBean> mateList = jiraItemBean.getMateList();

                //好友列表
                DevMateSelectedAdapter selectedAdapter = new DevMateSelectedAdapter();
                selectedAdapter.setCallBack(new DevMateSelectedAdapter.OnDeleteMateCallBack() {
                    @Override
                    public void onDeleteMateListener(DevMateBean devMateBean) {
                        IssueEditTaskViewModel.createViewModel().deleteMate(devMateBean);
                    }
                });
                selectedAdapter.setData(mateList);
                mDevNames.setAdapter(selectedAdapter);

                //好友视频文件
                IssueEditImageAdapter imageAdapter = new IssueEditImageAdapter();
                imageAdapter.setOnPickImageCallBack(new IssueEditImageAdapter.OnPickImageCallBack() {
                    @Override
                    public void onPickImageListener() {

                        checkHasWritePermission();
                    }

                    @Override
                    public void onDeleteImageListener(final JiraImageBean imageBean) {
                        Activity topActivity = KitForegroundUtil.getInstance().getTopActivity();
                        KitDialogUtil kitDialogUtil = new KitDialogUtil(topActivity);
                        kitDialogUtil.setTitle("温馨提示");
                        kitDialogUtil.setMessage("确定删除?");
                        kitDialogUtil.setPositiveListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                IssueEditTaskViewModel.createViewModel().deleteImage(imageBean);
                            }
                        });
                        kitDialogUtil.show();
                    }
                });
                imageAdapter.setData(imageList);
                mImageFiles.setAdapter(imageAdapter);
            }
        });

        Bundle arguments = getArguments();
        if (arguments != null) {
            int index = arguments.getInt(Constants.BUNDLE_FILE_KEY);
            //刷新数据
            IssueEditTaskViewModel.createViewModel().postItem(index);
        }

    }

    /**
     * 检测是否有读写SD卡权限
     */
    private void checkHasWritePermission() {
        int permission = ActivityCompat.checkSelfPermission(Kit.getInstance().getApplication(), Manifest.permission.WRITE_EXTERNAL_STORAGE);

        if (permission == PERMISSION_GRANTED) {
            openGallery();
        } else {
            ActivityCompat.requestPermissions(getBaseActivity(), new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, EXTERN_REQUEST_CODE);
        }
    }



    //打开图库
    private void openGallery() {
        KitMediaSelectUtil.getInstance().openGallery(REQUEST_CHOOSE_IMAGE);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == EXTERN_REQUEST_CODE) {
            for (int i = 0; i < permissions.length; i++) {
                String permission = permissions[i];
                if (TextUtils.equals(Manifest.permission.WRITE_EXTERNAL_STORAGE, permission)) {
                    if (grantResults[i] == PERMISSION_GRANTED) {
                        openGallery();
                        break;
                    }
                }
            }
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
//        if (resultCode == RESULT_OK) {
//            if (requestCode == REQUEST_CHOOSE_IMAGE) {
//                if (data == null) return;
//                boolean isVideoType = data.getBooleanExtra(EXTRA_RESULT_MEDIA_TYPE, false);
//                ArrayList<String> pathList = data.getStringArrayListExtra(EXTRA_RESULT_SELECTION_PATH);
//                for (String path : pathList) {
//                    ////过滤掉不存在的文件
//                    if (TextUtils.isEmpty(path)||!new File(path).exists()) {
//                        Toast.makeText(getBaseActivity(),"文件不存在",Toast.LENGTH_LONG).show();
//                        continue;
//                    }
//
//                    JiraImageBean jiraImageBean = new JiraImageBean();
//                    jiraImageBean.setLocalPath(path);
//                    if (isVideoType) {
//                        jiraImageBean.setFileType(JiraImageBean.VIDEO);
//                    } else {
//                        jiraImageBean.setFileType(JiraImageBean.IMAGE);
//                    }
//                    IssueEditTaskViewModel.createViewModel().addImage(jiraImageBean);
//                }
//            }
//        }
    }

    /**
     * 检测数据是否完整
     *
     * @param itemBean
     * @return
     */
    private boolean checkError(JiraItemBean itemBean) {

        if (TextUtils.isEmpty(itemBean.getTitle())) {
            Toast.makeText(getActivity(), "标题不能为空", Toast.LENGTH_SHORT).show();
            return true;
        }

        if (TextUtils.isEmpty(itemBean.getDesc())) {
            Toast.makeText(getActivity(), "描述不能为空", Toast.LENGTH_SHORT).show();
            return true;
        }

        List<DevMateBean> mateList = itemBean.getMateList();
        if (mateList == null || mateList.isEmpty()) {
            Toast.makeText(getActivity(), "请选择研发同学", Toast.LENGTH_SHORT).show();
            return true;
        }

        List<JiraImageBean> imageList = itemBean.getImageList();
        if (imageList == null ||imageList.isEmpty()) {
            Toast.makeText(getActivity(), "选择图片文件", Toast.LENGTH_SHORT).show();
            return true;
        }

        return false;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        //重置数据
        IssueEditTaskViewModel.createViewModel().reset();
    }
}