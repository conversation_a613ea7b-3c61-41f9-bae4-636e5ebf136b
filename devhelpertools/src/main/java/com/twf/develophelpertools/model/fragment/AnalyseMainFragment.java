package com.twf.develophelpertools.model.fragment;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ListView;
import android.widget.Toast;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.analy.AnalyseMainAdapter;
import com.twf.develophelpertools.adapter.analy.BgAnalyseIterceptor;
import com.twf.develophelpertools.bean.AnalyseInfoBean;
import com.twf.develophelpertools.util.KitDialogUtil;
import com.twf.develophelpertools.util.KitSpUtil;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

import static com.twf.develophelpertools.adapter.analy.BgAnalyseIterceptor.ANALYSE_KEY;
import static com.twf.develophelpertools.adapter.analy.BgAnalyseIterceptor.JSON_ACTION;
import static com.twf.develophelpertools.adapter.analy.BgAnalyseIterceptor.JSON_FAILED;
import static com.twf.develophelpertools.adapter.analy.BgAnalyseIterceptor.JSON_SUCCESS;
import static com.twf.develophelpertools.adapter.analy.BgAnalyseIterceptor.JSON_VALUE;

public class AnalyseMainFragment extends BaseKitFragment {


    public static final String SP_SHOW_TOAST = "SP_SHOW_TOAST";

    private ListView mListView;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_analy_fragment, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        //列表
        mListView = view.findViewById(R.id.mListView);
        mListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
            }
        });

        //是否选择Toast埋点
        CheckBox mCheckView = view.findViewById(R.id.mCheckView);
        mCheckView.setChecked(KitSpUtil.getBoolean(SP_SHOW_TOAST, false));
        mCheckView.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean b) {
                KitSpUtil.putBoolean(SP_SHOW_TOAST, b);
            }
        });
        refreshAdapter();
    }



    private void refreshAdapter() {
        AnalyseMainAdapter adapter = new AnalyseMainAdapter();
        mListView.setAdapter(adapter);
        adapter.setData(BgAnalyseIterceptor.getAnalyseInfoBeans());
    }

}
