package com.twf.develophelpertools.model.layoutborder;

import android.content.res.Resources;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ColorFilter;
import android.graphics.DashPathEffect;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;

import com.twf.develophelpertools.util.KitScaleUtil;

/**
 * Created by wang<PERSON><PERSON> on 2019/1/11
 */
public class ViewBorderDrawable extends Drawable {
    private final Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG);

    private final Rect rect;

    private final View mView;

    private final Paint textPaint = new Paint(Paint.ANTI_ALIAS_FLAG);

    public ViewBorderDrawable(View view) {
        rect = new Rect(0, 0, view.getWidth(), view.getHeight());
        mView = view;

        paint.setStyle(Paint.Style.STROKE);
        paint.setColor(Color.RED);
        paint.setStrokeWidth(2);
        paint.setPathEffect(new DashPathEffect(new float[]{4, 4}, 0));

        textPaint.setColor(Color.BLUE);
        textPaint.setStrokeWidth(2);
        textPaint.setStyle(Paint.Style.STROKE);
        textPaint.setTextSize(KitScaleUtil.dip2px(12));
    }


    @Override
    public void draw(@NonNull Canvas canvas) {
        if (LayoutBorderConfig.isLayoutBorderOpen()) {
            canvas.drawRect(rect, paint);
            canvas.drawText(getViewText(), 0, getBounds().height() / 2, textPaint);
        }
    }

    private String getViewText() {
        if (mView != null) {
            String className = "";
            String fieldName = "";
            if (mView.getTag() != null) {
                className = mView.getTag().getClass().getSimpleName();
            }
            try {
                if (mView.getResources() != null) {
                    fieldName = mView.getResources().getResourceEntryName(mView.getId());
                }
            } catch (Resources.NotFoundException e) {
                e.printStackTrace();
            }
            if (!TextUtils.isEmpty(className) && !TextUtils.isEmpty(fieldName)) {
                return className + ":" + fieldName;
            }
            return className + fieldName;
        }
        return "";
    }

    @Override
    public void setAlpha(int alpha) {

    }

    @Override
    public void setColorFilter(@Nullable ColorFilter colorFilter) {

    }

    @Override
    public int getOpacity() {
        // to be safe
        return PixelFormat.TRANSLUCENT;
    }
}