package com.twf.develophelpertools.model.fragment;

import android.content.Context;
import android.os.AsyncTask;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ListView;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.sendbox.ShowTextAdapter;
import com.twf.develophelpertools.constant.Constants;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by guofeng
 * on 2019/6/6.
 */

public class ShowTextFragment extends BaseKitFragment {

    private final List<String> contentList = new ArrayList<>();

    private File mFile;

    private ListView mListView;
    private ShowTextAdapter adapter;

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        initIntent();
    }

    private void initIntent() {
        Bundle arguments = getArguments();
        if (arguments != null) {
            mFile = (File) arguments.getSerializable(Constants.BUNDLE_FILE_KEY);
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return LayoutInflater.from(getBaseActivity()).inflate(R.layout.kit_show_text_fragment, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mListView = view.findViewById(R.id.mListView);
        refreshAdapter();
        startLoading();
    }

    private void startLoading() {
        FileReadTask task = new FileReadTask();
        task.execute(mFile);
    }


    private void refreshAdapter() {
        if (adapter == null) {
            adapter = new ShowTextAdapter(getBaseActivity());
            mListView.setAdapter(adapter);
        }
        adapter.setData(contentList);
    }


    private class FileReadTask extends AsyncTask<File, String, Void> {

        @Override
        protected Void doInBackground(File... files) {
            try {
                FileReader fileReader = new FileReader(files[0]);
                BufferedReader br = new BufferedReader(fileReader);
                String textLine;
                while ((textLine = br.readLine()) != null) {
                    publishProgress(textLine);
                }
                br.close();
                fileReader.close();
            } catch (IOException e) {
            }
            return null;
        }

        @Override
        protected void onProgressUpdate(String... values) {
            super.onProgressUpdate(values);
            contentList.add(values[0]);
            refreshAdapter();
        }
    }
}
