package com.twf.develophelpertools.model.fragment;

import android.graphics.Rect;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.kit.baselibrary.AppCallBackItf;
import com.kit.baselibrary.ButtonConfigBean;
import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.util.KitScaleUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * create by guofeng
 * date on 2021/8/4
 */

public class ButtonConfigFragment extends BaseKitFragment {

    private RecyclerView mRecycleView;

    public static ButtonConfigFragment newInstance() {
        return new ButtonConfigFragment();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_common_tools_fragment, container, false);
    }


    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mRecycleView = view.findViewById(R.id.mRecycleView);
        mRecycleView.setLayoutManager(new GridLayoutManager(getBaseActivity(), 3));
        mRecycleView.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, int itemPosition, @NonNull RecyclerView parent) {
                super.getItemOffsets(outRect, itemPosition, parent);
                outRect.set(0, KitScaleUtil.dip2px(30), KitScaleUtil.dip2px(10), 0);
            }
        });

        AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
        if (appCallBackItf != null) {
            List<ButtonConfigBean> buttonConfigList = appCallBackItf.getButtonConfigList();
            ButtonConfigAdapter adapter = new ButtonConfigAdapter();
            adapter.setData(buttonConfigList);
            mRecycleView.setAdapter(adapter);
        }
    }



    private class ButtonConfigAdapter extends RecyclerView.Adapter<ButtonConfigAdapter.CommonToolViewHolder> {

        private final List<ButtonConfigBean> data = new ArrayList<>();


        private void setData(List<ButtonConfigBean> source) {
            data.clear();
            if (source != null) {
                data.addAll(source);
            }
            notifyDataSetChanged();
        }


        @Override
        public ButtonConfigAdapter.CommonToolViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            return new ButtonConfigAdapter.CommonToolViewHolder(LayoutInflater.from(getContext()).inflate(R.layout.kit_common_tool_item, null));
        }

        @Override
        public void onBindViewHolder(ButtonConfigAdapter.CommonToolViewHolder holder, int position) {
            ButtonConfigBean commonToolsBean = data.get(position);
            if (commonToolsBean != null) {
                holder.mName.setText(commonToolsBean.getName());
                holder.itemView.setOnClickListener(commonToolsBean.getmListener());
            }
        }

        @Override
        public int getItemCount() {
            return data.size();
        }

        public class CommonToolViewHolder extends RecyclerView.ViewHolder {


            private TextView mName;

            public CommonToolViewHolder(View itemView) {
                super(itemView);

                mName = itemView.findViewById(R.id.mName);

            }
        }

    }

}