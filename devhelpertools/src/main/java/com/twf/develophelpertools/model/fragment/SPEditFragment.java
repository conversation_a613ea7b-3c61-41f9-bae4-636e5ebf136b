package com.twf.develophelpertools.model.fragment;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ListView;
import android.widget.Toast;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.sendbox.SpEditAdapter;
import com.twf.develophelpertools.bean.BaseSpInfo;
import com.twf.develophelpertools.bean.BooleanSpInfo;
import com.twf.develophelpertools.bean.FloatSpInfo;
import com.twf.develophelpertools.bean.IntegerSpInfo;
import com.twf.develophelpertools.bean.LongSpInfo;
import com.twf.develophelpertools.bean.StringSpInfo;
import com.twf.develophelpertools.constant.Constants;
import com.twf.develophelpertools.constant.SpValueType;
import com.twf.develophelpertools.util.KitFileUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by guofeng
 * on 2019/6/6.
 */

public class SPEditFragment extends BaseKitFragment {

    private SharedPreferences.Editor edit;
    private ListView mListView;
    private Button mCancel;
    private Button mSubmit;
    private File mFile;

    private final List<BaseSpInfo> spInfoCache = new ArrayList<>();

    private SharedPreferences sharedPreferences;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_sp_edit_fragment, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initIntent();
        initView(view);
        loadAllSp();
        refreshAdapter();
    }


    private void initIntent() {
        Bundle arguments = getArguments();
        if (arguments != null) {
            mFile = (File) arguments.getSerializable(Constants.BUNDLE_FILE_KEY);
        }
    }

    private void initView(@NonNull View view) {
        mSubmit = view.findViewById(R.id.mSubmit);
        mCancel = view.findViewById(R.id.mCancel);
        mListView = view.findViewById(R.id.mListView);
        mCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                getBaseActivity().onBackPressed();
            }
        });
        mSubmit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                edit.apply();
                Toast.makeText(getBaseActivity(), "保存成功", Toast.LENGTH_LONG).show();
                getBaseActivity().onBackPressed();
            }
        });
    }


    private void loadAllSp() {
        spInfoCache.clear();
        if (mFile != null) {
            String fileName = mFile.getName().replace(KitFileUtil.XML, "");
            sharedPreferences = getBaseActivity().getSharedPreferences(fileName, Context.MODE_PRIVATE);
            edit = sharedPreferences.edit();

            Map<String, ?> all = sharedPreferences.getAll();
            for (Map.Entry<String, ?> stringEntry : all.entrySet()) {
                if (stringEntry == null) continue;
                String key = stringEntry.getKey();
                Object value = stringEntry.getValue();
                String simpleName = value.getClass().getSimpleName();
                switch (simpleName) {
                    case SpValueType.booleanType:
                        BooleanSpInfo booleanSpInfo = new BooleanSpInfo();
                        booleanSpInfo.setKey(key);
                        booleanSpInfo.setValue(Boolean.parseBoolean(value.toString()));
                        spInfoCache.add(booleanSpInfo);
                        break;
                    case SpValueType.integerType:
                        IntegerSpInfo integerSpInfo = new IntegerSpInfo();
                        integerSpInfo.setKey(key);
                        integerSpInfo.setValue(Integer.parseInt(value.toString()));
                        spInfoCache.add(integerSpInfo);
                        break;
                    case SpValueType.longType:
                        LongSpInfo longSpInfo = new LongSpInfo();
                        longSpInfo.setKey(key);
                        longSpInfo.setValue(Long.parseLong(value.toString()));
                        spInfoCache.add(longSpInfo);
                        break;
                    case SpValueType.floatType:
                        FloatSpInfo floatSpInfo = new FloatSpInfo();
                        floatSpInfo.setKey(key);
                        floatSpInfo.setValue(Float.parseFloat(value.toString()));
                        spInfoCache.add(floatSpInfo);
                        break;
                    case SpValueType.stringType:
                        StringSpInfo stringSpInfo = new StringSpInfo();
                        stringSpInfo.setKey(key);
                        stringSpInfo.setValue(value.toString());
                        spInfoCache.add(stringSpInfo);
                        break;
                }
            }
        }
    }

    private void refreshAdapter() {
        SpEditAdapter adapter = new SpEditAdapter(getBaseActivity(), edit);
        adapter.setData(spInfoCache);
        mListView.setAdapter(adapter);
    }
}
