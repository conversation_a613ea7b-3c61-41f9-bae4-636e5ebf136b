package com.twf.develophelpertools.model.fragment;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.constant.Constants;
import com.twf.develophelpertools.util.KitClipBoardUtil;

/**
 * Created by guofeng
 * on 2019/6/6.
 */

public class ShowTextFragment1 extends BaseKitFragment {


    private String text;
    private TextView mTextView;


    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        initIntent();
    }

    private void initIntent() {
        Bundle arguments = getArguments();
        if (arguments != null) {
            text = (String) arguments.getSerializable(Constants.BUNDLE_FILE_KEY);
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return LayoutInflater.from(getBaseActivity()).inflate(R.layout.kit_show_text_fragment1, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mTextView = view.findViewById(R.id.mTextView);
        mTextView.setText(text);
        mTextView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                KitClipBoardUtil.copyToClipBoard(text);
                Toast.makeText(getActivity(), "复制成功", Toast.LENGTH_SHORT).show();
            }
        });
    }

}
