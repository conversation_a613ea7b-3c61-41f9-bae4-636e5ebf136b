package com.twf.develophelpertools.model;

import android.text.TextUtils;
import android.widget.Toast;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.bean.DevMateBean;
import com.twf.develophelpertools.bean.JiraImageBean;
import com.twf.develophelpertools.bean.JiraItemBean;

import java.util.ArrayList;
import java.util.List;

/**
 * create by guofeng
 * date on 2021/5/10
 */

public class IssueCreateTaskViewModel extends ViewModel {

    /**
     * 当前创建的任务数据
     */
    public final MutableLiveData<JiraItemBean> currentViewMode = new MutableLiveData<>();


    public final JiraItemBean resetItemBean = new JiraItemBean();


    /**
     * 删除
     *
     * @param devMateBean
     */
    public void deleteMate(DevMateBean devMateBean) {
        JiraItemBean value = currentViewMode.getValue();
        if (value != null) {
            List<DevMateBean> mateList = value.getMateList();
            if (mateList != null) {
                for (DevMateBean mateBean : mateList) {
                    if (mateBean == null) continue;
                    if (devMateBean.isSameMateBean(mateBean)) {
                        mateList.remove(mateBean);
                        Toast.makeText(Kit.getInstance().getApplication(), "删除成功", Toast.LENGTH_SHORT).show();
                        IssueCreateTaskViewModel.createViewModel().currentViewMode.postValue(value);
                        break;
                    }
                }
            }
        }
    }

    /**
     * 删除数据
     *
     * @param imageBean
     */
    public void deleteImage(JiraImageBean imageBean) {
        JiraItemBean value = currentViewMode.getValue();
        if (value != null) {
            List<JiraImageBean> imageList = value.getImageList();
            if (imageList != null) {
                for (JiraImageBean jiraImageBean : imageList) {
                    if (jiraImageBean == null) continue;
                    if (jiraImageBean == imageBean) {
                        imageList.remove(jiraImageBean);
                        break;
                    }
                    if (TextUtils.equals(jiraImageBean.getLocalPath(), imageBean.getLocalPath())
                            && TextUtils.equals(jiraImageBean.getUrl(), imageBean.getUrl())
                    ) {
                        imageList.remove(jiraImageBean);
                        break;
                    }
                }
            }
            currentViewMode.postValue(value);
        }
    }

    /**
     * 添加ITEM数据
     *
     * @param imageBean
     */
    public void addImage(JiraImageBean imageBean) {
        JiraItemBean value = currentViewMode.getValue();
        if (value != null) {
            List<JiraImageBean> imageList = value.getImageList();
            if (imageList == null) {
                imageList = new ArrayList<>();
                value.setImageList(imageList);
            }
            imageList.add(0, imageBean);
        }
        currentViewMode.postValue(value);
    }

    //重置数据
    public void reset() {
        resetItemBean.setTitle("");
        resetItemBean.setDesc("");
        resetItemBean.setMateList(null);
        resetItemBean.setImageList(null);
        currentViewMode.setValue(resetItemBean);
    }


    /**
     * 提交数据
     */
    public void postDefaultData() {

        JiraItemBean value = currentViewMode.getValue();

        if (value == null) {
            value = new JiraItemBean();
        }

        List<JiraImageBean> imageList = value.getImageList();
        if (imageList == null) {
            imageList = new ArrayList<>();
            value.setImageList(imageList);
        }

        currentViewMode.postValue(value);
    }


    private static IssueCreateTaskViewModel createTaskViewModel;

    /**
     * 获得全局数据
     *
     * @return
     */
    public static IssueCreateTaskViewModel createViewModel() {
        if (createTaskViewModel == null) {
            createTaskViewModel = ViewModelProvider.AndroidViewModelFactory.getInstance(Kit.getInstance().getApplication()).create(IssueCreateTaskViewModel.class);
        }
        return createTaskViewModel;
    }


} 