package com.twf.develophelpertools.model.fragment;

import android.content.Context;
import android.content.pm.ActivityInfo;
import android.graphics.Rect;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.kit.baselibrary.AppCallBackItf;
import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.activity.EnvironmentChangeActivity;
import com.twf.develophelpertools.activity.TransferCenterKitActivity;
import com.twf.develophelpertools.bean.CommonToolsBean;
import com.twf.develophelpertools.util.KitScaleUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * create by guofeng
 * date on 2021/8/4
 */

public class CommonToolsFragment extends BaseKitFragment {


    private RecyclerView mRecycleView;

    public static CommonToolsFragment newInstance() {
        return new CommonToolsFragment();
    }



    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_common_tools_fragment, container, false);
    }


    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mRecycleView = view.findViewById(R.id.mRecycleView);
        mRecycleView.setLayoutManager(new GridLayoutManager(getBaseActivity(), 3));
        mRecycleView.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, int itemPosition, @NonNull RecyclerView parent) {
                super.getItemOffsets(outRect, itemPosition, parent);
                outRect.set(0, KitScaleUtil.dip2px(30), KitScaleUtil.dip2px(10), 0);
            }
        });
        CommonToolAdapter adapter = new CommonToolAdapter();
        adapter.setData(result);
        mRecycleView.setAdapter(adapter);
    }


    private final List<CommonToolsBean> result = new ArrayList<CommonToolsBean>() {
        {

            add(getCommonItem("测试平台", R.drawable.kit_dk_sys_info, new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    TransferCenterKitActivity.jump(getBaseActivity(), TestPlatFragment.class);

                }
            }));

            add(getCommonItem("APP基本信息", R.drawable.kit_dk_sys_info, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    TransferCenterKitActivity.jump(getBaseActivity(), AppBaseInfoFragment.class);
                }
            }));

            add(getCommonItem("文件预览", R.drawable.kit_dk_sys_info, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
//                    TransferCenterKitActivity.jump(getBaseActivity(), FilePreviewFragment.class);
                }
            }));


            add(getCommonItem("模拟位置", R.drawable.kit_dk_sys_info, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    TransferCenterKitActivity.jump(getBaseActivity(), MockLocationFragment.class);
                }
            }));

            add(getCommonItem("H5任意门", R.drawable.kit_dk_sys_info, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    TransferCenterKitActivity.jump(getBaseActivity(), Html5MainFragment.class);
                }
            }));

            add(getCommonItem("Crash查看", R.drawable.kit_dk_sys_info, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    TransferCenterKitActivity.jump(getBaseActivity(), CrashMainFragment.class);
                }
            }));

            add(getCommonItem("日志查看", R.drawable.kit_dk_sys_info, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    TransferCenterKitActivity.jump(getBaseActivity(), LogcatFragment.class);
                }
            }));

            add(getCommonItem("模拟弱网", R.drawable.kit_dk_sys_info, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    TransferCenterKitActivity.jump(getBaseActivity(), WeakConnectFragment.class);
                }
            }));

            add(getCommonItem("研发MOCK", R.drawable.kit_dk_sys_info, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    TransferCenterKitActivity.jump(getBaseActivity(), DevelopMockFragment.class);
                }
            }));

            add(getCommonItem("打点统计", R.drawable.kit_dk_sys_info, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    TransferCenterKitActivity.jump(getBaseActivity(), AnalyseMainFragment.class);
                }
            }));

            add(getCommonItem("模拟点击", R.drawable.kit_dk_sys_info, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    TransferCenterKitActivity.jump(getBaseActivity(), MockFragment.class);
                }
            }));

            add(getCommonItem("小程序参数", R.drawable.kit_dk_sys_info, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    TransferCenterKitActivity.jump(getBaseActivity(), MiniProgramFragment.class);
                }
            }));

            add(getCommonItem("切换环境", R.drawable.kit_dk_sys_info, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    try {
                        EnvironmentChangeActivity.show(getBaseActivity());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    getBaseActivity().finish();
                }
            }));

            add(getCommonItem("跳转指定activity", R.drawable.kit_dk_sys_info, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    TransferCenterKitActivity.jump(getBaseActivity(), TargetActListFragment.class);
                }
            }));

            add(getCommonItem("权限统计", R.drawable.kit_dk_sys_info, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    TransferCenterKitActivity.jump(getBaseActivity(), PermissionManagerFragment.class);
                }
            }));


            add(getCommonItem("ISSUE管理", R.drawable.kit_dk_sys_info, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    TransferCenterKitActivity.jump(getBaseActivity(), IssueTaskManagerFragment.class);
                }
            }));


        }
    };






    private CommonToolsBean getCommonItem(String name, @DrawableRes int resourceBg, View.OnClickListener mListener) {
        CommonToolsBean item = new CommonToolsBean();
        item.setName(name);
        item.setResourceBg(resourceBg);
        item.setmListener(mListener);
        return item;
    }



    private class CommonToolAdapter extends RecyclerView.Adapter<CommonToolAdapter.CommonToolViewHolder> {

        private final List<CommonToolsBean> data = new ArrayList<>();


        private void setData(List<CommonToolsBean> source) {
            data.clear();
            if (source != null) {
                data.addAll(source);
            }
            notifyDataSetChanged();
        }


        @Override
        public CommonToolViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            return new CommonToolViewHolder(LayoutInflater.from(getContext()).inflate(R.layout.kit_common_tool_item, null));
        }

        @Override
        public void onBindViewHolder(CommonToolsFragment.CommonToolAdapter.CommonToolViewHolder holder, int position) {
            CommonToolsBean commonToolsBean = data.get(position);
            if (commonToolsBean != null) {
                holder.mName.setText(commonToolsBean.getName());
                holder.mImageView.setImageResource(commonToolsBean.getResourceBg());
                holder.itemView.setOnClickListener(commonToolsBean.getmListener());
            }
        }

        @Override
        public int getItemCount() {
            return data.size();
        }

        public class CommonToolViewHolder extends RecyclerView.ViewHolder {

            private ImageView mImageView;

            private TextView mName;

            public CommonToolViewHolder(View itemView) {
                super(itemView);

                mName = itemView.findViewById(R.id.mName);

                mImageView = itemView.findViewById(R.id.mImageView);
            }
        }

    }

}