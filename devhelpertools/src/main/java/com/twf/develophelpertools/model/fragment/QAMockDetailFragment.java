package com.twf.develophelpertools.model.fragment;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Toast;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.model.mock.QAMockUtil;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Created by guofeng
 * on 2019/7/22.
 */

public class QAMockDetailFragment extends BaseKitFragment {


    public static final String QA_MOCK_SP_KEY = "QA_MOCK_SP_KEY";

    private EditText mEditText;

    private String urlKey;

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        Bundle bundle = getArguments();
        if (bundle != null) {
            urlKey = bundle.getString(QA_MOCK_SP_KEY);
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_qa_mock_detail_fragment, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initView(view);
        initValue();
    }


    private void initView(@NonNull View view) {
        Button mSave = view.findViewById(R.id.mSave);
        mEditText = view.findViewById(R.id.mEditText);
        mSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String value = mEditText.getText().toString();
                if (TextUtils.isEmpty(value)) {
                    Toast.makeText(getBaseActivity(), "不可为空", Toast.LENGTH_LONG).show();
                    return;
                }
                if (!checkIsJson(value)) {
                    Toast.makeText(getBaseActivity(), "保存格式有问题", Toast.LENGTH_LONG).show();
                    return;
                }

                Toast.makeText(getBaseActivity(), "保存成功", Toast.LENGTH_LONG).show();
                QAMockUtil.putMockValue(urlKey, value);
                //关闭界面
                getBaseActivity().onBackPressed();

            }
        });
    }

    //检测是否
    private boolean checkIsJson(String value) {
        boolean isJson = true;
        try {
            new JSONObject(value);
        } catch (JSONException e) {
            e.printStackTrace();
            isJson = false;
        }
        return isJson;
    }

    private void initValue() {
        String value = QAMockUtil.getMockValue(urlKey);
        mEditText.setText(value);
    }

}
