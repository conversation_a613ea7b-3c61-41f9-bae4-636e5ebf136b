package com.twf.develophelpertools.model.fragment;

import android.graphics.Rect;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.kit.baselibrary.AppCallBackItf;
import com.kit.baselibrary.ButtonConfigBean;
import com.kit.baselibrary.DetailConfigBean;
import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.activity.TransferCenterKitActivity;
import com.twf.develophelpertools.constant.Constants;
import com.twf.develophelpertools.util.KitScaleUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * create by guofeng
 * date on 2021/8/4
 */

public class DetailConfigFragment extends BaseKitFragment {

    private RecyclerView mRecycleView;

    public static DetailConfigFragment newInstance() {
        return new DetailConfigFragment();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_common_tools_fragment, container, false);
    }


    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mRecycleView = view.findViewById(R.id.mRecycleView);
        mRecycleView.setLayoutManager(new GridLayoutManager(getBaseActivity(), 3));
        mRecycleView.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, int itemPosition, @NonNull RecyclerView parent) {
                super.getItemOffsets(outRect, itemPosition, parent);
                outRect.set(0, KitScaleUtil.dip2px(30), KitScaleUtil.dip2px(10), 0);
            }
        });

        AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
        if (appCallBackItf != null) {
            List<DetailConfigBean> buttonConfigList = appCallBackItf.getDetailConfigList();
            ButtonConfigAdapter adapter = new ButtonConfigAdapter();
            adapter.setData(buttonConfigList);
            mRecycleView.setAdapter(adapter);
        }
    }



    private class ButtonConfigAdapter extends RecyclerView.Adapter<ButtonConfigAdapter.CommonToolViewHolder> {

        private final List<DetailConfigBean> data = new ArrayList<>();


        private void setData(List<DetailConfigBean> source) {
            data.clear();
            if (source != null) {
                data.addAll(source);
            }
            notifyDataSetChanged();
        }


        @Override
        public CommonToolViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            return new CommonToolViewHolder(LayoutInflater.from(getContext()).inflate(R.layout.kit_common_tool_item, null));
        }

        @Override
        public void onBindViewHolder(CommonToolViewHolder holder, int position) {
            DetailConfigBean commonToolsBean = data.get(position);
            if (commonToolsBean != null) {
                holder.mName.setText(commonToolsBean.getName());
                final String detailText = commonToolsBean.getDetailText();
                holder.itemView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        Bundle args = new Bundle();
                        args.putString(Constants.BUNDLE_STRING_KEY, detailText);
                        TransferCenterKitActivity.jump(getBaseActivity(), DetailTextFragment.class, args);
                    }
                });
            }
        }

        @Override
        public int getItemCount() {
            return data.size();
        }

        public class CommonToolViewHolder extends RecyclerView.ViewHolder {


            private TextView mName;

            public CommonToolViewHolder(View itemView) {
                super(itemView);

                mName = itemView.findViewById(R.id.mName);

            }
        }

    }

}