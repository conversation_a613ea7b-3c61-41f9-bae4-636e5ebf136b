package com.twf.develophelpertools.model;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.bean.JiraItemBean;
import com.twf.develophelpertools.util.IssueSPManager;

import java.util.List;

/**
 * create by guofeng
 * date on 2021/5/10
 */

public class IssueTaskManagerViewModel extends ViewModel {

    /**
     * 所有的任务数据
     */
    public final MutableLiveData<List<JiraItemBean>> taskViewModel = new MutableLiveData<>();


    private static IssueTaskManagerViewModel jiraTaskManagerViewModel;

    /**
     * 获得全局数据
     *
     * @return
     */
    public static IssueTaskManagerViewModel createViewModel() {
        if (jiraTaskManagerViewModel == null) {
            jiraTaskManagerViewModel = ViewModelProvider.AndroidViewModelFactory.getInstance(Kit.getInstance().getApplication()).create(IssueTaskManagerViewModel.class);
        }
        return jiraTaskManagerViewModel;
    }

    /**
     * 创建数据
     */
    public void initSourceData() {
        List<JiraItemBean> allJiraList = IssueSPManager.getInstance().getIssueJiraList();
        taskViewModel.postValue(allJiraList);
    }

    /**
     * 保存jira数据
     */
    public void saveAllJiraList() {

        List<JiraItemBean> value = taskViewModel.getValue();
        if (value != null) {
            IssueSPManager.getInstance().saveAllIssueList(value);
        }

    }

} 