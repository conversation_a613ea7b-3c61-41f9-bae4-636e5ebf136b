package com.twf.develophelpertools.model;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.bean.JiraItemBean;

/**
 * create by guofeng
 * date on 2021/5/10
 */

public class IssueTaskViewModel extends ViewModel {



    /**
     * 当前编辑的任务数据
     */
    public final MutableLiveData<JiraItemBean> currentEditViewMode = new MutableLiveData<>();

    /**
     * 当前创建的任务数据
     */
    public final MutableLiveData<JiraItemBean> currentCreateViewMode = new MutableLiveData<>();




    /**
     * 获得全局数据
     *
     * @return
     */
    public static IssueTaskViewModel createViewModel() {
        return ViewModelProvider.AndroidViewModelFactory.getInstance(Kit.getInstance().getApplication()).create(IssueTaskViewModel.class);
    }



} 