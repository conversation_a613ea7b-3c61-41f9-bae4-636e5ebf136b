package com.twf.develophelpertools.model.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.EditText;
import android.widget.ListView;
import android.widget.SimpleAdapter;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.util.KitActivityUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by guofeng
 * on 2019/6/5.
 */

public class TargetActListFragment extends BaseKitFragment {



    private ListView mListView;
    private EditText mEditText;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_target_act_list_fragment, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mEditText = view.findViewById(R.id.mEditText);
        mListView = view.findViewById(R.id.mListView);
        mEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                queryActivity(s.toString());
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
    }

    private void queryActivity(String actName) {
        final List<Class> result = new ArrayList<>();
        List<Class> allActivity = getAllActivity();
        for (Class activity : allActivity) {
            String simpleName = activity.getName();
            if (simpleName.contains(actName)) {
                result.add(activity);
            }
        }

        List<Map<String, String>> mapList = new ArrayList<>();
        for (Class act : result) {
            Map<String, String> map = new HashMap<>();
            map.put("act", act.getName());
            mapList.add(map);
        }

        final SimpleAdapter adapter = new SimpleAdapter(getBaseActivity(), mapList,
                R.layout.kit_item_info_lv, new String[]{"act"},// 与下面数组元素要一一对应
                new int[]{R.id.item_content});
        mListView.setAdapter(adapter);
        mListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                Class activity = result.get(position);
                if (activity != null) {
                    try {
                        Intent intent = new Intent(getBaseActivity(), activity);
                        getBaseActivity().startActivity(intent);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        });
    }

    private List<Class> mActivities;

    private List<Class> getAllActivity() {
        if (mActivities == null) {
            mActivities = KitActivityUtil.getActivitiesByApplication(Kit.getInstance().getApplication());
        }
        return mActivities;
    }


}
