package com.twf.develophelpertools.model.fragment;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.ActivityInfo;
import android.graphics.Bitmap;
import android.os.Build;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

import com.kit.baselibrary.AppInfoBean;
import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;

/**
 * create by guofeng
 * date on 2021/8/17
 */

public class TestPlatFragment extends BaseKitFragment {

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_test_platform_fragment, container, false);
    }

    @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR1)
    @SuppressLint("SetJavaScriptEnabled")
    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);


        WebView mWebView = view.findViewById(R.id.mWebView);
        TextView mUserInfo = view.findViewById(R.id.mUserInfo);

        StringBuilder stringBuilder = new StringBuilder();
        AppInfoBean appInfo = Kit.getInstance().getAppCallBackItf().getAppInfo();

        stringBuilder.append(" 用户手机号: ");
        if (appInfo != null) {
            stringBuilder.append(appInfo.getPhoneNumber());
        }

        stringBuilder.append(" userId: ");
        if (appInfo != null) {
            stringBuilder.append(appInfo.getUserId());
        }

        mUserInfo.setText(stringBuilder.toString());

        WebSettings settings = mWebView.getSettings();
        settings.setUseWideViewPort(true);
        settings.setLoadWithOverviewMode(true);
        settings.setJavaScriptEnabled(true);
        settings.setDefaultTextEncodingName("UTF-8");
        settings.setBuiltInZoomControls(true);
        settings.setDomStorageEnabled(true);
        settings.setDatabaseEnabled(true);
//        settings.setAppCacheEnabled(true);
        settings.setMediaPlaybackRequiresUserGesture(true);

        settings.setPluginState(WebSettings.PluginState.ON);
        settings.setAllowFileAccess(true);

        settings.setSupportZoom(true);


        settings.setBuiltInZoomControls(true);
        settings.setDisplayZoomControls(true);

        mWebView.setInitialScale(80);

        settings.setUseWideViewPort(true);


        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            settings.setAllowFileAccessFromFileURLs(false);
            settings.setAllowUniversalAccessFromFileURLs(false);
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            WebView.setWebContentsDebuggingEnabled(true);
        }


        String appCachePath = getBaseActivity().getApplication().getCacheDir().getAbsolutePath();
//        settings.setAppCachePath(appCachePath);
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
            settings.setDatabasePath("/data/data/" + mWebView.getContext().getPackageName() + "/databases/");
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            mWebView.setForceDarkAllowed(false);
        }

        mWebView.setWebViewClient(new WebViewClient() {
            //覆盖shouldOverrideUrlLoading 方法
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                view.loadUrl(url);
                return true;
            }

            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                getBaseActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
            }
        });

        mWebView.loadUrl("http://qa.kanzhun-inc.com/qa/#/tools/business/code");
    }

}