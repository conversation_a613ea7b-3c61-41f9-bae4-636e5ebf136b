package com.twf.develophelpertools.model.fragment;

import com.twf.develophelpertools.model.layoutborder.LayoutBorderConfig;
import com.twf.develophelpertools.model.layoutborder.LayoutBorderManager;
import com.twf.develophelpertools.model.layoutborder.LayoutLevelFloatPage;
import com.twf.develophelpertools.model.pickcolor.FloatPageManager;
import com.twf.develophelpertools.model.pickcolor.PageIntent;

/**
 * Created by guofeng
 * on 2019/6/27.
 */

public class LayoutBorderFragment  {


   public static void initLayoutBorder(){
       LayoutBorderManager.getInstance().start();
       LayoutBorderConfig.setLayoutBorderOpen(true);

       PageIntent intent = new PageIntent(LayoutLevelFloatPage.class);
       intent.mode = PageIntent.MODE_SINGLE_INSTANCE;
       FloatPageManager.getInstance().add(intent);
       LayoutBorderConfig.setLayoutLevelOpen(true);
   }
}
