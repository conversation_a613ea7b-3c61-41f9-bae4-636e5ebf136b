package com.twf.develophelpertools.model.mock;

import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.kit.baselibrary.AppCallBackItf;
import com.kit.baselibrary.MockConfigBean;
import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.thread.KitThreadPool;
import com.twf.develophelpertools.util.KitSpUtil;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Created by guofeng
 * on 2019/7/18.
 */

public class DevelopMockUtil {


    /**
     * 接口数据对象
     */
    public static class MockURLBean {

        public String originUrl;

        public String mockUrl;

        public boolean isMocked;

        String classPath;

        String fieldName;

        @Override
        public String toString() {
            return "MockURLBean{" +
                    "originUrl='" + originUrl + '\'' +
                    ", mockUrl='" + mockUrl + '\'' +
                    ", isMocked=" + isMocked +
                    ", classPath='" + classPath + '\'' +
                    ", fieldName='" + fieldName + '\'' +
                    '}';
        }
    }

    private final static DevelopMockUtil instance = new DevelopMockUtil();

    private DevelopMockUtil() {
    }

    public static DevelopMockUtil getInstance() {
        return instance;
    }

    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();

    //是否已经在mock
    public static boolean containMock(String url) {
        for (MockURLBean mockURLBean : MOCK_LIST) {
            if (TextUtils.equals(mockURLBean.originUrl, url)) {
                return mockURLBean.isMocked;
            }
        }
        return false;
    }


    private static final List<MockURLBean> MOCK_LIST = new ArrayList<>();

    //初始化MOCK
    public void initMock() {
        try {
            lock.writeLock().lock();
            MOCK_LIST.clear();
            getConfigUrlList();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            lock.writeLock().unlock();
        }
    }


    /**
     * 获得数据源
     *
     * @return
     */
    public List<String> getAllUlrList() {
        final List<String> result = new ArrayList<>();
        try {
            lock.readLock().lock();
            for (MockURLBean mockURLBean : MOCK_LIST) {
                if (mockURLBean == null) continue;
                result.add(mockURLBean.originUrl);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            lock.readLock().unlock();
        }
        return result;
    }

    /**
     * 反射MOCK接口数据
     *
     * @param url
     */
    public void reflateSelectMockUrl(@NonNull String url) {
        try {
            lock.readLock().lock();
            for (MockURLBean mockURLBean : MOCK_LIST) {
                if (mockURLBean == null) continue;
                if (TextUtils.equals(mockURLBean.originUrl, url)) {
                    if (mockURLBean.isMocked) return;
                    Field field = getField(mockURLBean);
                    if (field != null) {
                        field.set(null, mockURLBean.mockUrl);
                        mockURLBean.isMocked = true;
                    }
                    return;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            lock.readLock().unlock();
            //同步到db
            saveSyncToDb();
        }
    }


    public void onAllChangeListener(boolean select) {
        try {
            lock.readLock().lock();
            for (MockURLBean mockURLBean : MOCK_LIST) {
                if (mockURLBean == null) continue;
                Field field = getField(mockURLBean);
                if (select) {
                    if (field != null) {
                        field.set(null, mockURLBean.mockUrl);
                        mockURLBean.isMocked = true;
                    }

                } else {
                    if (field != null) {
                        field.set(null, mockURLBean.originUrl);
                        mockURLBean.isMocked = false;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            lock.readLock().unlock();
            //同步到db
            saveSyncToDb();
        }
    }

    /**
     * 反射取消MOCK的URL
     *
     * @param url
     */
    public void reflateUnSelectMockUrl(@NonNull String url) {
        try {
            lock.readLock().lock();
            for (MockURLBean mockURLBean : MOCK_LIST) {
                if (mockURLBean == null) continue;
                if (TextUtils.equals(mockURLBean.originUrl, url)) {
                    if (!mockURLBean.isMocked) return;

                    Field field = getField(mockURLBean);
                    if (field != null) {
                        field.set(null, mockURLBean.originUrl);
                        mockURLBean.isMocked = false;
                    }

                    return;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            lock.readLock().unlock();
            //同步到db
            saveSyncToDb();
        }
    }


    public static final String SP_MOCK_KEY = "SP_MOCK_KEY_MANAGER";

    //同步保存数据
    private void saveSyncToDb() {
        String jsonValue = "";
        try {
            lock.readLock().lock();
            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.setPrettyPrinting();
            Gson gson = gsonBuilder.create();
            jsonValue = gson.toJson(MOCK_LIST);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            lock.readLock().unlock();
            if (!TextUtils.isEmpty(jsonValue)) {
                KitSpUtil.putString(SP_MOCK_KEY, jsonValue);
            }
        }
    }

    //获得db里面的数据
    private static List<MockURLBean> getDBMockUrl() {
        String value = KitSpUtil.getString(SP_MOCK_KEY);
        if (value != null) {
            return new Gson().fromJson(value, new TypeToken<List<MockURLBean>>() {
            }.getType());
        }
        return null;
    }

    /**
     * 根据MockUrlBean获得Field对象
     *
     * @param mockURLBean
     * @return
     */
    private static Field getField(MockURLBean mockURLBean) {
        try {
            String classPath = mockURLBean.classPath;
            String fieldName = mockURLBean.fieldName;
            Class<?> aClass = Class.forName(classPath);
            Field field = aClass.getDeclaredField(fieldName);
            //设置权限 去掉final
            int modifiers = field.getModifiers();
            if (Modifier.isFinal(modifiers) && Modifier.isPublic(modifiers)) {
                Field accessFlags = field.getClass().getDeclaredField("accessFlags");
                accessFlags.setAccessible(true);
                accessFlags.set(field, modifiers & ~Modifier.FINAL);
                return field;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    //获得所有url
    private void getConfigUrlList() {

        KitThreadPool.executor(new Runnable() {
            @Override
            public void run() {

                List<Class<?>> configClass = null;
                List<String> mockTag = null;
                String preMockUrl = "";
                AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
                if (appCallBackItf != null) {
                    MockConfigBean mockConfigBean = appCallBackItf.getMockConfigBean();
                    if (mockConfigBean != null) {
                        configClass = mockConfigBean.getMockUrlClassList();
                        mockTag = mockConfigBean.getMockFilter();
                        preMockUrl = mockConfigBean.getMockReplacePrefixUrl();
                    }
                }


                List<MockURLBean> dbMockUrl = getDBMockUrl();

                if (configClass != null) {
                    for (Class aClass : configClass) {
                        Field[] fields = aClass.getDeclaredFields();
                        if (fields != null) {
                            for (Field field : fields) {
                                if (field == null) continue;
                                field.setAccessible(true);
                                try {
                                    Object value = field.get(null);
                                    if (value instanceof String) {


                                        String url = (String) value;

                                        int index = -1;

                                        for (String tagValue : mockTag) {

                                            if (TextUtils.isEmpty(tagValue)) continue;

                                            index = url.indexOf(tagValue);


                                            if (index >= 0) {

                                                int startIndex = index + tagValue.length();
                                                String substring = url.substring(startIndex);
                                                String newUrl = preMockUrl + substring;


                                                MockURLBean mockURLBean = new MockURLBean();
                                                mockURLBean.originUrl = url;
                                                mockURLBean.mockUrl = newUrl;
                                                mockURLBean.isMocked = checkIsMockedFromDb(dbMockUrl, url);
                                                mockURLBean.classPath = aClass.getName();
                                                mockURLBean.fieldName = field.getName();
                                                MOCK_LIST.add(mockURLBean);
                                                break;
                                            }

                                        }


                                    }
                                } catch (IllegalAccessException e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                }

                //反射修改MOCK的URL
                try {
                    for (MockURLBean mockURLBean : MOCK_LIST) {
                        if (mockURLBean == null) continue;
                        if (mockURLBean.isMocked) {
                            Field field = getField(mockURLBean);
                            if (field != null) {
                                field.set(null, mockURLBean.mockUrl);
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

    }

    //从db检查是否mocked
    private boolean checkIsMockedFromDb(List<MockURLBean> dbMockUrl, String url) {
        if (dbMockUrl != null) {
            for (MockURLBean mockURLBean : dbMockUrl) {
                if (mockURLBean == null) continue;
                if (TextUtils.equals(mockURLBean.originUrl, url)) {
                    return mockURLBean.isMocked;
                }
            }
        }
        return false;
    }


}
