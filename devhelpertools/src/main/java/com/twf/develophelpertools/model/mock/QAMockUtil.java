package com.twf.develophelpertools.model.mock;

import android.text.TextUtils;

import com.twf.develophelpertools.util.KitSpUtil;

import java.util.Set;

/**
 * Created by guofeng
 * on 2019/7/22.
 */
public class QAMockUtil {

    private static final String SP_SELECT_UTL = "SP_SELECT_URL";


    //所有选择的mock集合
    public static Set<String> getAllSelectCollection() {
        return KitSpUtil.getStringSet(SP_SELECT_UTL);
    }


    //是否包含当前url
    public static boolean containMock(String url) {
        Set<String> collection = getAllSelectCollection();
        return collection.contains(url);
    }


    //添加单个url
    public static void putSelect(String url) {
        Set<String> collection = getAllSelectCollection();
        if (collection.contains(url)) return;
        collection.add(url);
        KitSpUtil.putStringSet(SP_SELECT_UTL, collection);
    }

    //取消选择
    public static void removeSelect(String url) {
        Set<String> collection = getAllSelectCollection();
        if (!collection.contains(url)) return;
        collection.remove(url);
        KitSpUtil.putStringSet(SP_SELECT_UTL, collection);
    }


    private static String makeKey(String url) {
        return "SP_QA_MOCK_URL" + "_" + url;
    }

    //获得Mock的数据
    public static String getMockValue(String url) {
        if (TextUtils.isEmpty(url)) return "";
        return KitSpUtil.getString(makeKey(url));
    }

    //保存单个url&value
    public static void putMockValue(String url, String value) {
        if (TextUtils.isEmpty(url)) return;
        KitSpUtil.putString(makeKey(url), value);
    }

}
