package com.twf.develophelpertools.model.fragment;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ListView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.kit.baselibrary.AppCallBackItf;
import com.kit.baselibrary.AppInfoBean;
import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.appinfo.AppInfoAdapter;
import com.twf.develophelpertools.bean.AppInfo;
import com.twf.develophelpertools.model.DemoGLSurfaceView;
import com.twf.develophelpertools.util.KitClipBoardUtil;
import com.twf.develophelpertools.util.KitDeviceInfoUtil;
import com.twf.develophelpertools.util.KitPermissionUtil;

import javax.microedition.khronos.egl.EGLConfig;
import javax.microedition.khronos.opengles.GL10;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by guofeng
 * on 2019/5/29.
 */

public class AppBaseInfoFragment extends BaseKitFragment {

    private ListView mListView;
    private AppInfoAdapter adapter;

    private String gpuInfo;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_app_base_info_fragment, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mListView = view.findViewById(R.id.mListView);
        final ViewGroup parent = (ViewGroup) mListView.getParent();
        final DemoGLSurfaceView glView = new DemoGLSurfaceView(getBaseActivity());
        glView.setCallBack(new DemoGLSurfaceView.OnSurfaceCallBack() {

            @Override
            public void onSurfaceListener(GL10 gl, EGLConfig config) {

                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(gl.glGetString(GL10.GL_RENDERER));
                stringBuilder.append(gl.glGetString(GL10.GL_VENDOR));
                stringBuilder.append(gl.glGetString(GL10.GL_VERSION));
                stringBuilder.append(gl.glGetString(GL10.GL_EXTENSIONS));
                gpuInfo = stringBuilder.toString();
                refreshAdapter();
                parent.removeView(glView);
            }
        });

        parent.addView(glView);

        mListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                if (adapter != null) {
                    Object item = adapter.getItem(position);
                    if (item instanceof AppInfo) {
                        AppInfo appInfo = (AppInfo) item;
                        String desc = appInfo.getDesc();

                        if (!TextUtils.isEmpty(desc)) {
                            Toast.makeText(view.getContext(), "内容已复制：" + desc, Toast.LENGTH_SHORT).show();
                            KitClipBoardUtil.copyToClipBoard(desc);
                        }
                    }
                }
            }
        });
    }

    private void refreshAdapter() {
        if (adapter == null) {
            adapter = new AppInfoAdapter(getBaseActivity());
            mListView.setAdapter(adapter);
        }
        adapter.setData(getAppInfoList());
    }


    @Override
    public void onResume() {
        super.onResume();
        refreshAdapter();
    }

    private List<AppInfo> getAppInfoList() {
        List<AppInfo> result = new ArrayList<>();
        String userId = "";
        String phoneNumber = "";
        AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
        if (appCallBackItf != null) {
            AppInfoBean appInfo = appCallBackItf.getAppInfo();
            if (appInfo != null) {
                userId = appInfo.getUserId();
                phoneNumber = appInfo.getPhoneNumber();
            }
        }
        /**
         * APP信息
         */
        result.add(getAppTitle("App信息"));
        result.add(getAppInfo("用户ID", userId));
        result.add(getAppInfo("当前账户手机号", phoneNumber));
        result.add(getAppInfo("包名", KitDeviceInfoUtil.getAppPackageName()));
        result.add(getAppInfo("应用版本名", KitDeviceInfoUtil.getAppVersion()));
        result.add(getAppInfo("应用版本号", KitDeviceInfoUtil.getAppVersionName()));
        result.add(getAppInfo("最低系统版本", KitDeviceInfoUtil.minSupportVersion()));
        result.add(getAppInfo("目标系统版本", KitDeviceInfoUtil.targetSupportVersion()));
        /**
         * 设备信息
         */
        result.add(getAppTitle("设备信息"));
        result.add(getAppInfo("ROOT", KitDeviceInfoUtil.isRoot() ? "YES" : "NO"));
        result.add(getAppInfo("手机信息", KitDeviceInfoUtil.getDeviceName()));
        result.add(getAppInfo("系统版本", KitDeviceInfoUtil.getDeviceVersion()));
        result.add(getAppInfo("devicePixelRatio", KitDeviceInfoUtil.getevicePixelRatio()));
        result.add(getAppInfo("width", KitDeviceInfoUtil.getScreenWidth()));
        result.add(getAppInfo("height", KitDeviceInfoUtil.getScreenHeight()));
        result.add(getAppInfo("availWidth", KitDeviceInfoUtil.getAvailableWidth()));
        result.add(getAppInfo("availHeight", KitDeviceInfoUtil.getAvailableHeight()));
        result.add(getAppInfo("SD卡剩余空间", KitDeviceInfoUtil.getSDCardSpace(Kit.getInstance().getApplication())));
        result.add(getAppInfo("系统剩余空间", KitDeviceInfoUtil.getDeviceRemainMemory()));
        result.add(getAppInfo("timeZone", KitDeviceInfoUtil.getTimeZone()));
        result.add(getAppInfo("cpuClass", KitDeviceInfoUtil.getCPUInfo()));
        result.add(getAppInfo("GPU 信息", gpuInfo));
        /**
         * 权限信息
         */
        result.add(getAppTitle("权限信息"));
        result.add(getAppInfo("地理位置权限", KitPermissionUtil.hasLocationPermission() ? "YES" : "NO"));
        result.add(getAppInfo("磁盘权限", KitPermissionUtil.hasWriteExternalPermission() ? "YES" : "NO"));
        result.add(getAppInfo("拍照权限", KitPermissionUtil.hasCameraPermission() ? "YES" : "NO"));
        result.add(getAppInfo("麦克风权限", KitPermissionUtil.hasRecordPermission() ? "YES" : "NO"));
        result.add(getAppInfo("通讯录权限", KitPermissionUtil.hasReadContactPermission() ? "YES" : "NO"));
        return result;
    }


    private AppInfo getAppTitle(String title) {
        AppInfo appInfo = new AppInfo();
        appInfo.setTitle(title);
        appInfo.setDesc("");
        appInfo.setType(AppInfo.TYPE_TITLE);
        return appInfo;
    }


    private AppInfo getAppInfo(String title, String desc) {
        AppInfo appInfo = new AppInfo();
        appInfo.setTitle(title);
        appInfo.setDesc(desc);
        appInfo.setType(AppInfo.TYPE_CONTENT);
        return appInfo;
    }


}
