package com.twf.develophelpertools.model.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.crash.KitUncaughtHandler;
import com.twf.develophelpertools.itf.LoadTargetFragmentItf;
import com.twf.develophelpertools.util.KitDialogUtil;
import com.twf.develophelpertools.util.KitFileUtil;

import java.io.File;

/**
 * Created by guofeng
 * on 2019/6/24.
 */
public class CrashMainFragment extends BaseKitFragment {

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_crash_main_fragment, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initView(view);
    }

    // 初始化View
    private void initView(@NonNull View view) {
        TextView mCrashLogcat = view.findViewById(R.id.mCrashLogcat);
        TextView mClearLogcat = view.findViewById(R.id.mClearLogcat);
        //crash开关
        mCrashLogcat.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                LoadTargetFragmentItf loadTargetFragmentItf = loadTargetFragmentItf();
                if (loadTargetFragmentItf != null) {
                    loadTargetFragmentItf.loadFragment(CrashDetailFragment.class, null);
                }
            }
        });
        //清除所有奔溃日志
        mClearLogcat.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                KitDialogUtil kitDialogUtil = new KitDialogUtil(getBaseActivity());
                kitDialogUtil.setTitle("确定删除?");
                kitDialogUtil.setPositiveListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        //清除文件夹
                        File crashRootFile = KitFileUtil.getCrashRootFile();
                        KitFileUtil.deleteFile(crashRootFile);
                        Toast.makeText(getBaseActivity(), "删除成功", Toast.LENGTH_SHORT).show();
                    }
                });
                kitDialogUtil.show();

            }
        });
    }

    // 检测奔溃开关
    public static void checkCrashHandler() {
        Thread.setDefaultUncaughtExceptionHandler(new KitUncaughtHandler());
    }

}
