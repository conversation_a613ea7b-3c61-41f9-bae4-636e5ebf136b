package com.twf.develophelpertools.model.fragment;

import android.app.Activity;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.Toast;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.service.MockClickService;
import com.twf.develophelpertools.util.KitDialogUtil;
import com.twf.develophelpertools.util.KitKeyboardUtil;
import com.twf.develophelpertools.util.KitMainHandler;

/**
 * Created by guofeng
 * on 2019/7/22.
 */

public class MockFragment extends BaseKitFragment {


    private EditText mEditHour;
    private EditText mEditMinute;
    private EditText mEditSecond;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_monkey_fragment, container, false);
    }


    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        mEditHour = view.findViewById(R.id.mEditHour);
        mEditMinute = view.findViewById(R.id.mEditMinute);
        mEditSecond = view.findViewById(R.id.mEditSecond);

        view.findViewById(R.id.mStart).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startMonkey();
            }
        });

        view.findViewById(R.id.mTips).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Activity baseActivity = getBaseActivity();
                new KitDialogUtil(baseActivity).show(LayoutInflater.from(baseActivity).inflate(R.layout.kit_mock_click_guide, null));
            }
        });
    }

    /**
     * 开启点击事件
     */
    private void startMonkey() {
        if (checkEmpty()) return;

        KitKeyboardUtil.hideSoftInput(getBaseActivity());

        KitMainHandler.getMainHandler().postDelayed(new Runnable() {
            @Override
            public void run() {
                getBaseActivity().setResult(Activity.RESULT_OK);

                getBaseActivity().finish();

                MockClickService.startMockClickService(getBaseActivity(), getTotalSecond());
            }
        }, 200);

    }


    /**
     * 判断是否为空信息
     *
     * @return
     */
    private boolean checkEmpty() {
        String hour = mEditHour.getText().toString();
        String minute = mEditMinute.getText().toString();
        String second = mEditSecond.getText().toString();

        if (TextUtils.isEmpty(hour)
                && TextUtils.isEmpty(minute)
                && TextUtils.isEmpty(second)) {
            Toast.makeText(getBaseActivity(), "请输入 时/ 分 /秒 信息", Toast.LENGTH_LONG).show();
            return true;
        }
        return false;
    }


    private int getTotalSecond() {

        int totalSecond = 0;

        String hour = mEditHour.getText().toString();
        String minute = mEditMinute.getText().toString();
        String second = mEditSecond.getText().toString();

        if (!TextUtils.isEmpty(hour)) {
            totalSecond += Integer.parseInt(hour) * 60 * 60;
        }
        if (!TextUtils.isEmpty(minute)) {
            totalSecond += Integer.parseInt(minute) * 60;
        }
        if (!TextUtils.isEmpty(second)) {
            totalSecond += Integer.parseInt(second);
        }
        return totalSecond;
    }

}
