package com.twf.develophelpertools.model.fragment;

import android.Manifest;
import android.app.Activity;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.amap.api.maps.AMap;
import com.amap.api.maps.AMapOptions;
import com.amap.api.maps.CameraUpdate;
import com.amap.api.maps.CameraUpdateFactory;
import com.amap.api.maps.MapView;
import com.amap.api.maps.UiSettings;
import com.amap.api.maps.model.CameraPosition;
import com.amap.api.maps.model.LatLng;
import com.amap.api.maps.model.Marker;
import com.amap.api.maps.model.MarkerOptions;
import com.amap.api.maps.model.MyLocationStyle;
import com.amap.api.services.core.LatLonPoint;
import com.amap.api.services.geocoder.GeocodeResult;
import com.amap.api.services.geocoder.GeocodeSearch;
import com.amap.api.services.geocoder.RegeocodeAddress;
import com.amap.api.services.geocoder.RegeocodeQuery;
import com.amap.api.services.geocoder.RegeocodeResult;
import com.amap.api.services.geocoder.StreetNumber;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.util.KitDialogUtil;
import com.twf.develophelpertools.util.KitSpUtil;

import org.json.JSONObject;

/**
 * create by guofeng
 * date on 2020/7/28
 */
public class MockLocationFragment extends BaseKitFragment {

    public static final String SP_MOCK_LOCATION_INFO = "SP_MOCK_LOCATION_INFO";

    public static final String PROVINCE = "province";
    public static final String CITY = "city";
    public static final String DISTRICT = "district";
    public static final String CITY_CODE = "cityCode";
    public static final String STREET_NUMBER = "streetNumber";
    public static final String FORMAT_ADDRESS = "formatAddress";
    public static final String LOCAL_LATITUDE = "localLatitude";
    public static final String LOCAL_LONGITUDE = "localLongitude";

    private AMap mMap;
    private MapView mMapView;
    private Marker marker;
    private AMapLocationClient mlocationClient;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_mock_location_fragment, container, false);
    }


    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mMapView = view.findViewById(R.id.mMapView);
        mMapView.onCreate(savedInstanceState);
        mMap = mMapView.getMap();
        try {
            initMap();
            onActivity();
            checkLocationPermission();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private double localLatitude;
    private double localLongitude;

    private void initMap() {
        AMapLocationClient.updatePrivacyShow(getBaseActivity(), true, true);
        AMapLocationClient.updatePrivacyAgree(getBaseActivity(), true);
        UiSettings uiSettings = mMap.getUiSettings();
        uiSettings.setMyLocationButtonEnabled(false);
        uiSettings.setLogoPosition(AMapOptions.LOGO_POSITION_BOTTOM_RIGHT);
        uiSettings.setZoomControlsEnabled(false);
        uiSettings.setMyLocationButtonEnabled(true); //显示默认的定位按钮
        mMap.setMyLocationEnabled(false);

        //定义了当可视范围改变时回调的接口
        mMap.setOnCameraChangeListener(new AMap.OnCameraChangeListener() {

            @Override
            public void onCameraChange(CameraPosition cameraPosition) {
                marker.hideInfoWindow(); // 拖动地图，隐藏信息气泡

            }

            @Override
            public void onCameraChangeFinish(CameraPosition cameraPosition) {
                marker.setPosition(cameraPosition.target);
                LatLng position = marker.getPosition();
                useLocation(position.latitude, position.longitude);
                localLatitude = position.latitude;
                localLongitude = position.longitude;
                //transfer code to location info
                geoCodeSearch();
            }
        });
    }


    private RegeocodeAddress address;

    private GeocodeSearch geocodeSearch;

    private void geoCodeSearch() {
        if (geocodeSearch == null) {
            geocodeSearch = new GeocodeSearch(getBaseActivity());
            geocodeSearch.setOnGeocodeSearchListener(new GeocodeSearch.OnGeocodeSearchListener() {

                @Override
                public void onRegeocodeSearched(RegeocodeResult regeocodeResult, int i) {
                    if (regeocodeResult != null && regeocodeResult.getRegeocodeAddress() != null) {
                        address = regeocodeResult.getRegeocodeAddress();
                    }
                }

                @Override
                public void onGeocodeSearched(GeocodeResult geocodeResult, int i) {

                }
            });
        }
        LatLonPoint lp = new LatLonPoint(localLatitude, localLongitude);
        RegeocodeQuery query = new RegeocodeQuery(lp, 1000, GeocodeSearch.AMAP);
        geocodeSearch.getFromLocationAsyn(query);

    }


    @Override
    public boolean onBackPressListener() {
        final Activity baseActivity = getBaseActivity();
        if (baseActivity != null) {
            KitDialogUtil kitDialogUtil = new KitDialogUtil(baseActivity);
            kitDialogUtil.setMessage(" 是否保存经纬度?");
            kitDialogUtil.setPositiveListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    String city = address.getCity();
                    String province = address.getProvince();
                    String district = address.getDistrict();
                    String cityCode = address.getCityCode();
                    StreetNumber streetNumber = address.getStreetNumber();
                    String formatAddress = address.getFormatAddress();

                    JSONObject jsonObject = new JSONObject();
                    try {
                        jsonObject.put(PROVINCE, province);
                        jsonObject.put(CITY, city);
                        jsonObject.put(DISTRICT, district);
                        jsonObject.put(CITY_CODE, cityCode);
                        jsonObject.put(STREET_NUMBER, streetNumber);
                        jsonObject.put(FORMAT_ADDRESS, formatAddress);
                        jsonObject.put(LOCAL_LATITUDE, localLatitude);
                        jsonObject.put(LOCAL_LONGITUDE, localLongitude);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    KitSpUtil.putString(SP_MOCK_LOCATION_INFO, jsonObject.toString());

                    Toast.makeText(baseActivity, "修改定位成功", Toast.LENGTH_SHORT).show();

                    baseActivity.finish();

                }
            });
            kitDialogUtil.setNegativeListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    getBaseActivity().finish();
                }
            });
            kitDialogUtil.show();
        }
        return false;
    }


    /**
     * 标记Marker
     *
     * @param latitude
     * @param longitude
     */
    private void useLocation(double latitude, double longitude) {
        //标记出定位的位置
        mMap.clear();
        MarkerOptions markerOptions = new MarkerOptions();
        markerOptions.title("title");
        markerOptions.snippet("snippet");
        markerOptions.draggable(false);
        markerOptions.zIndex(2);
        marker = mMap.addMarker(markerOptions);
        marker.setPositionByPixels(mMapView.getWidth() / 2, mMapView.getHeight() / 2);
        marker.showInfoWindow();

        //缩放一共18级,从3-21,数字越大代表越精细
        CameraUpdate update = CameraUpdateFactory
                .newLatLng(new LatLng(latitude, longitude));
        mMap.moveCamera(update);

    }

    /**
     * 方法必须重写
     */
    @Override
    public void onResume() {
        super.onResume();
        mMapView.onResume();
    }

    /**
     * 方法必须重写
     */
    @Override
    public void onPause() {
        super.onPause();
        mMapView.onPause();
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        mMapView.onSaveInstanceState(outState);
    }


    private boolean onnce = true;

    private void onActivity() throws Exception {
        mlocationClient = new AMapLocationClient(getBaseActivity());
//初始化定位参数
        AMapLocationClientOption mLocationOption = new AMapLocationClientOption();
        //设置为高精度定位模式
        mLocationOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);
        //设置定位参数
        mlocationClient.setLocationOption(mLocationOption);
        mlocationClient.setLocationListener(new AMapLocationListener() {

            @Override
            public void onLocationChanged(AMapLocation aMapLocation) {

                Log.d(TAG, "onLocationChanged: ");
                if (onnce) {
                    MyLocationStyle locationStyle = new MyLocationStyle()
                            .strokeWidth(0)
                            .showMyLocation(true);
                    mMap.setMyLocationStyle(locationStyle);
                    mMap.setMyLocationEnabled(false);

                    useLocation(aMapLocation.getLatitude(), aMapLocation.getLongitude());
                }
                onnce = false;
            }
        });
        mlocationClient.startLocation();//启动定位
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mlocationClient != null) {
            mlocationClient.onDestroy();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

    }

    private static final String TAG = "MockLocationFragment";

    @Override
    public void onDestroy() {
        super.onDestroy();
        mMapView.onDestroy();
    }



    public void checkLocationPermission() {
        requestPermissions(new String[]{Manifest.permission.ACCESS_FINE_LOCATION}, 1000);
    }

}


