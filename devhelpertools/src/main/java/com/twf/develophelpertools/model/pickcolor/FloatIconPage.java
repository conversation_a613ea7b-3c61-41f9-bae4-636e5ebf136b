package com.twf.develophelpertools.model.pickcolor;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Rect;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.activity.KITUITransparentActivity;
import com.twf.develophelpertools.activity.MainPanelActivity;
import com.twf.develophelpertools.activity.TransferCenterKitActivity;
import com.twf.develophelpertools.constant.FloatIconConfig;
import com.twf.develophelpertools.service.MockClickService;
import com.twf.develophelpertools.util.KitAppUtil;
import com.twf.develophelpertools.util.KitForegroundUtil;
import com.twf.develophelpertools.util.KitScaleUtil;

/**
 * 悬浮按钮
 * Created by zhangweida on 2018/6/22.
 */

public class FloatIconPage extends BaseFloatPage implements TouchProxy.OnTouchEventListener, FloatPageManager.FloatPageManagerListener {

    protected WindowManager mWindowManager;

    private TouchProxy mTouchProxy = new TouchProxy(this);

    @Override
    protected void onViewCreated(final View view) {
        getRootView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Activity topActivity = KitForegroundUtil.getInstance().getTopActivity();
                if (topActivity instanceof TransferCenterKitActivity) return;
                //关闭stack里面的act
                KitForegroundUtil.getInstance().finishStackPanelAct();

                Context context = view.getContext();
                Intent intent = new Intent(context, MainPanelActivity.class);
//                Intent intent = new Intent(context, PanelKitActivity.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(intent);
            }
        });
        getRootView().setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View view) {
                KitAppUtil.open(KITUITransparentActivity.Type.TYPE_EDIT_ATTR);
                return false;
            }
        });
        getRootView().setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                return mTouchProxy.onTouchEvent(v, event);
            }
        });
    }

    @Override
    protected View onCreateView(Context context, ViewGroup view) {
        return LayoutInflater.from(context).inflate(R.layout.kit_float_launch_icon, view, false);
    }

    @Override
    protected void onLayoutParamsCreated(WindowManager.LayoutParams params) {
        params.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
        params.x = FloatIconConfig.getLastPosX(getContext());
        params.y = FloatIconConfig.getLastPosY(getContext());
        params.width = WindowManager.LayoutParams.WRAP_CONTENT;
        params.height = WindowManager.LayoutParams.WRAP_CONTENT;
    }

    @Override
    protected void onCreate(Context context) {
        mWindowManager = (WindowManager) Kit.getInstance().getApplication().getSystemService(Context.WINDOW_SERVICE);
        FloatPageManager.getInstance().addListener(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        FloatPageManager.getInstance().removeListener(this);
    }

    @Override
    public void onEnterForeground() {
        super.onEnterForeground();
        getRootView().setVisibility(View.VISIBLE);
    }

    @Override
    public void onEnterBackground() {
        super.onEnterBackground();
        getRootView().setVisibility(View.GONE);
    }

    @Override
    public void onMove(int x, int y, int dx, int dy) {
        getLayoutParams().x += dx;
        getLayoutParams().y += dy;

        if (getLayoutParams().x < 0) {
            getLayoutParams().x = 0;
        }

        if (getLayoutParams().y < 0) {
            getLayoutParams().y = 0;
        }

        int width = mWindowManager.getDefaultDisplay().getWidth();
        int height = mWindowManager.getDefaultDisplay().getHeight();

        int rootWidth = getRootView().getWidth();
        int rootHeight = getRootView().getHeight();

        if (getLayoutParams().x > (width - rootWidth)) {
            getLayoutParams().x = width - rootWidth;
        }

        if (getLayoutParams().y > (height - rootHeight)) {
            getLayoutParams().y = height - rootHeight;
        }

        mWindowManager.updateViewLayout(getRootView(), getLayoutParams());
        refreshFlowArea();
    }

    @Override
    public void onUp(int x, int y) {
        FloatIconConfig.saveLastPosX(getContext(), getLayoutParams().x);
        FloatIconConfig.saveLastPosY(getContext(), getLayoutParams().y);
    }


    private void refreshFlowArea() {
        Rect flowArea = MockClickService.flowArea;
        flowArea.left = getLayoutParams().x;
        flowArea.top = getLayoutParams().y;
        flowArea.right = flowArea.left + KitScaleUtil.dip2px(40);
        flowArea.bottom = flowArea.top + KitScaleUtil.dip2px(40);
    }

    @Override
    public void onDown(int x, int y) {

    }

    @Override
    public void onPageAdd(BaseFloatPage page) {
        refreshFlowArea();
        if (page == this) {
            return;
        }
        FloatPageManager.getInstance().remove(this);
        PageIntent intent = new PageIntent(FloatIconPage.class);
        intent.mode = PageIntent.MODE_SINGLE_INSTANCE;
        FloatPageManager.getInstance().add(intent);

    }
}
