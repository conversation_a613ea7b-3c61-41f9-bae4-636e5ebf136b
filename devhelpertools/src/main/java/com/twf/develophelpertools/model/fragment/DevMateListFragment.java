package com.twf.develophelpertools.model.fragment;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ListView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.DevMateMemberAdapter;
import com.twf.develophelpertools.bean.DevMateBean;
import com.twf.develophelpertools.bean.JiraItemBean;
import com.twf.develophelpertools.constant.Constants;
import com.twf.develophelpertools.model.DevMateListViewModel;
import com.twf.develophelpertools.model.IssueCreateTaskViewModel;
import com.twf.develophelpertools.model.IssueEditTaskViewModel;

import java.util.ArrayList;
import java.util.List;

/**
 * create by guofeng
 * date on 2021/5/8
 */

public class DevMateListFragment extends BaseKitFragment {

    public static final int FROM_CREATE = 1;
    public static final int FROM_EDIT = 2;

    private DevMateListViewModel viewModel;

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        viewModel = DevMateListViewModel.createViewModel();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_dev_mate_list_fragment, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        final ListView mListView = view.findViewById(R.id.mListView);
        TextView mSubmit = view.findViewById(R.id.mSubmit);
        mSubmit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                Bundle arguments = getArguments();
                if (arguments != null) {
                    int from = arguments.getInt(Constants.BUNDLE_FILE_KEY);
                    //创建页面
                    if (from == FROM_CREATE) {
                        JiraItemBean value = IssueCreateTaskViewModel.createViewModel().currentViewMode.getValue();
                        if (value != null) {
                            value.setMateList(getAllSelectedMateList());
                            IssueCreateTaskViewModel.createViewModel().currentViewMode.postValue(value);
                        }
                    }
                    //编辑页面
                    if (from == FROM_EDIT) {
                        JiraItemBean value = IssueEditTaskViewModel.createViewModel().currentViewMode.getValue();
                        if (value != null) {
                            value.setMateList(getAllSelectedMateList());
                            IssueEditTaskViewModel.createViewModel().currentViewMode.postValue(value);
                        }
                    }
                }

                getBaseActivity().onBackPressed();
            }
        });


        viewModel.sourceDevMateViewModel.observe((LifecycleOwner) getBaseActivity(),
                new Observer<List<DevMateBean>>() {
                    @Override
                    public void onChanged(List<DevMateBean> devMateBeans) {
                        DevMateMemberAdapter adapter = new DevMateMemberAdapter();
                        adapter.setData(devMateBeans);
                        mListView.setAdapter(adapter);
                    }
                });

        Bundle arguments = getArguments();
        if (arguments != null) {
            int from = arguments.getInt(Constants.BUNDLE_FILE_KEY);
            viewModel.initAllDevMateViewModel(from);
        }

    }

    //获得所有选择的 研发好友
    private List<DevMateBean> getAllSelectedMateList() {

        final List<DevMateBean> result = new ArrayList<>();
        List<DevMateBean> list = viewModel.sourceDevMateViewModel.getValue();
        if (list != null) {
            for (DevMateBean mateBean : list) {
                if (mateBean == null) continue;
                if (mateBean.isSelect()) {
                    result.add(mateBean);
                }
            }
        }
        return result;

    }
}