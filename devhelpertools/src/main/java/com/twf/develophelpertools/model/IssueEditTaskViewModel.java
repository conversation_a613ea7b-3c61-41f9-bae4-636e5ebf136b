package com.twf.develophelpertools.model;

import android.text.TextUtils;
import android.widget.Toast;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.bean.DevMateBean;
import com.twf.develophelpertools.bean.JiraImageBean;
import com.twf.develophelpertools.bean.JiraItemBean;
import com.twf.develophelpertools.util.IssueSPManager;

import java.util.ArrayList;
import java.util.List;

/**
 * create by guofeng
 * date on 2021/5/10
 */

public class IssueEditTaskViewModel extends ViewModel {


    /**
     * 当前创建的任务数据
     */
    public final MutableLiveData<JiraItemBean> currentViewMode = new MutableLiveData<>();


    /**
     * 删除
     * @param devMateBean
     */
    public void deleteMate(DevMateBean devMateBean){
        JiraItemBean value = currentViewMode.getValue();
        if (value != null) {
            List<DevMateBean> mateList = value.getMateList();
            if (mateList != null) {
                for (DevMateBean mateBean : mateList) {
                    if (mateBean == null) continue;
                    if (devMateBean.isSameMateBean(mateBean)) {
                        mateList.remove(mateBean);
                        Toast.makeText(Kit.getInstance().getApplication(), "删除成功", Toast.LENGTH_SHORT).show();
                        IssueEditTaskViewModel.createViewModel().currentViewMode.postValue(value);
                        break;
                    }
                }
            }
        }
    }
    /**
     * 删除数据
     *
     * @param imageBean
     */
    public void deleteImage(JiraImageBean imageBean) {
        JiraItemBean value = currentViewMode.getValue();
        if (value != null) {
            List<JiraImageBean> imageList = value.getImageList();
            if (imageList != null) {
                for (JiraImageBean jiraImageBean : imageList) {
                    if (jiraImageBean == null) continue;
                    if (jiraImageBean == imageBean) {
                        imageList.remove(jiraImageBean);
                        break;
                    }
                    if (TextUtils.equals(jiraImageBean.getLocalPath(), imageBean.getLocalPath())
                            && TextUtils.equals(jiraImageBean.getUrl(), imageBean.getUrl())
                    ) {
                        imageList.remove(jiraImageBean);
                        break;
                    }
                }
            }
            currentViewMode.postValue(value);
        }
    }

    /**
     * 添加ITEM数据
     *
     * @param imageBean
     */
    public void addImage(JiraImageBean imageBean) {
        JiraItemBean value = currentViewMode.getValue();
        if (value != null) {
            List<JiraImageBean> imageList = value.getImageList();
            if (imageList == null) {
                imageList = new ArrayList<>();
                value.setImageList(imageList);
            }
            imageList.add(0, imageBean);
        }
        currentViewMode.postValue(value);
    }

    /**
     * 刷新数据
     *
     * @param index
     */
    public void postItem(int index) {

        List<JiraItemBean> value = IssueTaskManagerViewModel.createViewModel().taskViewModel.getValue();
        if (value != null) {
            JiraItemBean jiraItemBean = value.get(index);
            currentViewMode.setValue(jiraItemBean);
        }

    }


    /**
     * 重置数据
     */
    public void reset() {
        currentViewMode.setValue(new JiraItemBean());
    }

    /**
     * 保存jira数据
     */
    public void saveAllJiraList() {

        List<JiraItemBean> value = IssueTaskManagerViewModel.createViewModel().taskViewModel.getValue();
        if (value != null) {
            IssueSPManager.getInstance().saveAllIssueList(value);
        }

    }


    private static IssueEditTaskViewModel jiraEditTaskViewModel;

    /**
     * 获得全局数据
     *
     * @return
     */
    public static IssueEditTaskViewModel createViewModel() {
        if (jiraEditTaskViewModel == null) {
            jiraEditTaskViewModel = ViewModelProvider.AndroidViewModelFactory.getInstance(Kit.getInstance().getApplication()).create(IssueEditTaskViewModel.class);
        }
        return jiraEditTaskViewModel;
    }

} 