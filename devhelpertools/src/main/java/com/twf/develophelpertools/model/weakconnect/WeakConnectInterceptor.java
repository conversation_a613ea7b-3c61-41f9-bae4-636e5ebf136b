package com.twf.develophelpertools.model.weakconnect;

import android.os.SystemClock;

import com.twf.develophelpertools.bean.WeakParam;
import com.twf.develophelpertools.model.fragment.WeakConnectFragment;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;

import okhttp3.HttpUrl;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * Created by guofeng
 * on 2019/7/8.
 */

public class WeakConnectInterceptor implements Interceptor {


    @Override
    public Response intercept(Chain chain) throws IOException {
        Response proceed;
        try {
            Request request = chain.request();
            WeakParam params = WeakConnectFragment.getParams();
            if (params != null) {
                int status = params.getStatus();
                switch (status) {
                    case WeakConnectFragment.DISTURB_NET:
                        throw simulateOffNetwork(chain.request().url().host());
                    case WeakConnectFragment.TIME_OUT:
                        final HttpUrl url = chain.request().url();
                        throw simulateTimeOut(url.host(), url.port(), params);
                    case WeakConnectFragment.LIMIT_STREAM:
                        return simulateSpeedLimit(chain, params);
                }
            }
            proceed = chain.proceed(request);

        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
        return proceed;
    }


    /**
     * 限速
     *
     * @param chain
     * @return
     * @throws IOException
     */
    public Response simulateSpeedLimit(Interceptor.Chain chain, WeakParam params) throws IOException {
        Request request = chain.request();
        final RequestBody body = request.body();
        if (body != null) {
            request = request.newBuilder().method(request.method(), new SpeedLimitRequestBody(params.getRequestLimit(), body)).build();
        }
        final Response response = chain.proceed(request);
        return response.newBuilder().body(new SpeedLimitResponseBody(params.getResponseLimit(), response.body())).build();
    }

    /**
     * 模拟超时
     *
     * @param host
     * @param port
     */
    public SocketTimeoutException simulateTimeOut(String host, int port, WeakParam param) {
        SystemClock.sleep(param.getTimeOut());
        return new SocketTimeoutException(String.format("failed to connect to %s (port %d) after %dms", host, port, param.getTimeOut()));
    }

    /**
     * 模拟断网
     */
    public UnknownHostException simulateOffNetwork(String host) {
        return new UnknownHostException(String.format("Unable to resolve host \"%s\": No address associated with hostname", host));
    }


}
