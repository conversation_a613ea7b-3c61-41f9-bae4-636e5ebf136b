package com.twf.develophelpertools.model.fragment;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ListView;
import android.widget.Toast;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.sendbox.FilePreviewAdapter;
import com.twf.develophelpertools.constant.Constants;
import com.twf.develophelpertools.itf.LoadTargetFragmentItf;
import com.twf.develophelpertools.util.KitDialogUtil;
import com.twf.develophelpertools.util.KitFileUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by guofeng
 * on 2019/6/5.
 */

public class FilePreviewFragment extends BaseKitFragment {

    private ListView mListView;
    private File parentFile;
    private List<File> fileList;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_send_box_preview_fragment, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mListView = view.findViewById(R.id.mListView);
        mListView.setOnItemClickListener(onItemClickListener);
        mListView.setOnItemLongClickListener(onItemLongClickListener);
        if (parentFile != null) {
            refreshAdapter(KitFileUtil.listFiles(parentFile));
        } else {
            refreshAdapter(getRootFileList());
        }
    }

    private AdapterView.OnItemClickListener onItemClickListener = new AdapterView.OnItemClickListener() {

        @Override
        public void onItemClick(AdapterView<?> adapterView, View view, int position, long id) {
            File currentFile = (File) adapterView.getItemAtPosition(position);
            if (currentFile != null) {
                parentFile = currentFile.getParentFile();
                if (currentFile.isFile()) {//打开文件
                    openFile(currentFile);
                } else {// 遍历文件夹
                    parentFile = currentFile;
                    List<File> files = KitFileUtil.listFiles(currentFile);
                    refreshAdapter(files);
                }
            }
        }
    };

    @Override
    public boolean onBackPressListener() {
        if (parentFile != null) {
            //关闭界面
            List<File> rootFileList = getRootFileList();
            for (File file : rootFileList) {
                if (parentFile.getAbsolutePath().equals(file.getAbsolutePath())) {
                    refreshAdapter(rootFileList);
                    parentFile = null;
                    return false;
                }
            }

            parentFile = parentFile.getParentFile();
            if (parentFile != null && parentFile.exists()) {
                List<File> files = KitFileUtil.listFiles(parentFile);
                refreshAdapter(files);
                return false;
            }
        }
        return super.onBackPressListener();
    }

    private void openFile(File currentFile) {
        //图片
        if (KitFileUtil.isImageFile(currentFile)) {
            LoadTargetFragmentItf loadTargetFragmentItf = loadTargetFragmentItf();
            if (loadTargetFragmentItf != null) {
                Bundle bundle = new Bundle();
                bundle.putSerializable(Constants.BUNDLE_FILE_KEY, currentFile);
                loadTargetFragmentItf.loadFragment(PreviewImageFragment.class, bundle);
            }
            return;
        }
        //SP文件
        if (KitFileUtil.isSpFile(currentFile)) {
            LoadTargetFragmentItf loadTargetFragmentItf = loadTargetFragmentItf();
            if (loadTargetFragmentItf != null) {
                Bundle bundle = new Bundle();
                bundle.putSerializable(Constants.BUNDLE_FILE_KEY, currentFile);
                loadTargetFragmentItf.loadFragment(SPEditFragment.class, bundle);
            }
            return;
        }
        //DB文件
        if (KitFileUtil.isDbFile(currentFile)) {
            LoadTargetFragmentItf loadTargetFragmentItf = loadTargetFragmentItf();
            if (loadTargetFragmentItf != null) {
                Bundle bundle = new Bundle();
                bundle.putSerializable(Constants.BUNDLE_FILE_KEY, currentFile);
                loadTargetFragmentItf.loadFragment(DataBaseFragment.class, bundle);
            }
            return;
        }
        //视频文件
        if (KitFileUtil.isVideoFile(currentFile)) {
            LoadTargetFragmentItf loadTargetFragmentItf = loadTargetFragmentItf();
            if (loadTargetFragmentItf != null) {
                Bundle bundle = new Bundle();
                bundle.putSerializable(Constants.BUNDLE_FILE_KEY, currentFile);
//                loadTargetFragmentItf.loadFragment(VideoFragment.class, bundle);
            }
        }
        //默认文本文件兜底
        LoadTargetFragmentItf loadTargetFragmentItf = loadTargetFragmentItf();
        if (loadTargetFragmentItf != null) {
            Bundle bundle = new Bundle();
            bundle.putSerializable(Constants.BUNDLE_FILE_KEY, currentFile);
            loadTargetFragmentItf.loadFragment(ShowTextFragment.class, bundle);
        }
    }

    private AdapterView.OnItemLongClickListener onItemLongClickListener = new AdapterView.OnItemLongClickListener() {
        @Override
        public boolean onItemLongClick(AdapterView<?> adapterView, View view, int position, long id) {
            final File currentFile = (File) adapterView.getItemAtPosition(position);
            KitDialogUtil kitDialogUtil = new KitDialogUtil(getBaseActivity());
            kitDialogUtil.setTitle("提示");
            kitDialogUtil.setMessage("确定删除");
            kitDialogUtil.setPositiveListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    File parentFile = currentFile.getParentFile();
                    KitFileUtil.deleteFile(currentFile);
                    if (parentFile != null) {
                        List<File> files = KitFileUtil.listFiles(parentFile);
                        refreshAdapter(files);
                    }
                    Toast.makeText(Kit.getInstance().getApplication(), "删除完毕", Toast.LENGTH_SHORT).show();
                }
            });
            kitDialogUtil.show();
            return true;
        }
    };

    private List<File> getRootFileList() {
        if (fileList == null) {
            fileList = new ArrayList<>();
            fileList.add(getBaseActivity().getFilesDir().getParentFile());
            fileList.add(getBaseActivity().getExternalCacheDir());
            fileList.add(getBaseActivity().getExternalFilesDir(null));
        }
        return fileList;
    }


    private void refreshAdapter(List<File> fileList) {
        FilePreviewAdapter mAdapter = new FilePreviewAdapter(getBaseActivity());
        mListView.setAdapter(mAdapter);
        mAdapter.setData(fileList);
    }
}
