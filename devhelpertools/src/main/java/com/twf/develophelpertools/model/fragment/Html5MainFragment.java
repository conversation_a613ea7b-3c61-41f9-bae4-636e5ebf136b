package com.twf.develophelpertools.model.fragment;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;

import com.kit.baselibrary.AppCallBackItf;
import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.html5.Htm5HistoryAdapter;
import com.twf.develophelpertools.util.KitPermissionUtil;
import com.twf.develophelpertools.util.KitSpUtil;
import com.twf.develophelpertools.zxing.activity.CaptureActivity;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Created by guofeng
 * on 2019/6/24.
 */

public class Html5MainFragment extends BaseKitFragment {

    private static final String URL_KEY = "URL_KEY";
    private ListView mHistory;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_html5_main_fragment, container, false);
    }


    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initView(view);
        refreshAdapter(getListHistory());
    }

    //初始化View
    private void initView(@NonNull View view) {
        final EditText mInput = view.findViewById(R.id.mInput);
        final TextView mJump = view.findViewById(R.id.mJump);
        final TextView mJumpScan = view.findViewById(R.id.mJump_scan);
        mHistory = view.findViewById(R.id.mHistory);
        view.findViewById(R.id.mClear).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Set<String> stringSet = KitSpUtil.getStringSet(URL_KEY);
                if (stringSet != null) {
                    stringSet.clear();
                }
                KitSpUtil.putStringSet(URL_KEY, stringSet);
                refreshAdapter(null);
                Toast.makeText(getBaseActivity(), "清除完毕", Toast.LENGTH_SHORT).show();
            }
        });

        mJump.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //点击按钮事件
                onJumpListener(mInput);
            }
        });

        mJumpScan.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //点击按钮事件
                if (KitPermissionUtil.hasCameraPermission()) {
                    CaptureActivity.intent(getActivity());
                } else {
                    ActivityCompat.requestPermissions(getBaseActivity(), new String[]{Manifest.permission.CAMERA}, 100);
                }

            }
        });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        if (requestCode == 100) {
            CaptureActivity.intent(getActivity());
        }
    }

    // 刷新适配器
    private void refreshAdapter(List<String> urlList) {
        Htm5HistoryAdapter mAdapter = new Htm5HistoryAdapter();
        mHistory.setAdapter(mAdapter);
        mAdapter.setOnItemCallBack(new Htm5HistoryAdapter.OnItemCallBack() {
            @Override
            public void onItemClickListener(String url) {
                jumpToWeb(url);
            }
        });
        mAdapter.setData(urlList);
    }

    //点击按钮事件
    private void onJumpListener(EditText mInput) {
        String url = mInput.getText().toString();
        if (TextUtils.isEmpty(url)) {
            Toast.makeText(getBaseActivity(), "url不能为空", Toast.LENGTH_LONG).show();
            return;
        }
        //保存历史
        saveHistory(url);
        //浏览器打开
        jumpToWeb(url);
    }

    //浏览器
    private void jumpToWeb(String url) {
        AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
        if (appCallBackItf != null) {
            appCallBackItf.openHtmp5UrlListener(url);
        }
    }

    //保存历史
    private void saveHistory(String url) {
        Set<String> setString = KitSpUtil.getStringSet(URL_KEY);
        if (setString == null) {
            setString = new HashSet<>();
        }
        setString.add(url);
        KitSpUtil.putStringSet(URL_KEY, setString);
    }

    //获得历史记录
    private List<String> getListHistory() {
        Set<String> stringSet = KitSpUtil.getStringSet(URL_KEY);
        List<String> result = new ArrayList<>();
        if (stringSet != null) {
            result.addAll(stringSet);
        }
        return result;
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        if (resultCode == Activity.RESULT_OK) {
            String url = data.getStringExtra("result");
            //保存历史
            saveHistory(url);
            //浏览器打开
            jumpToWeb(url);
        }
    }
}
