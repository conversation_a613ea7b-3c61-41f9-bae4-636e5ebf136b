package com.twf.develophelpertools.model.fragment;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ListView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.MockAdapter;
import com.twf.develophelpertools.model.mock.DevelopMockUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by guofeng
 * on 2019/7/18.
 * 开发的MOCK类
 */

public class DevelopMockFragment extends BaseKitFragment {


    private ListView mListView;

    private EditText mEditText;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_mock_fragment, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initView(view);
        addTextWatcher();
        refreshAdapter(getAllUrlList());
    }


    private void initView(@NonNull View view) {
        mEditText = view.findViewById(R.id.mEditText);
        mListView = view.findViewById(R.id.mListView);
        view.findViewById(R.id.mSelectAll).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DevelopMockUtil.getInstance().onAllChangeListener(true);
                refreshAdapter(getAllUrlList());
            }
        });
        view.findViewById(R.id.mCacnelAll).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DevelopMockUtil.getInstance().onAllChangeListener(false);
                refreshAdapter(getAllUrlList());
            }
        });
    }

    private void addTextWatcher() {
        mEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {


            }

            @Override
            public void afterTextChanged(Editable s) {
                final List<String> allUrls = getAllUrlList();
                final List<String> result = new ArrayList<>();
                for (String url : allUrls) {
                    if (url == null) continue;
                    if (url.contains(s.toString())) {
                        result.add(url);
                    }
                }
                refreshAdapter(result);
            }
        });
    }

    private MockAdapter adapter;

    private void refreshAdapter(List<String> list) {
        if (adapter == null) {
            adapter = new MockAdapter(getBaseActivity());
            mListView.setAdapter(adapter);
        }
        adapter.setData(list);
    }


    //业务层所有的url集合
    private final List<String> allUrlList = new ArrayList<>();

    //反射获得业务层所有的url地址
    private List<String> getAllUrlList() {
        if (allUrlList.isEmpty()) {
            allUrlList.addAll(DevelopMockUtil.getInstance().getAllUlrList());
        }
        return allUrlList;
    }


}
