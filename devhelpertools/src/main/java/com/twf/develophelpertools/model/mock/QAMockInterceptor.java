package com.twf.develophelpertools.model.mock;

import android.text.TextUtils;

import com.kit.baselibrary.AppCallBackItf;
import com.twf.develophelpertools.Kit;

import java.io.EOFException;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.UnsupportedCharsetException;
import java.util.List;
import java.util.Set;

import okhttp3.Headers;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.internal.http.HttpHeaders;
import okio.Buffer;
import okio.BufferedSource;
import okio.GzipSource;
import okio.Okio;


public class QAMockInterceptor implements Interceptor {

    private long maxContentLength = Long.MAX_VALUE;

    private static final Charset UTF8 = Charset.forName("UTF-8");



    @Override
    public Response intercept(Chain chain) throws IOException {

        Response response;

        try {
            Request request = chain.request();
            String urlPath = request.url().url().getPath();


            try {
                response = chain.proceed(request);
            } catch (Exception e) {
                e.printStackTrace();
                throw e;
            }

            AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
            if (appCallBackItf != null && !appCallBackItf.isInterceptRequest(request.headers(), response.body(), request.url().toString())) {
                if (isSelectUrl(urlPath)) {
                    String value = QAMockUtil.getMockValue(urlPath);
                    if (!TextUtils.isEmpty(value)) {
                        ResponseBody responseBody = response.body();
                        responseBody = responseBody.create(responseBody.contentType(), value);
                        response = response.newBuilder().body(responseBody).build();
                    }
                } else {
                    String value = getResponseString(response);
                    QAMockUtil.putMockValue(getMockUrl(urlPath), value);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
        return response;
    }

    private String getMockUrl(String urlPath) {
        List<String> urlList = DevelopMockUtil.getInstance().getAllUlrList();
        for (String url : urlList) {
            if (urlPath.endsWith(url)) {
                return url;
            }
        }
        return "";
    }


    //是否选中MOCK数据
    private boolean isSelectUrl(String urlPath) {
        Set<String> collection = QAMockUtil.getAllSelectCollection();
        for (String url : collection) {
            if (urlPath.endsWith(url)) {
                return true;
            }
        }
        return false;
    }

    private String getResponseString(Response response) throws IOException {
        String responseString = null;
        ResponseBody responseBody = response.body();
        if (HttpHeaders.hasBody(response)) {
            BufferedSource source = getNativeSource(response);
            source.request(Long.MAX_VALUE);
            Buffer buffer = source.buffer();
            Charset charset = UTF8;
            MediaType contentType = responseBody.contentType();
            if (contentType != null) {
                try {
                    charset = contentType.charset(UTF8);
                } catch (UnsupportedCharsetException e) {
                    return "";
                }
            }
            if (isPlaintext(buffer)) {
                responseString = readFromBuffer(buffer.clone(), charset);
            }
        }
        return responseString;
    }


    private String readFromBuffer(Buffer buffer, Charset charset) {
        long bufferSize = buffer.size();
        long maxBytes = Math.min(bufferSize, maxContentLength);
        String body = "";
        try {
            body = buffer.readString(maxBytes, charset);
        } catch (EOFException e) {
        }
        return body;
    }

    private BufferedSource getNativeSource(Response response) throws IOException {
        if (bodyGzipped(response.headers())) {
            BufferedSource source = response.peekBody(maxContentLength).source();
            if (source.buffer().size() < maxContentLength) {
                return getNativeSource(source, true);
            } else {
            }
        }
        return response.body().source();
    }


    private boolean bodyGzipped(Headers headers) {
        String contentEncoding = headers.get("Content-Encoding");
        return "gzip".equalsIgnoreCase(contentEncoding);
    }

    private BufferedSource getNativeSource(BufferedSource input, boolean isGzipped) {
        if (isGzipped) {
            GzipSource source = new GzipSource(input);
            return Okio.buffer(source);
        } else {
            return input;
        }
    }

    private boolean isPlaintext(Buffer buffer) {
        try {
            Buffer prefix = new Buffer();
            long byteCount = buffer.size() < 64 ? buffer.size() : 64;
            buffer.copyTo(prefix, 0, byteCount);
            for (int i = 0; i < 16; i++) {
                if (prefix.exhausted()) {
                    break;
                }
                int codePoint = prefix.readUtf8CodePoint();
                if (Character.isISOControl(codePoint) && !Character.isWhitespace(codePoint)) {
                    return false;
                }
            }
            return true;
        } catch (EOFException e) {
            return false; // Truncated UTF-8 sequence.
        }
    }

}
