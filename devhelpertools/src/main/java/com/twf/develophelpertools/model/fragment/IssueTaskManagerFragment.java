package com.twf.develophelpertools.model.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ListView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.IssueManagerAdapter;
import com.twf.develophelpertools.bean.JiraItemBean;
import com.twf.develophelpertools.constant.Constants;
import com.twf.develophelpertools.model.IssueTaskManagerViewModel;
import com.twf.develophelpertools.util.FloatIconUtil;
import com.twf.develophelpertools.util.KitSpUtil;

import java.util.List;

/**
 * create by guofeng
 * date on 2021/5/8
 */

public class IssueTaskManagerFragment extends BaseKitFragment {


    public static final String SP_IS_OPEN_TUYA = "SP_IS_OPEN_TUYA";

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {

        return inflater.inflate(R.layout.kit_jira_task_manager_fragment, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        final ListView mRecycleView = view.findViewById(R.id.mRecycleView);
        TextView mCreateRecord = view.findViewById(R.id.mCreateRecord);
        mCreateRecord.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                loadTargetFragmentItf().loadFragment(IssueCreateTaskFragment.class, null);
            }
        });


        IssueTaskManagerViewModel.createViewModel().taskViewModel.observe((LifecycleOwner) getBaseActivity(), new Observer<List<JiraItemBean>>() {
            @Override
            public void onChanged(List<JiraItemBean> jiraItemBeans) {
                IssueManagerAdapter managerAdapter = new IssueManagerAdapter();
                managerAdapter.setOnEditCallBack(new IssueManagerAdapter.OnEditCallBack() {
                    @Override
                    public void onEditListener(int index) {
                        Bundle args = new Bundle();
                        args.putInt(Constants.BUNDLE_FILE_KEY, index);
                        loadTargetFragmentItf().loadFragment(IssueEditTaskFragment.class, args);
                    }
                });
                managerAdapter.setData(jiraItemBeans);
                mRecycleView.setAdapter(managerAdapter);
            }
        });
        //初始化数据
        IssueTaskManagerViewModel.createViewModel().initSourceData();

        CheckBox mCheckBox = view.findViewById(R.id.mCheckBox);
        mCheckBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                KitSpUtil.putBoolean(SP_IS_OPEN_TUYA, isChecked);
//                FloatIconUtil.getInstance().checkScrawlIcon();
            }
        });
        mCheckBox.setChecked(KitSpUtil.getBoolean(SP_IS_OPEN_TUYA, false));
    }


}