package com.twf.develophelpertools.model;

import android.content.Context;
import android.opengl.GLSurfaceView;

import javax.microedition.khronos.egl.EGLConfig;
import javax.microedition.khronos.opengles.GL10;

public class DemoGLSurfaceView extends GLSurfaceView {


    private OnSurfaceCallBack callBack;

    public void setCallBack(OnSurfaceCallBack callBack) {
        this.callBack = callBack;
    }

    public DemoGLSurfaceView(Context context) {
        super(context);
        setEGLConfigChooser(8, 8, 8, 8, 0, 0);
        setRenderer(new DemoRenderer(callBack));
    }

    public interface OnSurfaceCallBack {
        void onSurfaceListener(GL10 gl, EGLConfig config);
    }

}

class DemoRenderer implements GLSurfaceView.Renderer {

    private DemoGLSurfaceView.OnSurfaceCallBack callBack;

    public DemoRenderer(DemoGLSurfaceView.OnSurfaceCallBack callBack) {
        this.callBack = callBack;
    }

    @Override
    public void onSurfaceCreated(GL10 gl, EGLConfig config) {

        if (callBack != null) {
            callBack.onSurfaceListener(gl,config);
        }

    }

    @Override
    public void onSurfaceChanged(GL10 gl, int width, int height) {

    }

    @Override
    public void onDrawFrame(GL10 gl) {

    }
}

