package com.twf.develophelpertools.model.fragment;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.TextView;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.bean.WeakParam;
import com.twf.develophelpertools.util.KitScaleUtil;
import com.twf.develophelpertools.util.KitSpUtil;

/**
 * Created by guofeng
 * on 2019/6/26.
 */

public class WeakConnectFragment extends BaseKitFragment {

    private static final String MONITOR_WEAK_NEW = "MONITOR_WEAK_NEW";
    private static final String TIMEOUT = "TIMEOUT";
    private static final String REQUEST_LIMIT = "REQUEST_LIMIT";
    private static final String RESPONSE_LIMIT = "RESPONSE_LIMIT";
    /**
     * @see #DISTURB_NET
     * @see #TIME_OUT
     * @see #LIMIT_STREAM
     * @see #NONE
     */
    private int status = NONE;
    public static final int NONE = -1;
    public static final int DISTURB_NET = 0;
    public static final int TIME_OUT = 1;
    public static final int LIMIT_STREAM = 2;

    private static final String STATUS_TYPE = "statusType";
    private CheckBox mCheckView;
    private LinearLayout mContainer;
    private LinearLayout mTabView;


    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_weak_connect_fragment, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mCheckView = view.findViewById(R.id.mCheckView);
        RadioButton mInterceptNet = view.findViewById(R.id.mInterceptNet);
        RadioButton mTimeOut = view.findViewById(R.id.mTimeOut);
        RadioButton mLimitSpeed = view.findViewById(R.id.mLimitSpeed);
        mContainer = view.findViewById(R.id.mContainer);
        mTabView = view.findViewById(R.id.mTabView);
        mCheckView.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    mTabView.setVisibility(View.VISIBLE);
                    mContainer.setVisibility(View.VISIBLE);
                } else {
                    status = NONE;
                    unSelectOther(null, true);
                    mTabView.setVisibility(View.GONE);
                    mContainer.setVisibility(View.GONE);
                }
                KitSpUtil.putBoolean(MONITOR_WEAK_NEW, isChecked);
            }
        });
        mInterceptNet.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                unSelectOther(buttonView, isChecked);
                if (isChecked) {
                    status = DISTURB_NET;
                }
                refreshPanel();
            }
        });
        mTimeOut.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                unSelectOther(buttonView, isChecked);
                if (isChecked) {
                    status = TIME_OUT;
                }
                refreshPanel();
            }
        });
        mLimitSpeed.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                unSelectOther(buttonView, isChecked);
                if (isChecked) {
                    status = LIMIT_STREAM;
                }
                refreshPanel();
            }
        });

        mCheckView.setChecked(KitSpUtil.getBoolean(MONITOR_WEAK_NEW, false));
        int statusType = KitSpUtil.getInt(STATUS_TYPE, NONE);
        switch (statusType) {
            case DISTURB_NET:
                mInterceptNet.setChecked(true);
                break;
            case TIME_OUT:
                mTimeOut.setChecked(true);
                break;
            case LIMIT_STREAM:
                mLimitSpeed.setChecked(true);
                break;
        }
        refreshPanel();
    }

    private void refreshPanel() {
        mContainer.removeAllViews();
        KitSpUtil.putInt(STATUS_TYPE, status);
        switch (status) {
            case DISTURB_NET:
                mContainer.addView(getDisturb());
                break;
            case TIME_OUT:
                mContainer.addView(getTimeOut());
                break;
            case LIMIT_STREAM:
                mContainer.addView(getLimitStream());
                break;
        }
    }

    private View getDisturb() {
        TextView textView = new TextView(getBaseActivity());
        textView.setText("当前处于断网状态");
        textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 15);
        textView.setPadding(0, KitScaleUtil.dip2px(10), 0, 0);
        return textView;
    }

    private View getTimeOut() {
        View view = LayoutInflater.from(getBaseActivity()).inflate(R.layout.kit_weak_connect_time_out, null);
        EditText mTimeOut = view.findViewById(R.id.mTimeOut);
        mTimeOut.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String value = s.toString();
                if (TextUtils.isEmpty(value)) return;
                KitSpUtil.putInt(TIMEOUT, Integer.parseInt(value));
            }
        });
        int timeOut = KitSpUtil.getInt(TIMEOUT, 0);
        if (timeOut > 0) {
            mTimeOut.setText(timeOut + "");
        }
        return view;
    }

    private View getLimitStream() {
        View view = LayoutInflater.from(getBaseActivity()).inflate(R.layout.kit_weak_connect_limit_stream, null);
        EditText mRequestLimit = view.findViewById(R.id.mRequestLimit);
        EditText mResponseLimit = view.findViewById(R.id.mResponseLimit);
        mRequestLimit.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String value = s.toString();
                if (TextUtils.isEmpty(value)) return;
                KitSpUtil.putInt(REQUEST_LIMIT, Integer.parseInt(value));
            }
        });
        mResponseLimit.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                KitSpUtil.putInt(RESPONSE_LIMIT, Integer.parseInt(s.toString()));
            }
        });
        int requestLimit = KitSpUtil.getInt(REQUEST_LIMIT, 0);
        if (requestLimit > 0) {
            mRequestLimit.setText(requestLimit + "");
        }

        int responseLimit = KitSpUtil.getInt(RESPONSE_LIMIT, 0);
        if (responseLimit > 0) {
            mResponseLimit.setText(responseLimit + "");
        }
        return view;
    }

    //反其它
    private void unSelectOther(CompoundButton buttonView, boolean isChecked) {
        if (isChecked) {
            int childCount = mTabView.getChildCount();
            for (int i = 0; i < childCount; i++) {
                View child = mTabView.getChildAt(i);
                if (child == buttonView) continue;
                if (child instanceof RadioButton) {
                    RadioButton radioButton = (RadioButton) child;
                    radioButton.setChecked(false);
                }
            }
        }
    }

    public static WeakParam getParams() {
        boolean isSwitchOpen = KitSpUtil.getBoolean(MONITOR_WEAK_NEW, false);
        if (isSwitchOpen) {
            WeakParam weakParam = new WeakParam();
            weakParam.setStatus(KitSpUtil.getInt(STATUS_TYPE, NONE));
            switch (weakParam.getStatus()) {
                case TIME_OUT:
                    weakParam.setTimeOut(KitSpUtil.getInt(TIMEOUT, 0));
                    break;
                case LIMIT_STREAM:
                    weakParam.setRequestLimit(KitSpUtil.getInt(REQUEST_LIMIT, 0));
                    weakParam.setResponseLimit(KitSpUtil.getInt(RESPONSE_LIMIT, 0));
                    break;
            }
            return weakParam;
        }
        return null;
    }

}
