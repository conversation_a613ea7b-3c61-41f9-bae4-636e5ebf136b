package com.twf.develophelpertools.model.fragment;

import android.app.Activity;
import android.content.Context;
import androidx.fragment.app.Fragment;

import com.twf.develophelpertools.itf.LoadTargetFragmentItf;

/**
 * Created by guofeng
 * on 2019/5/29.
 */

public class BaseKitFragment extends Fragment {

    private Activity mActivity;

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        if (context instanceof Activity) {
            mActivity = (Activity) context;
        }
    }

    public Activity getBaseActivity() {
        return mActivity;
    }


    public LoadTargetFragmentItf loadTargetFragmentItf() {
        if (getBaseActivity() instanceof LoadTargetFragmentItf) {
            return (LoadTargetFragmentItf)mActivity;
        }
        return null;
    }

    //点击返回键 true表示关闭 false表示不关闭
    public boolean onBackPressListener() {
        return true;
    }

}
