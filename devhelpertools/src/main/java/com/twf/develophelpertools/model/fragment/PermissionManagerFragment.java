package com.twf.develophelpertools.model.fragment;

import android.content.pm.PackageManager;
import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ListView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.PermissionAdapter;
import com.twf.develophelpertools.bean.PermissionBean;
import com.twf.develophelpertools.util.NotifyUtil;

import java.util.ArrayList;
import java.util.List;


/**
 * create by guofeng
 * date on 2021/7/31
 */
public class PermissionManagerFragment extends BaseKitFragment {

    public static final int REQUEST_CODE = 100;
    private ListView mPermissionList;
    private TextView mImportantPermission;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_permisssion_manager, container, false);
    }

    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mPermissionList = view.findViewById(R.id.mPermissionList);
        mImportantPermission = view.findViewById(R.id.mImportantPermission);
        mPermissionList.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                PermissionBean permissionBean = adapter.getSourceData().get(position);
                if (permissionBean != null) {
                    applyPermission(permissionBean.permissionName);
                }
            }
        });

        boolean notificationEnabled = NotifyUtil.isNotificationEnabled(getBaseActivity());
        if (notificationEnabled) {
            mImportantPermission.setText("通知栏权限:已开启");
            mImportantPermission.setTextColor(Color.parseColor("#000000"));
        } else {
            mImportantPermission.setText("通知栏权限:未开启   《点击开启》");
            mImportantPermission.setTextColor(Color.parseColor("#ff0000"));
            mImportantPermission.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    NotifyUtil.openSettingNotifyActivity(getBaseActivity());
                }
            });
        }
        refreshAdapter(getPermissionList());
    }

    private PermissionAdapter adapter;

    private void refreshAdapter(List<PermissionBean> permissionBeans) {
        if (adapter == null) {
            adapter = new PermissionAdapter(getBaseActivity());
            mPermissionList.setAdapter(adapter);
        }
        adapter.setData(permissionBeans);
    }

    /**
     * 申请权限
     *
     * @param permissionName
     */
    private void applyPermission(String permissionName) {
        requestPermissions(new String[]{permissionName}, REQUEST_CODE);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_CODE) {
            //刷新列表
            refreshAdapter(getPermissionList());
        }
    }

    /**
     * 获得APP的权限列表
     *
     * @return
     */
    private String[] getAppRegisterPermission() {
        try {
            PackageManager packageManager = getBaseActivity().getApplication().getPackageManager();
            return packageManager.getPackageInfo(getBaseActivity().getPackageName(), PackageManager.GET_PERMISSIONS).requestedPermissions;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 获得权限列表
     *
     * @return
     */
    private List<PermissionBean> getPermissionList() {
        List<PermissionBean> result = new ArrayList<>();
        String[] appRegisterPermission = getAppRegisterPermission();
        if (appRegisterPermission != null) {
            for (String permission : appRegisterPermission) {
                if (TextUtils.isEmpty(permission)) continue;
                String content = getPermissionContent(permission);
                if (!TextUtils.isEmpty(content)) {
                    boolean hasPermission = checkHasPermission(permission);
                    PermissionBean permissionBean = new PermissionBean();
                    permissionBean.hasPermission = hasPermission;
                    permissionBean.permissionContent = content;
                    permissionBean.permissionName = permission;
                    result.add(permissionBean);
                }
            }
        }
        return result;
    }

    /**
     * 判断该权限是否允许||拒绝
     *
     * @param permission
     * @return
     */
    private boolean checkHasPermission(String permission) {
        int status = ContextCompat.checkSelfPermission(getBaseActivity(), permission);
        return status == PackageManager.PERMISSION_GRANTED;
    }

    /**
     * 获得权限的含义，作用
     *
     * @param permission
     * @return
     */
    private String getPermissionContent(String permission) {

        if (TextUtils.equals("android.permission.WRITE_CONTACTS", permission)) {
            return "写入联系人，但不可读取";
        }
        if (TextUtils.equals("android.permission.GET_ACCOUNTS", permission)) {
            return "访问GMail账户列表";
        }
        if (TextUtils.equals("android.permission.READ_CONTACTS", permission)) {
            return "允许应用访问联系人通讯录信息";
        }
        if (TextUtils.equals("android.permission.READ_CALL_LOG", permission)) {
            return "";
        }
        if (TextUtils.equals("android.permission.READ_PHONE_STATE", permission)) {
            return "访问电话状态";
        }

        if (TextUtils.equals("android.permission.CALL_PHONE", permission)) {
            return "允许程序从非系统拨号器里输入电话号码";
        }

        if (TextUtils.equals("android.permission.USE_SIP", permission)) {
            return "允许程序使用SIP视频服务";
        }
        if (TextUtils.equals("android.permission.PROCESS_OUTGOING_CALLS", permission)) {
            return "允许程序监视，修改或放弃播出电话";
        }
        if (TextUtils.equals("android.permission.READ_CALENDAR", permission)) {
            return "允许程序读取用户的日程信息";
        }
        if (TextUtils.equals("android.permission.WRITE_CALENDAR", permission)) {
            return "写入日程，但不可读取";
        }
        if (TextUtils.equals("android.permission.CAMERA", permission)) {
            return "允许访问摄像头进行拍照";
        }
        if (TextUtils.equals("android.permission.BODY_SENSORS", permission)) {
            return "传感器权限";
        }
        if (TextUtils.equals("android.permission.ACCESS_FINE_LOCATION", permission)) {
            return "通过GPS芯片接收卫星的定位信息，定位精度达10米以内";
        }
        if (TextUtils.equals("android.permission.ACCESS_COARSE_LOCATION", permission)) {
            return "通过WiFi或移动基站的方式获取用户错略的经纬度信息，定位精度大概误差在30~1500米";
        }
        if (TextUtils.equals("android.permission.READ_EXTERNAL_STORAGE", permission)) {
            return "存储权限";
        }
        if (TextUtils.equals("android.permission.RECORD_AUDIO", permission)) {
            return "录制声音通过手机或耳机的麦克";
        }
        if (TextUtils.equals("android.permission.READ_SMS", permission)) {
            return "读取短信内容";
        }
        if (TextUtils.equals("android.permission.RECEIVE_WAP_PUSH", permission)) {
            return "接收WAP PUSH信息";
        }
        if (TextUtils.equals("android.permission.RECEIVE_MMS", permission)) {
            return "接收彩信";
        }
        if (TextUtils.equals("android.permission.RECEIVE_SMS", permission)) {
            return "接收短信";
        }
        if (TextUtils.equals("android.permission.SEND_SMS", permission)) {
            return "发送短信";
        }

        if (TextUtils.equals("android.permission.READ_CELL_BROADCASTS", permission)) {
            return "短信权限";
        }

        if (TextUtils.equals("android.permission.WRITE_CONTACTS", permission)) {
            return "，写入联系人，但不可读取";
        }

        return "";
    }
}