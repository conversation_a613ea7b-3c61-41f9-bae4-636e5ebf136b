package com.twf.develophelpertools.model.fragment;

import com.twf.develophelpertools.model.pickcolor.FloatPageManager;
import com.twf.develophelpertools.model.pickcolor.PageIntent;
import com.twf.develophelpertools.model.pickcolor.PageTag;
import com.twf.develophelpertools.ruler.AlignRulerLineFloatPage;
import com.twf.develophelpertools.ruler.AlignRulerMarkerFloatPage;

/**
 * Created by guofeng
 * on 2019/6/26.
 */

public class RulerFragment {


    public static void initRuler() {
        PageIntent markIntent = new PageIntent(AlignRulerMarkerFloatPage.class);
        markIntent.tag = PageTag.PAGE_ALIGN_RULER_MARKER;
        FloatPageManager.getInstance().add(markIntent);
        PageIntent lineIntent = new PageIntent(AlignRulerLineFloatPage.class);
        lineIntent.tag = PageTag.PAGE_ALIGN_RULER_LINE;
        FloatPageManager.getInstance().add(lineIntent);
    }

    public static void removeRuler() {
        FloatPageManager.getInstance().remove(PageTag.PAGE_ALIGN_RULER_MARKER);
        FloatPageManager.getInstance().remove(PageTag.PAGE_ALIGN_RULER_LINE);
    }
}
