package com.twf.develophelpertools.model.fragment;

import android.graphics.Rect;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.kit.baselibrary.AppCallBackItf;
import com.kit.baselibrary.SwitchConfigBean;
import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.toppageinfo.TopPageInfoFinder;
import com.twf.develophelpertools.util.KitScaleUtil;
import com.twf.develophelpertools.util.KitSpUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * create by guofeng
 * date on 2021/8/4
 */

public class SwitchConfigFragment extends BaseKitFragment {


    public static final String SP_REPORT_API = "SP_REPORT_API";
    public static final String SP_JUMP_ADVERTISEMENT = "SP_JUMP_ADVERTISEMENT";
    public static final String SP_ACTIVITY_INFO = "SP_ACTIVITY_INFO";
    public static final String SP_API_ERROR_TOAST = "SP_API_ERROR_TOAST";
    public static final String SP_H_5_PAGE_WATER_MARK = "SP_H5_PAGE_WATER_MARK";
    public static final String SP_LONG_TERM_SIMULATE = "SP_LONG_TERM_SIMULATE";// 长文本模拟

    private RecyclerView mRecycleView;

    public static SwitchConfigFragment newInstance() {
        return new SwitchConfigFragment();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_common_tools_fragment, container, false);
    }


    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mRecycleView = view.findViewById(R.id.mRecycleView);
        mRecycleView.setLayoutManager(new GridLayoutManager(getBaseActivity(), 3));
        mRecycleView.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, int itemPosition, @NonNull RecyclerView parent) {
                super.getItemOffsets(outRect, itemPosition, parent);
                outRect.set(0, KitScaleUtil.dip2px(30), 0, 0);
            }
        });


        SwitchConfigAdapter adapter = new SwitchConfigAdapter();
        adapter.setData(getData());
        mRecycleView.setAdapter(adapter);
    }

    private List<SwitchConfigBean> getData() {
        List<SwitchConfigBean> value = new ArrayList<>();
        value.addAll(result);

        AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
        if (appCallBackItf != null) {
            List<SwitchConfigBean> switchConfigList = appCallBackItf.getSwitchConfigList();
            if (switchConfigList != null) {
                value.addAll(switchConfigList);
            }
        }
        return value;
    }



    private final List<SwitchConfigBean> result = new ArrayList<SwitchConfigBean>() {
        {
            add(new SwitchConfigBean(SP_REPORT_API, isOpenApiReport(), "接口上报", null));
            add(new SwitchConfigBean(SP_JUMP_ADVERTISEMENT, isOpenJumpAdvertisement(), "跳过广告", null));

            add(new SwitchConfigBean(SP_API_ERROR_TOAST, isOpenAPiErrorToast(), "异常提示", null));
            add(new SwitchConfigBean(SP_H_5_PAGE_WATER_MARK, isOpenH5WaterMark(), "H5提示", null));


            add(new SwitchConfigBean(SP_ACTIVITY_INFO, isOpenActivityInfo(), "页面信息", new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    if (isChecked) {
                        TopPageInfoFinder.getInstance().switchFloat(getBaseActivity());
                    } else {
                        TopPageInfoFinder.getInstance().switchFloat(getBaseActivity());
                    }
                }
            }));

            add(new SwitchConfigBean(SP_LONG_TERM_SIMULATE, isOpenLongTermSimulate(), "模拟长文本", new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    KitSpUtil.putBoolean(SP_LONG_TERM_SIMULATE, isChecked);
                }
            }));
        }
    };


    public static boolean isOpenApiReport() {
        return KitSpUtil.getBoolean(SP_REPORT_API, true);
    }

    public static boolean isOpenJumpAdvertisement() {
        return KitSpUtil.getBoolean(SP_JUMP_ADVERTISEMENT, false);
    }

    public static boolean isOpenAPiErrorToast() {
        return KitSpUtil.getBoolean(SP_API_ERROR_TOAST, false);
    }

    public static boolean isOpenH5WaterMark() {
        return KitSpUtil.getBoolean(SP_H_5_PAGE_WATER_MARK, false);
    }

    public static boolean isOpenActivityInfo() {
        return KitSpUtil.getBoolean(SP_ACTIVITY_INFO, false);
    }

    public static boolean isOpenLongTermSimulate() {
        return KitSpUtil.getBoolean(SP_LONG_TERM_SIMULATE, false);
    }





    private class SwitchConfigAdapter extends RecyclerView.Adapter<SwitchConfigAdapter.SwitchConfigViewHolder> {

        private final List<SwitchConfigBean> data = new ArrayList<>();


        private void setData(List<SwitchConfigBean> source) {
            data.clear();
            if (source != null) {
                data.addAll(source);
            }
            notifyDataSetChanged();
        }


        @Override
        public SwitchConfigViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            return new SwitchConfigViewHolder(LayoutInflater.from(getContext()).inflate(R.layout.kit_switch_config_item, null));
        }

        @Override
        public void onBindViewHolder(SwitchConfigViewHolder holder, int position) {
            final SwitchConfigBean commonToolsBean = data.get(position);
            if (commonToolsBean != null) {

                boolean isSelect = KitSpUtil.getBoolean(commonToolsBean.getSwitchKey(), commonToolsBean.isDefaultIsSelect());

                holder.mCheckBox.setText(commonToolsBean.getSwitchName());
                holder.mCheckBox.setChecked(isSelect);
                final CompoundButton.OnCheckedChangeListener onChangeListener = commonToolsBean.getOnChangeListener();
                holder.mCheckBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                    @Override
                    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                        if (onChangeListener != null) {
                            onChangeListener.onCheckedChanged(buttonView, isChecked);
                        }
                        //SP报错数据
                        if (!TextUtils.isEmpty(commonToolsBean.getSwitchKey())) {
                            KitSpUtil.putBoolean(commonToolsBean.getSwitchKey(), isChecked);
                        }
                    }
                });
            }
        }

        @Override
        public int getItemCount() {
            return data.size();
        }

        public class SwitchConfigViewHolder extends RecyclerView.ViewHolder {


            private CheckBox mCheckBox;

            public SwitchConfigViewHolder(View itemView) {
                super(itemView);

                mCheckBox = itemView.findViewById(R.id.mCheckBox);

            }
        }

    }

}