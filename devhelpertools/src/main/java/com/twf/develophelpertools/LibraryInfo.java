package com.twf.develophelpertools;

/**
 * create by guofeng
 * date on 2021/10/20
 */

public class LibraryInfo {

   public  static String libraryInfo =" org.jetbrains.kotlinx:kotlinx-coroutines-test:1.8.0###androidx.compose.foundation:foundation:null###org.jetbrains.kotlin:kotlin-build-tools-impl:null###androidx.work:work-testing:2.9.0###org.jetbrains.kotlinx:kotlinx-coroutines-core:1.8.0###com.google.dagger:hilt-compiler:2.51.1###com.amap.api:search:7.1.0###androidx.databinding:databinding-ktx:8.4.0###com.amap.api:3dmap:7.9.1###org.jetbrains.kotlin:kotlin-stdlib:2.0.0###androidx.constraintlayout:constraintlayout-compose:1.0.1###androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0###com.squareup.okhttp3:logging-interceptor:4.12.0###androidx.arch.core:core-testing:2.2.0###com.google.android.apps.common.testing.accessibility.framework:accessibility-test-framework:4.1.1###androidx.datastore:datastore-preferences:1.1.1###com.facebook.fresco:fresco:1.13.0###androidx.appcompat:appcompat:1.7.0###com.google.testing.platform:core:0.0.9-alpha02###androidx.databinding:databinding-common:8.4.0###com.google.devtools.ksp:symbol-processing-api:2.0.0-1.0.21###com.google.android.material:material:1.13.0-alpha01###androidx.compose:compose-bom:2024.05.00###com.android.tools.utp:android-test-plugin-host-apk-installer:31.4.0###com.google.android.material:material:1.0.0###androidx.compose.runtime:runtime:null###androidx.room:room-compiler:2.6.1###androidx.navigation:navigation-compose:2.7.7###com.android.tools.utp:android-test-plugin-host-emulator-control:31.4.0###com.android.tools.utp:android-test-plugin-host-logcat:31.4.0###androidx.compose.ui:ui-tooling:null###androidx.compose.compiler:compiler:1.3.2###androidx.compose.ui:ui-test-junit4:null###com.android.tools.utp:android-test-plugin-result-listener-gradle:31.4.0###com.google.dagger:hilt-android-testing:2.51.1###androidx.databinding:viewbinding:8.4.0###androidx.room:room-ktx:2.6.1###androidx.compose.foundation:foundation-layout:null###androidx.lifecycle:lifecycle-runtime-compose:2.7.0###sunflower:lib_collection:unspecified###io.github.cymchad:BaseRecyclerViewAdapterHelper:3.0.14###com.google.testing.platform:android-test-plugin:0.0.9-alpha02###com.android.tools.utp:android-device-provider-ddmlib:31.4.0###com.google.testing.platform:android-driver-instrumentation:0.0.9-alpha02###com.google.guava:guava:33.1.0-jre###com.android.tools.utp:android-device-provider-gradle:31.4.0###sunflower:app:unspecified###androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0###androidx.databinding:databinding-compiler:8.4.0###androidx.paging:paging-compose:3.3.0-rc01###com.squareup.retrofit2:retrofit:2.11.0###sunflower:lib_base:unspecified###com.google.devtools.ksp:symbol-processing-cmdline:2.0.0-1.0.21###com.google.testing.platform:launcher:0.0.9-alpha02###com.github.bumptech.glide:compose:1.0.0-beta01###androidx.test.espresso:espresso-contrib:3.5.1###sunflower:BaseLibrary:unspecified###androidx.compose.ui:ui-tooling-preview:null###nl.qbusict:cupboard:2.2.0###com.android.tools.utp:android-test-plugin-host-additional-test-output:31.4.0###com.squareup.okhttp3:okhttp:3.11.0###androidx.benchmark:benchmark-macro-junit4:1.2.4###androidx.work:work-runtime-ktx:2.9.0###com.google.devtools.ksp:symbol-processing:2.0.0-1.0.21###androidx.test.espresso:espresso-core:3.5.1###androidx.test.espresso:espresso-intents:3.5.1###androidx.compose.runtime:runtime-livedata:null###androidx.datastore:datastore-core:1.1.1###androidx.compose.material3:material3:1.2.1###com.google.dagger:hilt-android:2.51.1###com.google.dagger:hilt-android-compiler:2.51.1###androidx.test.ext:junit:1.1.5###androidx.hilt:hilt-navigation-compose:1.2.0###androidx.profileinstaller:profileinstaller:1.3.1###androidx.appcompat:appcompat:1.1.0###com.squareup.retrofit2:converter-gson:2.11.0###androidx.compose.ui:ui:null###com.github.li-xiaojun:XPopup:2.10.0###org.jetbrains.kotlin:kotlin-compose-compiler-plugin-embeddable:2.0.0###org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.0###androidx.core:core-ktx:1.13.1###com.android.tools.utp:android-test-plugin-host-device-info:31.4.0###androidx.databinding:databinding-adapters:8.4.0###androidx.compose.ui:ui-viewbinding:null###com.google.code.gson:gson:2.7###com.android.tools.utp:android-test-plugin-host-retention:31.4.0###androidx.databinding:databinding-runtime:8.4.0###junit:junit:4.13.2###sunflower:devhelpertools:unspecified###com.android.tools.utp:android-test-plugin-host-coverage:31.4.0###com.google.zxing:core:3.3.0###net.engawapg.lib:zoomable:1.6.1###androidx.test:monitor:1.6.1###com.amap.api:location:5.6.2###androidx.test.uiautomator:uiautomator:2.3.0###androidx.constraintlayout:constraintlayout:1.1.3###com.google.code.gson:gson:2.10.1###androidx.lifecycle:lifecycle-livedata-ktx:2.7.0###androidx.legacy:legacy-support-v4:1.0.0###androidx.activity:activity-compose:1.9.0###androidx.recyclerview:recyclerview:1.0.0###com.google.accompanist:accompanist-systemuicontroller:0.34.0###";

} 