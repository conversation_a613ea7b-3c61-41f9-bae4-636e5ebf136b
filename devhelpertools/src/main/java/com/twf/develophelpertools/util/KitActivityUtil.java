package com.twf.develophelpertools.util;

import android.app.Activity;
import android.app.Application;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * create by guofeng
 * date on 2021/4/20
 */

public class KitActivityUtil {



    public static List<Class> getActivitiesByApplication(Application application) {

        List<Class> returnClassList = new ArrayList<Class>();
        try {
            //Get all activity classes in the AndroidManifest.xml
            PackageInfo packageInfo = application.getPackageManager().getPackageInfo(application.getPackageName(), PackageManager.GET_ACTIVITIES);
            if(packageInfo.activities!=null){
                for(ActivityInfo ai: packageInfo.activities){
                    Class c;
                    try {
                        c = Class.forName(ai.name);
                        // Maybe isAssignableFrom is unnecessary
                        if(Activity.class.isAssignableFrom(c)){
                            returnClassList.add(c);
                        }
                    } catch (ClassNotFoundException e) {
                    }
                }

                //Exclude some activity classes
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return returnClassList;
    }
}