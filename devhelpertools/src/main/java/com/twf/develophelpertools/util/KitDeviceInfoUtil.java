package com.twf.develophelpertools.util;

import android.app.Application;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Environment;
import android.os.StatFs;
import android.text.format.Formatter;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.WindowManager;

import com.twf.develophelpertools.Kit;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.TimeZone;

/**
 * Created by guofeng
 * on 2019/5/30.
 * 设备信息工具类
 */
public class KitDeviceInfoUtil {

    public static String getAppPackageName() {
        Application application = Kit.getInstance().getApplication();
        return application.getPackageName();
    }

    public static String getAppVersion() {
        Application application = Kit.getInstance().getApplication();
        PackageManager pManager = application.getPackageManager();
        try {
            PackageInfo pInfo = pManager.getPackageInfo(application.getPackageName(),
                    PackageManager.GET_CONFIGURATIONS);
            return pInfo.versionCode + "";
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String getAppVersionName() {
        Application application = Kit.getInstance().getApplication();
        PackageManager pManager = application.getPackageManager();
        try {
            PackageInfo pInfo = pManager.getPackageInfo(application.getPackageName(),
                    PackageManager.GET_CONFIGURATIONS);
            return pInfo.versionName;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String minSupportVersion() {
        Application application = Kit.getInstance().getApplication();
        ApplicationInfo applicationInfo = application.getApplicationInfo();
        if (applicationInfo != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                return applicationInfo.minSdkVersion + "";
            }
        }
        return "";
    }

    public static String targetSupportVersion() {
        Application application = Kit.getInstance().getApplication();
        ApplicationInfo applicationInfo = application.getApplicationInfo();
        if (applicationInfo != null) {
            return applicationInfo.targetSdkVersion + "";
        }
        return "";
    }

    public static String getDeviceName() {

        return Build.DEVICE;
    }

    public static String getDeviceVersion() {

        return Build.VERSION.RELEASE;
    }

    public static boolean isRoot() {
        Process process = null;
        DataOutputStream os = null;
        try {
            process = Runtime.getRuntime().exec("su");
            os = new DataOutputStream(process.getOutputStream());
            os.writeBytes("exit\n");
            os.flush();
            int exitValue = process.waitFor();
            if (exitValue == 0) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            return false;
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
                process.destroy();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static String getScreenWidth() {
        DisplayMetrics metrics = new DisplayMetrics();
        Application application = Kit.getInstance().getApplication();
        WindowManager windowManager = (WindowManager) application.getSystemService(Context.WINDOW_SERVICE);
        if (windowManager != null) {
            windowManager.getDefaultDisplay().getMetrics(metrics);
            return metrics.widthPixels + "";
        }
        return "";
    }

    public static String getAvailableWidth() {
        Application application = Kit.getInstance().getApplication();
        WindowManager windowManager = (WindowManager) application.getSystemService(Context.WINDOW_SERVICE);
        if (windowManager != null) {
            return windowManager.getDefaultDisplay().getWidth() + "";
        }
        return "";
    }

    public static String getAvailableHeight() {
        Application application = Kit.getInstance().getApplication();
        WindowManager windowManager = (WindowManager) application.getSystemService(Context.WINDOW_SERVICE);
        if (windowManager != null) {
            return windowManager.getDefaultDisplay().getHeight() + "";
        }
        return "";
    }

    public static String getTimeZone() {
        return TimeZone.getDefault().getID();
    }


    public static String getevicePixelRatio() {
        DisplayMetrics metrics = new DisplayMetrics();
        Application application = Kit.getInstance().getApplication();
        WindowManager windowManager = (WindowManager) application.getSystemService(Context.WINDOW_SERVICE);
        if (windowManager != null) {
            Display defaultDisplay = windowManager.getDefaultDisplay();
            defaultDisplay.getMetrics(metrics);
            return metrics.density + "";
        }
        return "";
    }



    public static String getCPUInfo(){
        StringBuffer sb = new StringBuffer();
        sb.append("abi: ").append(Build.CPU_ABI).append("n");
        if (new File("/proc/cpuinfo").exists()) {
            try {
                BufferedReader br = new BufferedReader(new FileReader(new File("/proc/cpuinfo")));
                String aLine;
                while ((aLine = br.readLine()) != null) {
                    sb.append(aLine + "n");
                }
                if (br != null) {
                    br.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return sb.toString();
    }

    public static String getScreenHeight() {
        DisplayMetrics metrics = new DisplayMetrics();
        Application application = Kit.getInstance().getApplication();
        WindowManager windowManager = (WindowManager) application.getSystemService(Context.WINDOW_SERVICE);
        if (windowManager != null) {
            windowManager.getDefaultDisplay().getMetrics(metrics);
            return metrics.heightPixels + "";
        }
        return "";
    }

    public static String getDensity() {
        DisplayMetrics metrics = new DisplayMetrics();
        Application application = Kit.getInstance().getApplication();
        WindowManager windowManager = (WindowManager) application.getSystemService(Context.WINDOW_SERVICE);
        if (windowManager != null) {
            windowManager.getDefaultDisplay().getMetrics(metrics);
            return metrics.widthPixels + "x" + metrics.heightPixels;
        }
        return "";
    }

    public static String getSDCardSpace(Context context) {
        try {
            String free = getSDAvailableSize(context);
            String total = getSDTotalSize(context);
            return free + "/" + total;
        } catch (Exception e) {
            return "-/-";
        }
    }

    /**
     * 获得SD卡总大小
     *
     * @return
     */
    private static String getSDTotalSize(Context context) {
        File path = Environment.getExternalStorageDirectory();
        StatFs stat = new StatFs(path.getPath());
        long blockSize = stat.getBlockSize();
        long totalBlocks = stat.getBlockCount();
        return Formatter.formatFileSize(context, blockSize * totalBlocks);
    }

    /**
     * 获得sd卡剩余容量，即可用大小
     *
     * @return
     */
    private static String getSDAvailableSize(Context context) {
        File path = Environment.getExternalStorageDirectory();
        StatFs stat = new StatFs(path.getPath());
        long blockSize = stat.getBlockSize();
        long availableBlocks = stat.getAvailableBlocks();
        return Formatter.formatFileSize(context, blockSize * availableBlocks);
    }

    /**
     * 获得机身内存总大小
     *
     * @return
     */
    private static String getRomTotalSize(Context context) {
        File path = Environment.getDataDirectory();
        StatFs stat = new StatFs(path.getPath());
        long blockSize = stat.getBlockSize();
        long totalBlocks = stat.getBlockCount();
        return Formatter.formatFileSize(context, blockSize * totalBlocks);
    }

    public static String getResolution() {
        Application application = Kit.getInstance().getApplication();
        WindowManager windowManager = (WindowManager) application.getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics displayMetrics = new DisplayMetrics();
        if (windowManager != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                windowManager.getDefaultDisplay().getRealMetrics(displayMetrics);
            } else {
                windowManager.getDefaultDisplay().getMetrics(displayMetrics);
            }
            return displayMetrics.widthPixels + "_" + displayMetrics.heightPixels;
        }
        return "";
    }

    public static String getDeviceRemainMemory() {
        try {
            String free = getRomAvailableSize(Kit.getInstance().getApplication());
            String total = getRomTotalSize(Kit.getInstance().getApplication());
            return free + "/" + total;
        } catch (Exception e) {
            return "-/-";
        }
    }

    /**
     * 获得机身可用内存
     *
     * @return
     */
    private static String getRomAvailableSize(Context context) {
        File path = Environment.getDataDirectory();
        StatFs stat = new StatFs(path.getPath());
        long blockSize = stat.getBlockSize();
        long availableBlocks = stat.getAvailableBlocks();
        return Formatter.formatFileSize(context, blockSize * availableBlocks);
    }
}
