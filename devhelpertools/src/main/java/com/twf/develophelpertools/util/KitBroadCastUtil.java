package com.twf.develophelpertools.util;

import android.app.Application;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.text.TextUtils;

import com.twf.develophelpertools.bean.JiraImageBean;
import com.twf.develophelpertools.view.AddIssueDialog;

//import static com.stone.cold.screenrecorder.rain.screenrecorder.oQO.adrr.ACTION_FILE_PATH;
//import static com.stone.cold.screenrecorder.rain.screenrecorder.oQO.adrr.ACTION_FILE_TYPE;
//import static com.stone.cold.screenrecorder.rain.screenrecorder.oQO.adrr.ACTION_SHOW_ADD_ISSUE;

/**
 * create by guofeng
 * date on 2021/7/27
 */

public class KitBroadCastUtil {


    //注册广播
//    public void registerBroadCast(Application app) {
//        IntentFilter intentFilter = new IntentFilter();
//        intentFilter.addAction(ACTION_SHOW_ADD_ISSUE);
//        app.registerReceiver(receiver, intentFilter);
//    }
//
//
//
//    private final BroadcastReceiver receiver = new BroadcastReceiver() {
//        @Override
//        public void onReceive(Context context, Intent intent) {
//            String action = intent.getAction();
//            if (TextUtils.equals(ACTION_SHOW_ADD_ISSUE, action)) {
//                String path = intent.getStringExtra(ACTION_FILE_PATH);
//                String type = intent.getStringExtra(ACTION_FILE_TYPE);
//                AddIssueDialog addIssueDialog = new AddIssueDialog();
//                JiraImageBean jiraImageBean = new JiraImageBean();
//                jiraImageBean.setLocalPath(path);
//                jiraImageBean.setFileType(type);
//                addIssueDialog.setJiraImageBean(jiraImageBean);
//                addIssueDialog.show();
//            }
//        }
//    };

} 