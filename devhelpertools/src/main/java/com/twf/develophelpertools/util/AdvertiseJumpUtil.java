package com.twf.develophelpertools.util;

import android.app.Activity;
import android.app.Application;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.kit.baselibrary.AdvertiseJumpBean;
import com.kit.baselibrary.AppCallBackItf;
import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.model.fragment.SwitchConfigFragment;

import java.util.List;

/**
 * create by guofeng
 * date on 2021/7/29
 */

public class AdvertiseJumpUtil {


    public void setJumpAdvertiseListener(Application application) {

        application.registerActivityLifecycleCallbacks(new Application.ActivityLifecycleCallbacks() {

            @Override
            public void onActivityCreated(Activity activity, Bundle savedInstanceState) {

            }

            @Override
            public void onActivityStarted(Activity activity) {

            }

            @Override
            public void onActivityResumed(Activity activity) {

                AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
                if (appCallBackItf == null) return;
                List<AdvertiseJumpBean> advertiseJumpList = appCallBackItf.getAdvertiseJumpList();

                if (advertiseJumpList == null || advertiseJumpList.size() == 0) return;


                boolean jumpAd = SwitchConfigFragment.isOpenJumpAdvertisement();

                if (!jumpAd) return;

                String simpleName = activity.getClass().getSimpleName();


                for (AdvertiseJumpBean jumpPage : advertiseJumpList) {
                    String className = jumpPage.getClassName();
                    int jumpButtonId = jumpPage.getJumpButtonId();
                    String jumpText = jumpPage.getJumpText();

                    if (TextUtils.equals(className, simpleName)) {
                        //通过id找到点击的view&&点击
                        if (jumpButtonId > 0) {
                            findButtonByIdClick(activity, jumpButtonId);
                        } else {
                            //通过文案找到控件&&点击
                            findButtonByTextAndClick(activity, jumpText);
                        }

                    }
                }

            }

            @Override
            public void onActivityPaused(Activity activity) {

            }

            @Override
            public void onActivityStopped(Activity activity) {

            }

            @Override
            public void onActivitySaveInstanceState(Activity activity, Bundle outState) {

            }

            @Override
            public void onActivityDestroyed(Activity activity) {

            }
        });
    }

    //通过id找到view 点击
    private void findButtonByIdClick(Activity activity, int jumpButtonId) {
        ViewGroup decorView = (ViewGroup) activity.getWindow().getDecorView();
        ViewGroup mRootView = decorView.findViewById(android.R.id.content);
        View viewById = mRootView.findViewById(jumpButtonId);
        if (viewById != null) {
            viewById.performClick();
        }
    }

    //找到布局的文本内容，并且点击
    private void findButtonByTextAndClick(Activity activity, String strings) {
        ViewGroup decorView = (ViewGroup) activity.getWindow().getDecorView();
        ViewGroup mRootView = decorView.findViewById(android.R.id.content);
        findClickText(mRootView, strings);
    }

    private void findClickText(ViewGroup viewGroup, String strings) {
        if (viewGroup == null) return;
        int childCount = viewGroup.getChildCount();
        if (childCount <= 0) return;
        for (int i = 0; i < childCount; i++) {
            View child = viewGroup.getChildAt(i);

            if (child instanceof ViewGroup) {
                findClickText((ViewGroup) child, strings);
            } else {
                if (child instanceof TextView) {

                    String value = ((TextView) child).getText().toString();

                    if (value.contains(strings)) {
                        //点击view
                        child.performClick();
                    }
                }
            }

        }
    }
} 