package com.twf.develophelpertools.util;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.view.View;

/**
 * Created by guofeng
 * on 2019/6/12.
 */

public class KitDialogUtil {

    private Activity activity;

    private String title;

    private String message;


    public KitDialogUtil(Activity activity) {
        this.activity = activity;
    }


    public void setTitle(String title) {
        this.title = title;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    private View.OnClickListener positiveListener;

    private View.OnClickListener negativeListener;

    public void setPositiveListener(View.OnClickListener positiveListener) {
        this.positiveListener = positiveListener;
    }

    public void setNegativeListener(View.OnClickListener negativeListener) {
        this.negativeListener = negativeListener;
    }

    private AlertDialog alertDialog;

    public void show() {
        AlertDialog.Builder builder = new AlertDialog.Builder(activity);
        builder.setTitle(title);
        builder.setMessage(message);
        builder.setPositiveButton("确定", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                alertDialog.dismiss();
                if (positiveListener != null) {
                    positiveListener.onClick(null);
                }
            }
        });
        builder.setNegativeButton("取消", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                alertDialog.dismiss();
                if (negativeListener != null) {
                    negativeListener.onClick(null);
                }
            }
        });

        alertDialog = builder.create();

        alertDialog.show();
    }


    public void show(View convertView) {
        alertDialog = new AlertDialog.Builder(activity).create();
        alertDialog.setTitle(title);
        alertDialog.setMessage(message);
        alertDialog.show();
        alertDialog.setContentView(convertView);
    }


}
