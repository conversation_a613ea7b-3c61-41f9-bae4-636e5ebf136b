package com.twf.develophelpertools.util;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by guofeng
 * on 2019/6/25.
 */

public class KitLogcatUtil {


    public static Process getLogcatProcess() throws Exception {
        List<String> args = new ArrayList<>(Arrays.asList("logcat", "-v", "time"));
        return Runtime.getRuntime().exec(toArray(args, String.class));
    }

    private static <T> T[] toArray(List<T> list, Class<T> clazz) {
        @SuppressWarnings("unchecked")
        T[] result = (T[]) Array.newInstance(clazz, list.size());
        for (int i = 0; i < list.size(); i++) {
            result[i] = list.get(i);
        }
        return result;
    }


    public static String logcatFormatter(String text) {
        if (text == null) return null;
        int start = text.indexOf("{");
        int last = text.lastIndexOf("}");
        if (start != -1 && last != -1) {
            String head = text.substring(0, start + 1);
            String json = text.substring(start, last + 1);
            String end = text.substring(last, text.length());

            try {
                final JSONObject jsonObject = new JSONObject(json);
                String string = jsonObject.toString(2);
                return head + "\n" + string + "\n" + end;
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        return text;
    }
}
