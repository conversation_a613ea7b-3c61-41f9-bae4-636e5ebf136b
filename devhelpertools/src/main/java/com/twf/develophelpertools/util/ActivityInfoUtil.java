package com.twf.develophelpertools.util;

import android.app.Activity;
import android.app.Application;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.twf.develophelpertools.model.fragment.SwitchConfigFragment;
import com.twf.develophelpertools.toppageinfo.TopPageInfoFinder;

/**
 * create by guofeng
 * date on 2021/8/6
 */

public class ActivityInfoUtil {


    public void register(final Application application) {
        application.registerActivityLifecycleCallbacks(new Application.ActivityLifecycleCallbacks() {
            @Override
            public void onActivityCreated(@NonNull Activity activity, Bundle savedInstanceState) {
                // 检测是否显示图标
                if (SwitchConfigFragment.isOpenActivityInfo()) {
                    TopPageInfoFinder.getInstance().switchFloat(activity);
                }
                application.unregisterActivityLifecycleCallbacks(this);
            }

            @Override
            public void onActivityStarted(@NonNull Activity activity) {

            }

            @Override
            public void onActivityResumed(@NonNull Activity activity) {

            }

            @Override
            public void onActivityPaused(@NonNull Activity activity) {

            }

            @Override
            public void onActivityStopped(@NonNull Activity activity) {

            }

            @Override
            public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {

            }

            @Override
            public void onActivityDestroyed(@NonNull Activity activity) {

            }
        });
    }

} 