package com.twf.develophelpertools.util;

import android.app.Instrumentation;
import android.os.SystemClock;
import android.view.KeyEvent;
import android.view.MotionEvent;

/**
 * create by guofeng
 * date on 2021/3/24
 * 模拟点击事件
 */

public class KitMockClickUtil {


    /**
     * 模拟点击
     *
     * @param randomX
     * @param randomY
     */
    public static void onClickEvent(int randomX, int randomY) {
        try {
            Instrumentation inst = new Instrumentation();
            inst.sendPointerSync(MotionEvent.obtain(SystemClock.uptimeMillis(), SystemClock.uptimeMillis(),
                    MotionEvent.ACTION_DOWN, randomX, randomY, 0));

            inst.sendPointerSync(MotionEvent.obtain(SystemClock.uptimeMillis(), SystemClock.uptimeMillis(),
                    MotionEvent.ACTION_UP, randomX, randomY, 0));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }




    //滑动的总距离
    private static final int FLINGER_DISTANCE = KitScaleUtil.dip2px(30);

    //滑动的次数
    public static final int LIMIT_COUNT = 3;

    /**
     * 向右边滑动
     *
     * @param randomX
     * @param randomY
     */
    public static void onFlingerRight(int randomX, int randomY) {
        try {
            Instrumentation inst = new Instrumentation();
            long dowTime = SystemClock.uptimeMillis();
            inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime,
                    MotionEvent.ACTION_DOWN, randomX, randomY, 0));
            inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime,
                    MotionEvent.ACTION_MOVE, randomX, randomY, 0));

            int distance = FLINGER_DISTANCE / LIMIT_COUNT;

            for (int i = 1; i <= LIMIT_COUNT; i++) {
                inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime + (i * 10),
                        MotionEvent.ACTION_MOVE, randomX + (distance * i), randomY, 0));
            }
            inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime + LIMIT_COUNT * 10,
                    MotionEvent.ACTION_UP, randomX + FLINGER_DISTANCE, randomY, 0));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 向左边边滑动
     *
     * @param randomX
     * @param randomY
     */
    public static void onFlingerLeft(int randomX, int randomY) {
        try {
            Instrumentation inst = new Instrumentation();
            long dowTime = SystemClock.uptimeMillis();
            inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime,
                    MotionEvent.ACTION_DOWN, randomX, randomY, 0));
            inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime,
                    MotionEvent.ACTION_MOVE, randomX, randomY, 0));

            int distance = -FLINGER_DISTANCE / LIMIT_COUNT;

            for (int i = 1; i <= LIMIT_COUNT; i++) {
                inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime + (i * 10),
                        MotionEvent.ACTION_MOVE, randomX + (distance * i), randomY, 0));
            }

            inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime + LIMIT_COUNT * 10,
                    MotionEvent.ACTION_UP, randomX - FLINGER_DISTANCE, randomY, 0));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 像下滑动
     *
     * @param randomX
     * @param randomY
     */
    public static void onFlingerDown(int randomX, int randomY) {
        try {
            Instrumentation inst = new Instrumentation();
            long dowTime = SystemClock.uptimeMillis();
            inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime,
                    MotionEvent.ACTION_DOWN, randomX, randomY, 0));
            inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime,
                    MotionEvent.ACTION_MOVE, randomX, randomY, 0));

            int distance = FLINGER_DISTANCE / LIMIT_COUNT;
            for (int i = 1; i <= LIMIT_COUNT; i++) {
                inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime + (i * 10),
                        MotionEvent.ACTION_MOVE, randomX, randomY + (distance * i), 0));
            }
            inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime + LIMIT_COUNT * 10,
                    MotionEvent.ACTION_UP, randomX, randomY + FLINGER_DISTANCE, 0));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 像上滑动
     *
     * @param randomX
     * @param randomY
     */
    public static void onFlingerUp(int randomX, int randomY) {
        try {
            Instrumentation inst = new Instrumentation();
            long dowTime = SystemClock.uptimeMillis();
            inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime,
                    MotionEvent.ACTION_DOWN, randomX, randomY, 0));
            inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime,
                    MotionEvent.ACTION_MOVE, randomX, randomY, 0));

            int distance = -FLINGER_DISTANCE / LIMIT_COUNT;
            for (int i = 1; i <= LIMIT_COUNT; i++) {
                inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime + (i * 10),
                        MotionEvent.ACTION_MOVE, randomX, randomY + (distance * i), 0));
            }
            inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime + LIMIT_COUNT * 10,
                    MotionEvent.ACTION_UP, randomX, randomY - FLINGER_DISTANCE, 0));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    //执行返回操作
    public static void onPressBackListener() {
        try {
            Instrumentation inst = new Instrumentation();
            inst.sendKeyDownUpSync(KeyEvent.KEYCODE_BACK);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

} 