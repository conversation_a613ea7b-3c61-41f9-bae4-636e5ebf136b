package com.twf.develophelpertools.util;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;

/**
 * create by guofeng
 * date on 2020/8/6
 */
public class KitCopyDialogUtil {

    private Context context;

    public KitCopyDialogUtil(Context context) {
        this.context = context;
    }

    private OnCopyCallBack onCopyCallBack;

    public void setOnCopyCallBack(OnCopyCallBack onCopyCallBack) {
        this.onCopyCallBack = onCopyCallBack;
    }

    public void show() {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setTitle("温馨提示")

                .setItems(new String[]{"全部复制", "复制此节点数据"}, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {

                        //全部复制
                        if (which == 0) {
                            if (onCopyCallBack != null) {
                                onCopyCallBack.onCopyAllListener();
                            }
                            return;
                        }

                        //复制部分节点
                        if (onCopyCallBack != null) {
                            onCopyCallBack.onCopyLocalListener();
                        }
                    }
                });

        builder.create().show();
    }


    public interface OnCopyCallBack {

        void onCopyAllListener();

        void onCopyLocalListener();
    }


} 