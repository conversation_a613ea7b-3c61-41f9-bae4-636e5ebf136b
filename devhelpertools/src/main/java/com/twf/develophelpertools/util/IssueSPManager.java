package com.twf.develophelpertools.util;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.twf.develophelpertools.bean.JiraItemBean;

import java.util.List;

/**
 * create by guofeng
 * date on 2021/5/10
 */

public class IssueSPManager {

    private IssueSPManager() {
    }

    private static IssueSPManager instance = new IssueSPManager();

    public static IssueSPManager getInstance() {
        return instance;
    }

    private static final String SP_JIRA_TASK_MANAGER = "SP_JIRA_TASK_MANAGER";

    /**
     * 保存jira数据
     */
    public void saveAllIssueList(List<JiraItemBean> value) {
        if (value != null) {
            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.setPrettyPrinting();
            Gson gson = gsonBuilder.create();
            String jsonValue = gson.toJson(value);
            KitSpUtil.putString(SP_JIRA_TASK_MANAGER, jsonValue);
        }
    }



    /**
     * 获得问题列表所有数据
     *
     * @return
     */
    public List<JiraItemBean> getIssueJiraList() {
        String value = KitSpUtil.getString(SP_JIRA_TASK_MANAGER);
        if (value != null) {
            return new Gson().fromJson(value, new TypeToken<List<JiraItemBean>>() {
            }.getType());
        }
        return null;
    }



}