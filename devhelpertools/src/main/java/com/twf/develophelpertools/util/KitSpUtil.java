package com.twf.develophelpertools.util;

import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;

import com.twf.develophelpertools.Kit;

import java.util.HashSet;
import java.util.Set;

/**
 * Created by guofeng
 * on 2019/6/24.
 */

public class KitSpUtil {


    private static final String KIT_SP_TABLE = "KIT_SP_TABLE";

    public static final String KIT_H5_PAGE_ACTIVITY = "KIT_H5_PAGE_ACTIVITY";

    private static SharedPreferences getSharedPreference() {
        Application application = Kit.getInstance().getApplication();
        if (application != null) {
            return application.getSharedPreferences(KIT_SP_TABLE, Context.MODE_PRIVATE);
        }
        return null;
    }

    public static boolean putString(String key, String value) {
        SharedPreferences sharedPreference = getSharedPreference();
        if (sharedPreference != null) {
            SharedPreferences.Editor edit = sharedPreference.edit();
            edit.putString(key, value);
            return edit.commit();
        }
        return false;
    }

    public static boolean putBoolean(String key, boolean value) {
        SharedPreferences sharedPreference = getSharedPreference();
        if (sharedPreference != null) {
            SharedPreferences.Editor edit = sharedPreference.edit();
            edit.putBoolean(key, value);
            return edit.commit();
        }
        return false;
    }

    public static boolean getBoolean(String key, boolean defValue) {
        SharedPreferences sharedPreference = getSharedPreference();
        if (sharedPreference != null) {
            return sharedPreference.getBoolean(key, defValue);
        }
        return false;
    }

    public static int getInt(String key, int defValue) {
        SharedPreferences sharedPreference = getSharedPreference();
        if (sharedPreference != null) {
            return sharedPreference.getInt(key, defValue);
        }
        return 0;
    }

    public static void putInt(String key, int value) {
        SharedPreferences sharedPreference = getSharedPreference();
        if (sharedPreference != null) {
            SharedPreferences.Editor edit = sharedPreference.edit();
            edit.putInt(key, value);
            edit.commit();
        }
    }


    public static void putStringSet(String key, Set<String> value) {
        SharedPreferences sharedPreference = getSharedPreference();
        if (sharedPreference != null) {
            SharedPreferences.Editor edit = sharedPreference.edit();
            edit.putStringSet(key, value);
            edit.commit();
        }
    }

    public static Set<String> getStringSet(String key) {
        SharedPreferences sharedPreference = getSharedPreference();
        if (sharedPreference != null) {
            Set<String> stringSet = sharedPreference.getStringSet(key, null);
            if (stringSet != null) {
                return stringSet;
            }
        }
        return new HashSet<>();
    }


    public static String getString(String key) {
        SharedPreferences sharedPreference = getSharedPreference();
        if (sharedPreference != null) {
            return sharedPreference.getString(key, null);
        }
        return "";
    }

}
