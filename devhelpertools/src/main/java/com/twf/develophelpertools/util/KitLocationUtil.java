package com.twf.develophelpertools.util;

import android.text.TextUtils;

import com.twf.develophelpertools.model.fragment.MockLocationFragment;
import com.twf.develophelpertools.model.fragment.SwitchConfigFragment;

import org.json.JSONObject;

import java.lang.reflect.Field;

import static com.twf.develophelpertools.model.fragment.MockLocationFragment.SP_MOCK_LOCATION_INFO;

/**
 * create by guofeng
 * date on 2021/7/27
 */

public class KitLocationUtil {

    public void inflateMockLocation(Class<?> tClass, Object object) {


        String value = KitSpUtil.getString(SP_MOCK_LOCATION_INFO);
        if (TextUtils.isEmpty(value)) return;

        if (tClass != null) {
            try {
                Field radius = tClass.getField("radius");
                Field latitude = tClass.getField("latitude");
                Field longitude = tClass.getField("longitude");
                Field province = tClass.getField("province");
                Field city = tClass.getField("city");
                Field cityCode = tClass.getField("cityCode");
                Field district = tClass.getField("district");
                Field street = tClass.getField("street");
                Field streetNumber = tClass.getField("streetNumber");
                Field address = tClass.getField("address");


                JSONObject jsonObject = new JSONObject(value);
                String mProvice = jsonObject.optString(MockLocationFragment.PROVINCE);
                String mCity = jsonObject.optString(MockLocationFragment.CITY);
                String mDistrict = jsonObject.optString(MockLocationFragment.DISTRICT);
                String mCityCode = jsonObject.optString(MockLocationFragment.CITY_CODE);
                String mStreetNumber = jsonObject.optString(MockLocationFragment.STREET_NUMBER);
                String mAddress = jsonObject.optString(MockLocationFragment.FORMAT_ADDRESS);
                String mLatitude = jsonObject.optString(MockLocationFragment.LOCAL_LATITUDE);
                String mLongitude = jsonObject.optString(MockLocationFragment.LOCAL_LONGITUDE);

                radius.set(object, 1000);
                latitude.set(object, Double.valueOf(mLatitude));
                longitude.set(object, Double.valueOf(mLongitude));
                province.set(object, mProvice);
                city.set(object, mCity);
                district.set(object, mDistrict);
                cityCode.set(object, mCityCode);
                street.set(object, mAddress);
                streetNumber.set(object, mStreetNumber);
                address.set(object, mAddress);

            } catch (Exception e) {
                e.printStackTrace();
            }
        }


    }

} 