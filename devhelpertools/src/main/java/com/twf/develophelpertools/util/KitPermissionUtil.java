package com.twf.develophelpertools.util;

import android.app.Activity;
import android.app.AppOpsManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Process;
import android.provider.Settings;
import androidx.core.content.ContextCompat;

import com.twf.develophelpertools.Kit;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

import static android.Manifest.permission.ACCESS_FINE_LOCATION;
import static android.Manifest.permission.CAMERA;
import static android.Manifest.permission.RECORD_AUDIO;
import static android.Manifest.permission.WRITE_CONTACTS;
import static android.Manifest.permission.WRITE_EXTERNAL_STORAGE;

/**
 * Created by guofeng
 * on 2019/5/30.
 */

public class KitPermissionUtil {


    public static boolean hasLocationPermission() {
        int result = ContextCompat.checkSelfPermission(Kit.getInstance().getApplication(), ACCESS_FINE_LOCATION);
        return result == PackageManager.PERMISSION_GRANTED;
    }

    public static boolean hasWriteExternalPermission() {
        int result = ContextCompat.checkSelfPermission(Kit.getInstance().getApplication(), WRITE_EXTERNAL_STORAGE);
        return result == PackageManager.PERMISSION_GRANTED;
    }

    public static boolean hasCameraPermission() {
        int result = ContextCompat.checkSelfPermission(Kit.getInstance().getApplication(), CAMERA);
        return result == PackageManager.PERMISSION_GRANTED;
    }


    public static boolean hasRecordPermission() {
        int result = ContextCompat.checkSelfPermission(Kit.getInstance().getApplication(), RECORD_AUDIO);
        return result == PackageManager.PERMISSION_GRANTED;
    }

    public static boolean hasReadContactPermission() {
        int result = ContextCompat.checkSelfPermission(Kit.getInstance().getApplication(), WRITE_CONTACTS);
        return result == PackageManager.PERMISSION_GRANTED;
    }

    private static final int OP_SYSTEM_ALERT_WINDOW = 24;

    public static boolean canDrawOverlays(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            return Settings.canDrawOverlays(context);
        }
        return checkOp(context, OP_SYSTEM_ALERT_WINDOW);
    }

    private static boolean checkOp(Context context, int op) {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.KITKAT) {
            AppOpsManager manager = (AppOpsManager) context.getSystemService(Context.APP_OPS_SERVICE);
            Class clazz = AppOpsManager.class;
            try {
                Method method = clazz.getDeclaredMethod("checkOp", int.class, int.class, String.class);
                return AppOpsManager.MODE_ALLOWED == (int) method.invoke(manager, op, Process.myUid(), context.getPackageName());
            } catch (NoSuchMethodException e) {
                e.printStackTrace();
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            } catch (InvocationTargetException e) {
                e.printStackTrace();
            }
        }
        return true;
    }

    public static void requestDrawOverlays(Context context) {
        Intent intent = new Intent("android.settings.action.MANAGE_OVERLAY_PERMISSION", Uri.parse("package:" + context.getPackageName()));
        if (!(context instanceof Activity)) {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        if (intent.resolveActivity(context.getPackageManager()) != null) {
            context.startActivity(intent);
        }
    }

}
