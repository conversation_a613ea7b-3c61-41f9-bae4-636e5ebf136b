package com.twf.develophelpertools.util;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;

import com.twf.develophelpertools.Kit;

/**
 * Created by guofeng
 * on 2019/7/9.
 */

public class KitClipBoardUtil {

    /**
     * 复制到系统剪切板
     *
     * @param content
     */
    public static void copyToClipBoard(String content) {
        ClipboardManager cmb = (ClipboardManager) Kit.getInstance().getApplication().getSystemService(Context.CLIPBOARD_SERVICE);
        if (cmb != null) {
            cmb.setPrimaryClip(ClipData.newPlainText(null, content));
        }
    }
}
