package com.twf.develophelpertools.util;

import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.view.inputmethod.InputMethodManager;

/**
 * create by guofeng
 * date on 2021/3/24
 */

public class KitKeyboardUtil {


    /**
     * Hides the soft input if it is active for the input text.
     */
    public static void hideSoftInput(Activity activity) {
        if (activity == null) return;

        InputMethodManager inputMethodManager = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        if (inputMethodManager != null) {
            View currentFocus = activity.getCurrentFocus();
            if (currentFocus != null) {
                inputMethodManager.hideSoftInputFromWindow(currentFocus.getWindowToken(), InputMethodManager.HIDE_NOT_ALWAYS);
            }
        }
    }
} 