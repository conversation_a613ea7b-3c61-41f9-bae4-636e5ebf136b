package com.twf.develophelpertools.util;


import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

/**
 * created by tong<PERSON><PERSON><PERSON>.
 * date: 8/3/22
 * time: 2:58 PM
 * description:
 */
public class ActivityNameStackUtils {

    /**
     * 用于装最近的N个Activity 的name
     */
    private final List<String> activityName = Collections.synchronizedList(new ArrayList<String>());

    private static final int N = 5;

    public static ActivityNameStackUtils getInstance() {
        return SingletonHolder.INSTANCE;
    }


    public void pushName(String name) {
        if (activityName.size() >= N) {
            activityName.remove(0);
        }
        activityName.add(name);

    }


    public String getActivityInfo() {
        return new Gson().toJson(activityName);
    }


    static final class SingletonHolder {
        static final ActivityNameStackUtils INSTANCE = new ActivityNameStackUtils();
    }


}
