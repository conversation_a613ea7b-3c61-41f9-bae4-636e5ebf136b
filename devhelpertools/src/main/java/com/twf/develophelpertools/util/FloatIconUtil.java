package com.twf.develophelpertools.util;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentManager.FragmentLifecycleCallbacks;

import com.kit.baselibrary.AppCallBackItf;
import com.kit.baselibrary.MessageLoginBean;
//import com.stone.cold.screenrecorder.rain.screenrecorder.ap.wcm;
//import com.stone.cold.screenrecorder.rain.screenrecorder.fl.sf;
import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.model.fragment.SwitchConfigFragment;
import com.twf.develophelpertools.model.pickcolor.FloatIconPage;
import com.twf.develophelpertools.model.pickcolor.FloatPageManager;
import com.twf.develophelpertools.model.pickcolor.PageIntent;
import com.twf.develophelpertools.toppageinfo.TopPageInfoFinder;

import java.util.List;

import static com.twf.develophelpertools.model.fragment.IssueTaskManagerFragment.SP_IS_OPEN_TUYA;
import static com.twf.develophelpertools.model.pickcolor.PageIntent.ICON_TAG;

/**
 * create by guofeng
 * date on 2021/7/27
 */

public class FloatIconUtil {

    private static final FloatIconUtil instance = new FloatIconUtil();

    private FloatIconUtil() {
    }

    public static FloatIconUtil getInstance() {
        return instance;
    }

    private final H5CoverUtil h5CoverUtil = new H5CoverUtil();



    private int actCount;

    public void registerMainLifeCircle() {

        Kit.getInstance().getApplication().registerActivityLifecycleCallbacks(new Application.ActivityLifecycleCallbacks() {
            @Override
            public void onActivityCreated(Activity activity, Bundle savedInstanceState) {

            }

            @Override
            public void onActivityStarted(Activity activity) {
            }

            @Override
            public void onActivityResumed(Activity activity) {
                actCount++;
                //检测是否显示图标
                checkShowIcon();
                //检测是否h5页面
                h5CoverUtil.checkIsH5Activity(activity);
            }



            @Override
            public void onActivityPaused(Activity activity) {

            }

            @Override
            public void onActivityStopped(Activity activity) {
                actCount--;
                checkAppRunningBackGround();
            }

            @Override
            public void onActivitySaveInstanceState(Activity activity, Bundle outState) {
            }

            @Override
            public void onActivityDestroyed(Activity activity) {

            }
        });
    }


    private static final String TAG = "FloatIconUtil";


    //检测应用是否在后台
    private void checkAppRunningBackGround() {
        //应用在后台 隐藏icon
        if (actCount == 0) {
            FloatPageManager.getInstance().remove(ICON_TAG);
        }
    }

    private void checkShowIcon() {
        showFloatIcon();
    }


    private boolean sHasRequestPermission;


    //显示入口图标
    private void showFloatIcon() {
        //是否有漂浮顶部权限
        if (KitPermissionUtil.canDrawOverlays(Kit.getInstance().getApplication())) {
            PageIntent intent = new PageIntent(FloatIconPage.class);
            intent.tag = ICON_TAG;
            intent.mode = PageIntent.MODE_SINGLE_INSTANCE;
            FloatPageManager.getInstance().add(intent);

//            checkScrawlIcon();

        } else {
            if (!sHasRequestPermission) {
                KitPermissionUtil.requestDrawOverlays(Kit.getInstance().getApplication());
                sHasRequestPermission = true;
            }
        }
    }

    /**
     * 检测涂鸦悬浮的ICON是否显示
     */
//    public void checkScrawlIcon() {
//        boolean isOpen = KitSpUtil.getBoolean(SP_IS_OPEN_TUYA, false);
//        if (isOpen) {
//            sf.a(Kit.getInstance().getApplication(), "ACTION_NORMAL");
//            sf.a(Kit.getInstance().getApplication(), "ACTION_SHOW_SCREEN_SHOT_VIEW");
//        } else {
//            sf.a(wcm.getMyContextWrapper());
//        }
//    }


} 