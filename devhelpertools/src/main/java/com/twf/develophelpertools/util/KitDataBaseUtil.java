package com.twf.develophelpertools.util;

import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by guofeng
 * on 2019/6/21.
 */

public class KitDataBaseUtil {


    /**
     * 获得所以的数据库表名
     *
     * @param sqLiteDatabase
     * @return
     */
    public static List<String> queryAllTables(SQLiteDatabase sqLiteDatabase) {
        final ArrayList<String> tableName = new ArrayList<>();

        if (sqLiteDatabase == null) {
            return tableName;
        }
        Cursor cursor = sqLiteDatabase.rawQuery("select name from sqlite_master where type='table' order by name", null);
        while (cursor.moveToNext()) {
            tableName.add(cursor.getString(0));
        }
        cursor.close();
        return tableName;
    }


    //获得数据库所有列内容
    public static List<String> queryTableColumnName(SQLiteDatabase sqLiteDatabase, String tableName) {
        if (tableName == null || sqLiteDatabase == null) {
            return null;
        }
        Cursor cursor = null;
        try {
            cursor = sqLiteDatabase.query(tableName, null, null, null, null, null, null);
            return new ArrayList<>(Arrays.asList(cursor.getColumnNames()));
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
    }

    //获得数据库属性
    public static List<List<String>> queryTableValue(SQLiteDatabase sqLiteDatabase, String tableName) {
        List<String> strings = queryTableColumnName(sqLiteDatabase, tableName);
        Cursor cursor = sqLiteDatabase.query(tableName, null, null, null, null, null, null);
        if (cursor == null) return null;
        int rowCount = cursor.getCount();
        try {
            final List<List<String>> result = new ArrayList<>();
            for (int y = 0; y < rowCount; y++) {
                final List<String> item = new ArrayList<>();
                result.add(item);
                if (cursor.moveToNext()) {
                    for (int x = 0; x < strings.size(); x++) {
                        String string = "";
                        try {
                            string = cursor.getString(x);
                        } catch (Exception e) {
                        }
                        item.add(string);
                    }
                }
            }
            return result;
        } finally {
            cursor.close();
        }
    }
}
