package com.twf.develophelpertools.util;

import android.app.Activity;

//import com.stone.cold.screenrecorder.fileexplorer.Matisse;
//import com.stone.cold.screenrecorder.fileexplorer.MimeType;
//import com.stone.cold.screenrecorder.fileexplorer.SelectionCreator;
//import com.stone.cold.screenrecorder.fileexplorer.engine.impl.GlideEngine;
//import com.stone.cold.screenrecorder.fileexplorer.internal.entity.CaptureStrategy;
import com.twf.develophelpertools.R;

/**
 * create by guofeng
 * date on 2021/5/11
 */

public class KitMediaSelectUtil {

    public static final int MAX_SELECTABLE = 4;

    private KitMediaSelectUtil() {
    }

    private static KitMediaSelectUtil instance = new KitMediaSelectUtil();


    public static KitMediaSelectUtil getInstance() {
        return instance;
    }


    public void openGallery(int requestCode) {

//        Activity topActivity = KitForegroundUtil.getInstance().getTopActivity();
//        if (topActivity == null) return;
//
//        Matisse from = Matisse.from(topActivity);
//        SelectionCreator choose = from.choose(MimeType.ofAll());
//        choose.countable(true);
//
//        choose.maxSelectable(MAX_SELECTABLE);
//        choose.originalEnable(true);
//        choose.setOnCheckedListener(null);
//        choose.showSingleMediaType(true);
//        choose.capture(true);
//        choose.captureStrategy(new CaptureStrategy(true, topActivity.getString(R.string.string_fileprovider_authority)));
//        choose.thumbnailScale(0.85f);
//        choose.imageEngine(new GlideEngine());
//        choose.forResult(requestCode);
    }
}