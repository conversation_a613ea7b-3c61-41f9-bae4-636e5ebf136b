package com.twf.develophelpertools.view;

import android.content.Context;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.MotionEvent;

/**
 * create by guofeng
 * date on 2021/3/15
 */

public class NoneInterceptTextView extends androidx.appcompat.widget.AppCompatTextView {


    public NoneInterceptTextView(Context context) {
        super(context);
        initTouch();
    }

    public NoneInterceptTextView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initTouch();
    }

    public NoneInterceptTextView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initTouch();
    }

    private void initTouch() {
        setOnTouchListener(null);
        setOnClickListener(null);
        setEnabled(false);
    }


    @Override
    public boolean onTouchEvent(MotionEvent event) {
        return false;
    }
}