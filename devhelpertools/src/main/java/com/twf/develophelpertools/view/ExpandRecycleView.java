package com.twf.develophelpertools.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.twf.develophelpertools.util.KitScaleUtil;

/**
 * create by guofeng
 * date on 2021/5/10
 */

public class ExpandRecycleView extends RecyclerView {


    public ExpandRecycleView(@NonNull Context context) {
        super(context);
    }

    public ExpandRecycleView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public ExpandRecycleView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    @Override
    protected void onMeasure(int widthSpec, int heightSpec) {
        super.onMeasure(widthSpec, heightSpec);

        Adapter adapter = getAdapter();
        if (adapter != null) {
            int itemCount = adapter.getItemCount();
            if (itemCount <= 0) return;
            int childCount = getChildCount();
            if (childCount <= 0) return;
            View child = getChildAt(0);

            int size = 0;
            ViewGroup.LayoutParams layoutParams = child.getLayoutParams();
            if (layoutParams != null) {
                size = layoutParams.height;
            }

            if (size <= 0) {
                int measureSpec = MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED);
                child.measure(measureSpec,measureSpec);
                size = child.getMeasuredHeight();
            }

            LayoutManager layoutManager = getLayoutManager();
            if (layoutManager instanceof GridLayoutManager) {
                GridLayoutManager gridLayoutManager = (GridLayoutManager) layoutManager;
                int spanCount = gridLayoutManager.getSpanCount();
                int lineCount = itemCount / spanCount;
                if (itemCount % spanCount > 0) {
                    lineCount++;
                }
                int dividerHeight = KitScaleUtil.dip2px(5);
                int tempHeightSpec = (size + dividerHeight) * lineCount;
                setMeasuredDimension(widthSpec, tempHeightSpec);
            }
        }
    }
}