package com.twf.develophelpertools.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.ContextThemeWrapper;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.twf.develophelpertools.R;

public class TreeNodeWrapperView extends LinearLayout {
    private LinearLayout nodeItemsContainer;
    private ViewGroup nodeContainer;
    private final int containerStyle;

    public TreeNodeWrapperView(Context context, int containerStyle) {
        super(context);
        this.containerStyle = containerStyle;
        this.init();
    }

    private void init() {
        this.setOrientation(LinearLayout.VERTICAL);
        this.nodeContainer = new RelativeLayout(this.getContext());
        this.nodeContainer.setLayoutParams(new LayoutParams(-1, -2));
        this.nodeContainer.setId(R.id.kit_node_header);
        ContextThemeWrapper newContext = new ContextThemeWrapper(this.getContext(), this.containerStyle);
        this.nodeItemsContainer = new LinearLayout(newContext, (AttributeSet)null, this.containerStyle);
        this.nodeItemsContainer.setLayoutParams(new LayoutParams(-1, -2));
        this.nodeItemsContainer.setId(R.id.kit_node_items);
        this.nodeItemsContainer.setOrientation(LinearLayout.VERTICAL);
        this.nodeItemsContainer.setVisibility(GONE);
        this.addView(this.nodeContainer);
        this.addView(this.nodeItemsContainer);
    }

    public void insertNodeView(View nodeView) {
        this.nodeContainer.addView(nodeView);
    }

    public ViewGroup getNodeContainer() {
        return this.nodeContainer;
    }
}
