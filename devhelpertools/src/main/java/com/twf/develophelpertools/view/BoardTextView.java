package com.twf.develophelpertools.view;

import android.content.Context;
import android.util.AttributeSet;
import android.util.TypedValue;

import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.content.ContextCompat;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.manager.UETool;
import com.twf.develophelpertools.util.KitScaleUtil;

public class BoardTextView extends AppCompatTextView {
    private final String defaultInfo = "Kit / " + UETool.getInstance().getTargetActivity().getClass().getName();
    private final int padding = KitScaleUtil.dip2px(3);

    public BoardTextView(Context context) {
        this(context, null);
    }

    public BoardTextView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BoardTextView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    private void initView() {
        setBackgroundColor(0x902395ff);
        setPadding(padding, padding, padding, padding);
        setTextColor(0xffffffff);
        setTextSize(TypedValue.COMPLEX_UNIT_DIP,12);
        setText(defaultInfo);
        setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, ContextCompat.getDrawable(getContext(), R.drawable.kit_close_icon), null);
        setCompoundDrawablePadding(KitScaleUtil.dip2px(2));
    }

    public void updateInfo(String info) {
        setText(info + "\n" + defaultInfo);
    }
}
