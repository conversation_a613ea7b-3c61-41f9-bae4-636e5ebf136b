package com.twf.develophelpertools.view;

import android.content.Context;
import android.os.Build;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.GridView;
import android.widget.ListAdapter;

import androidx.annotation.RequiresApi;

import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;

/**
 * create by guofeng
 * date on 2021/5/10
 */

public class DevMateExpandGridView extends GridView {
    public DevMateExpandGridView(Context context) {
        super(context);
    }

    public DevMateExpandGridView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public DevMateExpandGridView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }




    @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN)
    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        ListAdapter adapter = getAdapter();
        if (adapter != null) {
            final int count = adapter.getCount();

            if (count == 0) return;

            int itemHeight = getItemHeight();

            if (itemHeight == 0) return;

            int numColumns = getNumColumns();

            int verticalSpacing = getVerticalSpacing();

            int lineCount = count / numColumns;
            if (count % numColumns > 0) {
                lineCount++;
            }

            int tempHeight = ((itemHeight * lineCount) + (verticalSpacing * lineCount));

            setMeasuredDimension(widthMeasureSpec, tempHeight);
        }

    }

    private int itemSize;

    private int getItemHeight() {
        if (itemSize <= 0) {
            View child = LayoutInflater.from(Kit.getInstance().getApplication()).inflate(R.layout.kit_dev_mate_selected_item, null);
            int measureSpec = MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED);
            child.measure(measureSpec, measureSpec);
            itemSize = child.getMeasuredHeight();
        }

        return itemSize;
    }
}