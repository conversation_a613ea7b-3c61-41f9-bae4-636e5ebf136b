package com.twf.develophelpertools.view;

import android.app.Activity;
import android.app.Dialog;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ListView;
import android.widget.RadioButton;
import android.widget.TextView;
import android.widget.Toast;

import androidx.core.content.ContextCompat;

import com.kit.baselibrary.APiFilterBean;
import com.kit.baselibrary.APiFilterConfigBean;
import com.kit.baselibrary.AppCallBackItf;
//import com.stone.cold.screenrecorder.rain.base.utils.sp.SPUtils;
import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.util.KitMainHandler;
import com.twf.develophelpertools.util.KitSpUtil;

import java.util.List;

/**
 * create by guofeng
 * date on 2021/8/14
 */

public class ApiGroupFilterDialog {



    public static final String SP_API_GROUP_SELECT_INDEX = "SP_API_GROUP_SELECT_INDEX";
    private final Activity activity;


    private Dialog dialog;


    public ApiGroupFilterDialog(Activity activity) {
        this.activity = activity;
    }

    public void show(final OnFilterSelectCallBack callBack) {
        AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
        if (appCallBackItf == null) return;

        final APiFilterConfigBean configBean = appCallBackItf.getGroupApiFilterList();
        if (configBean == null) return;

        final List<APiFilterBean> groupApiFilterList = configBean.getaPiFilterBeanList();
        if (groupApiFilterList == null) return;

        View view = LayoutInflater.from(activity).inflate(R.layout.kit_api_group_dialog, null);
        ListView mListView = view.findViewById(R.id.mListView);


        mListView.setAdapter(new BaseAdapter() {
            @Override
            public int getCount() {
                return groupApiFilterList.size();
            }

            @Override
            public APiFilterBean getItem(int position) {
                return groupApiFilterList.get(position);
            }

            @Override
            public long getItemId(int position) {
                return position;
            }

            @Override
            public View getView(final int position, View convertView, ViewGroup parent) {

                GroupFilterViewHolder viewHolder;

                if (convertView == null) {
                    convertView = LayoutInflater.from(activity).inflate(R.layout.kit_group_filter_item, null);
                    viewHolder = new GroupFilterViewHolder(convertView);
                    convertView.setTag(viewHolder);
                } else {
                    viewHolder = (GroupFilterViewHolder) convertView.getTag();
                }

                final APiFilterBean aPiFilterBean = groupApiFilterList.get(position);


                if (getSelectIndex() == position) {
                    viewHolder.mContentView.setBackgroundColor(Color.parseColor("#0000FF"));
                } else {
                    viewHolder.mContentView.setBackgroundColor(Color.parseColor("#FFFFFF"));
                }


                viewHolder.mContentView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                        dialog.dismiss();

                        Toast.makeText(activity, "已选择" + aPiFilterBean.getGroupName(), Toast.LENGTH_SHORT).show();

                        setDefaultIndex(position);

                        KitMainHandler.getMainHandler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                if (callBack != null) {
                                    callBack.onFilterSelectListener();
                                }
                            }
                        }, 200);

                    }
                });

                viewHolder.mContentView.setText(aPiFilterBean.getGroupName());

                return convertView;
            }
        });
        dialog = new Dialog(activity);
        dialog.setContentView(view);
        dialog.show();
    }

    private static class GroupFilterViewHolder {


        private final TextView mContentView;

        public GroupFilterViewHolder(View convertView) {


            this.mContentView = convertView.findViewById(R.id.mContentView);

        }
    }

    public interface OnFilterSelectCallBack {

        void onFilterSelectListener();
    }


    //获得已经选中的筛选数据
    public static APiFilterBean getSelectFilterBean() {
        AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
        if (appCallBackItf != null) {
            APiFilterConfigBean groupApiFilterList = appCallBackItf.getGroupApiFilterList();
            List<APiFilterBean> aPiFilterBeans = groupApiFilterList.getaPiFilterBeanList();
            int defaultSelectIndex = getSelectIndex();
            if (aPiFilterBeans != null) {
                return aPiFilterBeans.get(defaultSelectIndex);
            }
        }
        return null;
    }


    //获得默认的筛选数据
    public static int getSelectIndex() {
        int defaultIndex = 0;
        AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
        if (appCallBackItf != null) {
            APiFilterConfigBean groupApiFilterList = appCallBackItf.getGroupApiFilterList();
            defaultIndex = groupApiFilterList.getDefaultSelectIndex();
        }
        return KitSpUtil.getInt(SP_API_GROUP_SELECT_INDEX, defaultIndex);
    }

    //设置默认index
    public static void setDefaultIndex(int index) {
        KitSpUtil.putInt(SP_API_GROUP_SELECT_INDEX, index);
    }
}