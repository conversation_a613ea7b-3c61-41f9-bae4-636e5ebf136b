package com.twf.develophelpertools.view;

import android.content.Context;
import android.graphics.Rect;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.facebook.drawee.view.SimpleDraweeView;
import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.bean.JiraImageBean;
import com.twf.develophelpertools.util.KitForegroundUtil;
import com.twf.develophelpertools.util.KitFrescoUtils;
import com.twf.develophelpertools.util.KitScaleUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * create by guofeng
 * date on 2021/5/8
 */

public class IssueItemImageLayout extends LinearLayout {


    private RecyclerView mRecycleView;

    public IssueItemImageLayout(Context context) {
        super(context);
        init();
    }


    public IssueItemImageLayout(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public IssueItemImageLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }


    private void init() {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.kit_jira_add_image_item, null);
        mRecycleView = view.findViewById(R.id.mRecycleView);
        GridLayoutManager gridLayoutManager = new GridLayoutManager(KitForegroundUtil.getInstance().getTopActivity(), 5);
        mRecycleView.setLayoutManager(gridLayoutManager);
        final int dividerHeight = KitScaleUtil.dip2px(5);
        mRecycleView.addItemDecoration(new RecyclerView.ItemDecoration() {

            @Override
            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                outRect.set(0, 0, dividerHeight, dividerHeight);
            }
        });
        addView(view);
    }



    public void setJiraImageBeanList(List<JiraImageBean> jiraImageBeanList) {
        AddImageAdapter addImageAdapter = new AddImageAdapter();
        addImageAdapter.setImageList(jiraImageBeanList);
        mRecycleView.setAdapter(addImageAdapter);
    }



    private static class AddImageAdapter extends RecyclerView.Adapter<AddImageViewHolder> {

        private final List<JiraImageBean> jiraImageBeanList = new ArrayList<>();


        public void setImageList(List<JiraImageBean> data) {
            jiraImageBeanList.clear();
            if (data != null) {
                jiraImageBeanList.addAll(data);
            }
        }


        @NonNull
        @Override
        public AddImageViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(Kit.getInstance().getApplication()).inflate(R.layout.kit_jira_item_imageview, null);
            return new AddImageViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull AddImageViewHolder holder, int position) {
            final JiraImageBean item = jiraImageBeanList.get(position);
            if (item != null) {


                try {
                    if (TextUtils.equals(item.getFileType(), JiraImageBean.IMAGE)) {
                        holder.mTextView.setText("图片");
                    } else {
                        holder.mTextView.setText("视频");
                    }

                    //加载本地图片
                    String localPath = item.getLocalPath();
                    if (localPath != null) {
                        File file = new File(localPath);
                        if (file.exists()) {
//                            FileInputStream fis = new FileInputStream(new File(localPath));
                            KitFrescoUtils.getInstance().loadLocalFile(holder.mImageView, localPath);
                            return;
                        }
                    }


                    //加载网络图片
                    String url = item.getUrl();
                    if (!TextUtils.isEmpty(url)) {
                        KitFrescoUtils.getInstance().loadUri(holder.mImageView, url);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }

        @Override
        public int getItemCount() {
            return jiraImageBeanList.size();
        }
    }


    public static class AddImageViewHolder extends RecyclerView.ViewHolder {

        private SimpleDraweeView mImageView;

        private TextView mTextView;

        public AddImageViewHolder(@NonNull View itemView) {
            super(itemView);
            mImageView = itemView.findViewById(R.id.mImageView);
            mTextView = itemView.findViewById(R.id.mTextView);
        }
    }


}