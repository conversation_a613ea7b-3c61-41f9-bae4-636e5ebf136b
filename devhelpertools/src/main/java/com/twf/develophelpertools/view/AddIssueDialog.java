package com.twf.develophelpertools.view;

import android.app.Activity;
import android.app.Dialog;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ListView;
import android.widget.TextView;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.activity.TransferCenterKitActivity;
import com.twf.develophelpertools.adapter.AddIssueAdapter;
import com.twf.develophelpertools.bean.JiraImageBean;
import com.twf.develophelpertools.bean.JiraItemBean;
import com.twf.develophelpertools.model.fragment.IssueCreateTaskFragment;
import com.twf.develophelpertools.util.KitForegroundUtil;
import com.twf.develophelpertools.util.IssueSPManager;

import java.util.List;

/**
 * create by guofeng
 * date on 2021/5/17
 */

public class AddIssueDialog {


    private JiraImageBean jiraImageBean;
    private Dialog dialog;

    public void setJiraImageBean(JiraImageBean jiraImageBean) {
        this.jiraImageBean = jiraImageBean;
    }

    public void show() {

        final Activity topActivity = KitForegroundUtil.getInstance().getTopActivity();

        if (topActivity == null) {
            return;
        }
        dialog = new Dialog(topActivity);

        View view = LayoutInflater.from(topActivity).inflate(R.layout.kit_add_issue_dialog, null);
        View mCloseView = view.findViewById(R.id.mCloseView);
        mCloseView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss(topActivity);
            }
        });

        ListView mListView = view.findViewById(R.id.mListView);
        TextView mCreateView = view.findViewById(R.id.mCreateView);
        mCreateView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss(topActivity);
                TransferCenterKitActivity.jump(topActivity, IssueCreateTaskFragment.class);
            }
        });
        refreshAdapter(mListView,topActivity);
        dialog.setContentView(view);
        dialog.show();
    }

    private void dismiss(Activity activity) {
        if (activity != null) {
            activity.finish();
        }
        dialog.dismiss();
    }


    //刷新适配器
    private void refreshAdapter(ListView mListView,final Activity topActivity) {

        final List<JiraItemBean> allJiraList = IssueSPManager.getInstance().getIssueJiraList();

        final AddIssueAdapter addIssueAdapter = new AddIssueAdapter();

        addIssueAdapter.setCallBack(new AddIssueAdapter.OnItemCheckCallBack() {
            @Override
            public void onCheckListener(JiraItemBean itemBean, int position) {
                //保存数据到SP里面
                JiraItemBean jiraItemBean = allJiraList.get(position);
                if (jiraItemBean != null) {
                    List<JiraImageBean> imageList = jiraItemBean.getImageList();
                    imageList.add(0, jiraImageBean);
                }
                IssueSPManager.getInstance().saveAllIssueList(allJiraList);
                if (dialog != null) {
                    dismiss(topActivity);
                }
            }
        });
        addIssueAdapter.setData(allJiraList);
        mListView.setAdapter(addIssueAdapter);
    }


}