package com.twf.develophelpertools.crash;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.twf.develophelpertools.util.KitDateUtil;
import com.twf.develophelpertools.util.KitFileUtil;

import java.io.File;
import java.io.FileOutputStream;
import java.io.PrintStream;

public class <PERSON>UncaughtHandler implements Thread.UncaughtExceptionHandler {

    @Nullable
    private final Thread.UncaughtExceptionHandler ueh;

    public KitUncaughtHandler() {
        this.ueh = Thread.getDefaultUncaughtExceptionHandler();
    }

    @Override
    public void uncaughtException(@NonNull Thread t, @NonNull Throwable e) {
        PrintStream printStream = null;
        try {
            File crashRootFile = KitFileUtil.getCrashRootFile();
            String date = KitDateUtil.getYearMonthDay();
            String time = KitDateUtil.getHourMinuteSecond();
            String fileName = date + " " + time;
            String absPath = crashRootFile + "/" + fileName;
            printStream = new PrintStream(new FileOutputStream(absPath));
            e.printStackTrace(printStream);
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        } finally {
            if (printStream != null) {
                printStream.close();
            }

            if (ueh != null) {
                ueh.uncaughtException(t, e);
            }
        }
    }

}
