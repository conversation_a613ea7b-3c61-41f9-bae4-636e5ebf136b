package com.twf.develophelpertools.constant;

import android.content.Context;

import com.twf.develophelpertools.util.KitSpUtil;

/**
 * Created by wa<PERSON><PERSON><PERSON> on 2018/12/14.
 */

public class FloatIconConfig {

    public static int getLastPosX(Context context) {
        return KitSpUtil.getInt(SharedPrefsKey.FLOAT_ICON_POS_X, 0);
    }

    public static int getLastPosY(Context context) {
        return KitSpUtil.getInt(SharedPrefsKey.FLOAT_ICON_POS_Y, 0);
    }

    public static void saveLastPosY(Context context, int val) {
        KitSpUtil.putInt(SharedPrefsKey.FLOAT_ICON_POS_Y, val);
    }

    public static void saveLastPosX(Context context, int val) {
        KitSpUtil.putInt(SharedPrefsKey.FLOAT_ICON_POS_X, val);
    }
}
