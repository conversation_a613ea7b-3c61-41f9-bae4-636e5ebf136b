package com.twf.develophelpertools;

import android.app.Activity;
import android.app.Application;
import android.content.Intent;

import com.kit.baselibrary.AppCallBackItf;
import com.kit.baselibrary.KitItf;
//import com.stone.cold.screenrecorder.rain.base.base.BaseApplicationUtils;
import com.twf.develophelpertools.adapter.analy.BgAnalyseIterceptor;
import com.twf.develophelpertools.chunk.ChuckInterceptor;
import com.twf.develophelpertools.longterm.LongTermTouchEventWatcher;
import com.twf.develophelpertools.model.fragment.CrashMainFragment;
import com.twf.develophelpertools.model.mock.DevelopMockUtil;
import com.twf.develophelpertools.model.mock.QAMockInterceptor;
import com.twf.develophelpertools.model.pickcolor.FloatPageManager;
import com.twf.develophelpertools.model.weakconnect.WeakConnectInterceptor;
import com.twf.develophelpertools.shake.callback.ActivityCallbacks;
import com.twf.develophelpertools.util.ActivityInfoUtil;
import com.twf.develophelpertools.util.AdvertiseJumpUtil;
import com.twf.develophelpertools.util.FloatIconUtil;
//import com.twf.develophelpertools.util.KitBroadCastUtil;
import com.twf.develophelpertools.util.KitForegroundUtil;
import com.twf.develophelpertools.util.KitFrescoUtils;
import com.twf.develophelpertools.util.KitLocationUtil;

import okhttp3.Interceptor;


/**
 * create by guofeng
 * date on 2021/7/27
 */

public class Kit implements KitItf {

    private static final Kit instance = new Kit();

    private Kit() {
    }

    public static Kit getInstance() {
        return instance;
    }


    private Application application;

    public Application getApplication() {
        return application;
    }


//    private final KitBroadCastUtil kitBroadCastUtil = new KitBroadCastUtil();


    private AppCallBackItf appCallBackItf;

    public AppCallBackItf getAppCallBackItf() {
        return appCallBackItf;
    }


    @Override
    public void openLeakCanary() {
        Activity topActivity = KitForegroundUtil.getInstance().getTopActivity();
        Intent intent = new Intent();
        intent.setClassName(topActivity.getPackageName(), "leakcanary.internal.activity.LeakActivity");
        topActivity.startActivity(intent);
    }

    @Override
    public void install(Application application, com.kit.baselibrary.AppCallBackItf appCallBackItf) {
        this.application = application;
        this.appCallBackItf = appCallBackItf;
        //注册摇一摇到
        application.registerActivityLifecycleCallbacks(new ActivityCallbacks(getApplication()));
        //初始化跳过广告
        new AdvertiseJumpUtil().setJumpAdvertiseListener(application);
        //初始化WindowManager
        FloatPageManager.getInstance().init(application);
        //是否开启收集日志奔溃
        CrashMainFragment.checkCrashHandler();
        //注册监控生命周期
        FloatIconUtil.getInstance().registerMainLifeCircle();
        //注册页面监控
        KitForegroundUtil.getInstance().register(application);
        //初始化MOCK
        DevelopMockUtil.getInstance().initMock();
        //录屏启动
//        BaseApplicationUtils.onCreate(application);
        //启动加载图片
//        KitFrescoUtils.getInstance().init(application);
        //显示app信息
        new ActivityInfoUtil().register(application);
        //注册广播
//        kitBroadCastUtil.registerBroadCast(application);
        // 注册长文本模拟
        new LongTermTouchEventWatcher().init(application);
    }

    ///////////////////////////////////////////////////////////////////////////
    // 以下是业务层调用的代码
    ///////////////////////////////////////////////////////////////////////////
    @Override
    public Interceptor getWeakConnectInterceptor() {
        return new WeakConnectInterceptor();
    }


//    @Override
//    public Interceptor getQaMockInterceptor() {
//        return new QAMockInterceptor();
//    }
//
//    @Override
//    public Interceptor getChuckInterceptor() {
//        return new ChuckInterceptor(Kit.getInstance().getApplication());
//    }

    @Override
    public Interceptor getAnalyInterceptor() {
        return new BgAnalyseIterceptor();
    }

    @Override
    public Interceptor getReportInterceptor() {
        return new ReportInterceptor();
    }

    @Override
    public void inflateMockLocation(Class<?> tClass, Object object) {
        new KitLocationUtil().inflateMockLocation(tClass, object);
    }

}