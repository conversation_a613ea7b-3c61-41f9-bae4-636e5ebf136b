package com.twf.develophelpertools.thread;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Created by guo<PERSON>
 * on 2019/6/6.
 */

public class KitThreadPool {

    private static ExecutorService executorService;

    private static ExecutorService getThreadPools() {
        if (executorService == null) {
            executorService = Executors.newCachedThreadPool();
        }
        return executorService;
    }

    public static void executor(Runnable runnable) {
        getThreadPools().execute(runnable);
    }


}
