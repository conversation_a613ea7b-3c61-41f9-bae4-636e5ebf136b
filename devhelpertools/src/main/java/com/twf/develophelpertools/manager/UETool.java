package com.twf.develophelpertools.manager;

import android.app.Activity;

import com.twf.develophelpertools.binder.AddMinusEditTextItemBinder;
import com.twf.develophelpertools.binder.BitmapItemBinder;
import com.twf.develophelpertools.binder.BriefDescItemBinder;
import com.twf.develophelpertools.binder.EditTextItemBinder;
import com.twf.develophelpertools.binder.SwitchItemBinder;
import com.twf.develophelpertools.binder.TextItemBinder;
import com.twf.develophelpertools.binder.TitleItemBinder;
import com.twf.develophelpertools.chunk.internal.ui.MainActivity;
import com.twf.develophelpertools.item.AddMinusEditItem;
import com.twf.develophelpertools.item.AttrsDialogMultiTypePool;
import com.twf.develophelpertools.item.BitmapItem;
import com.twf.develophelpertools.item.BriefDescItem;
import com.twf.develophelpertools.item.EditTextItem;
import com.twf.develophelpertools.item.Item;
import com.twf.develophelpertools.item.ItemViewBinder;
import com.twf.develophelpertools.item.SwitchItem;
import com.twf.develophelpertools.item.TextItem;
import com.twf.develophelpertools.item.TitleItem;
import com.twf.develophelpertools.item.UETCore;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class UETool {
    private static volatile UETool instance;
    private Set<String> filterClassesSet = new HashSet<>();
    private Activity targetActivity;
    public static UETool getInstance() {
        if (instance == null) {
            synchronized (UETool.class) {
                if (instance == null) {
                    instance = new UETool();
                }
            }
        }
        return instance;
    }

    private UETool() {
        initAttrsDialogMultiTypePool();
    }

    public Activity getTargetActivity() {
        return targetActivity;
    }


    public void setTargetActivity(Activity targetActivity) {
        this.targetActivity = targetActivity;
    }

    public void release() {
        targetActivity = null;
    }


    private void putFilterClassName(String className) {
        filterClassesSet.add(className);
    }

    public Set<String> getFilterClasses() {
        return filterClassesSet;
    }

    public List<String> getAttrsProvider() {
        return attrsProviderSet;
    }

    private void putAttrsProviderClassName(String className) {
        attrsProviderSet.add(0, className);
    }

    private List<String> attrsProviderSet = new ArrayList<String>(){
        {
            add(UETCore.class.getName());
            add("com.twf.develophelpertools.item.UETFresco");
        }
    };

    private AttrsDialogMultiTypePool attrsDialogMultiTypePool = new AttrsDialogMultiTypePool();


    private void initAttrsDialogMultiTypePool() {
        attrsDialogMultiTypePool.register(AddMinusEditItem.class, new AddMinusEditTextItemBinder());
        attrsDialogMultiTypePool.register(BitmapItem.class, new BitmapItemBinder());
        attrsDialogMultiTypePool.register(BriefDescItem.class, new BriefDescItemBinder());
        attrsDialogMultiTypePool.register(EditTextItem.class, new EditTextItemBinder());
        attrsDialogMultiTypePool.register(SwitchItem.class, new SwitchItemBinder());
        attrsDialogMultiTypePool.register(TextItem.class, new TextItemBinder());
        attrsDialogMultiTypePool.register(TitleItem.class, new TitleItemBinder());
    }

    public static <T extends Item> void registerAttrDialogItemViewBinder(Class<T> clazz, ItemViewBinder<T, ?> binder) {
        getInstance().attrsDialogMultiTypePool.register(clazz, binder);
    }

    public AttrsDialogMultiTypePool getAttrsDialogMultiTypePool() {
        return attrsDialogMultiTypePool;
    }

}
