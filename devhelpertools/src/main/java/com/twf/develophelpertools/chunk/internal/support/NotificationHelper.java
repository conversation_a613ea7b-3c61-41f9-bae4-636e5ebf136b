/*
 * Copyright (C) 2017 <PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.twf.develophelpertools.chunk.internal.support;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.core.app.NotificationCompat;
import androidx.core.content.ContextCompat;
import androidx.collection.LongSparseArray;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.chunk.Chuck;
import com.twf.develophelpertools.chunk.internal.data.HttpTransaction;
import com.twf.develophelpertools.chunk.internal.ui.BaseChuckActivity;

import java.lang.reflect.Method;

@Keep
public class NotificationHelper {

    private static final String CHANNEL_ID = "chuck";
    private static final int NOTIFICATION_ID = 1138;
    private static final int BUFFER_SIZE = 10;

    private static final LongSparseArray<HttpTransaction> transactionBuffer = new LongSparseArray<>();
    private static int transactionCount;

    private final Context context;
    private final NotificationManager notificationManager;
    private Method setChannelId;

    public static synchronized void clearBuffer() {
        transactionBuffer.clear();
        transactionCount = 0;
    }

    private static synchronized void addToBuffer(HttpTransaction transaction) {
        if (transaction.getStatus() == HttpTransaction.Status.Requested) {
            transactionCount++;
        }
        transactionBuffer.put(transaction.getId(), transaction);
        if (transactionBuffer.size() > BUFFER_SIZE) {
            transactionBuffer.removeAt(0);
        }
    }

    public NotificationHelper(Context context) {
        this.context = context;
        notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        if (notificationManager == null) return;

        String channelId = "boss_channel_id";
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(channelId, "BOSS", NotificationManager.IMPORTANCE_LOW);
            notificationManager.createNotificationChannel(channel);
        }
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
//            notificationManager.
//            notificationManager.createNotificationChannel(
//                    new NotificationChannel(CHANNEL_ID,
//                            context.getString(R.string.notification_category), NotificationManager.IMPORTANCE_LOW));
//            try {
//                setChannelId = NotificationCompat.Builder.class.getMethod("setChannelId", String.class);
//            } catch (Exception ignored) {}
//        }
    }

    public synchronized void show(HttpTransaction transaction) {
        try {
            addToBuffer(transaction);
            if (!BaseChuckActivity.isInForeground()) {
                NotificationCompat.Builder builder = new NotificationCompat.Builder(context, "boss_channel_id")


                        .setContentIntent(PendingIntent.getActivity(context, 0, Chuck.getLaunchIntent(context),
                                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M ?
                                        (PendingIntent.FLAG_IMMUTABLE | PendingIntent.FLAG_UPDATE_CURRENT) :
                                        PendingIntent.FLAG_UPDATE_CURRENT))

                        .setLocalOnly(true)
                        .setOngoing(true)
                        .setAutoCancel(false)
                        .setSmallIcon(R.drawable.kit_chuck_ic_notification_white_24dp)
                        .setColor(Color.parseColor("#00BCD4"))
                        .setContentTitle(context.getString(R.string.kit_app_name));
                NotificationCompat.InboxStyle inboxStyle = new NotificationCompat.InboxStyle();
                if (setChannelId != null) {
                    try {
                        setChannelId.invoke(builder, CHANNEL_ID);
                    } catch (Exception ignored) {
                    }
                }
                int count = 0;
                for (int i = transactionBuffer.size() - 1; i >= 0; i--) {
                    if (count < BUFFER_SIZE) {
                        //https://bugly.qq.com/v2/crash-reporting/crashes/65d92ea8a7/2042580?pid=1
                        HttpTransaction httpTransaction = transactionBuffer.valueAt(i);
                        if (httpTransaction == null) continue;
                        if (count == 0) {
                            builder.setContentText(httpTransaction.getNotificationText());
                        }
                        inboxStyle.addLine(httpTransaction.getNotificationText());
                    }
                    count++;
                }
                builder.setAutoCancel(false);
                builder.setStyle(inboxStyle);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    builder.setSubText(String.valueOf(transactionCount));
                } else {
                    builder.setNumber(transactionCount);
                }
                // builder.addAction(getClearAction());
                notificationManager.notify(NOTIFICATION_ID, builder.build());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @NonNull
    private NotificationCompat.Action getClearAction() {
        CharSequence clearTitle = context.getString(R.string.kit_chuck_clear);
        Intent deleteIntent = new Intent(context, ClearTransactionsService.class);
        PendingIntent intent = PendingIntent.getService(context, 11, deleteIntent, PendingIntent.FLAG_ONE_SHOT);
        return new NotificationCompat.Action(R.drawable.kit_chuck_ic_delete_white_24dp,
                clearTitle, intent);
    }

    public void dismiss() {
//        notificationManager.cancel(NOTIFICATION_ID);
    }
}
