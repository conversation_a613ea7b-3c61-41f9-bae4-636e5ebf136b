package com.twf.develophelpertools.chunk.internal.view;

import android.content.Context;
import android.graphics.Color;

import androidx.annotation.Keep;
import androidx.annotation.Nullable;

import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.google.gson.JsonArray;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.util.KitClipBoardUtil;
import com.twf.develophelpertools.util.KitCopyDialogUtil;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import static com.twf.develophelpertools.chunk.internal.ui.TransactionPayloadFragment.bodyText;

/**
 * Created by guofeng
 * on 2019/7/2.
 */
@Keep
public class ExpandCombineLayout extends LinearLayout {

    private static final String NEXT_LINE = "\n";
    private static LayoutParams params;
    private ExpandCombineTextView mStartTagView;
    private TextView mEndTagView;
    private LinearLayout mContent;

    public ExpandCombineLayout(Context context) {
        super(context);
        initView();
    }

    public ExpandCombineLayout(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }


    public ExpandCombineLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    private static LayoutParams params() {
        if (params == null) {
            params = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            params.leftMargin = 30;
        }
        return params;
    }

    //初始化所有View
    public static void invalidateView(final Context context, String json, ExpandCombineLayout rootView, boolean isInit) {
        if (TextUtils.isEmpty(json)) return;
        try {
            if (isInit) {
                rootView.setStartTagText("{" + NEXT_LINE);
            }

            JSONObject jsonObject = new JSONObject(json);
            JSONArray names = jsonObject.names();
            if (names != null) {
                for (int i = 0; i < names.length(); i++) {
                    String key = names.getString(i);
                    Object opt = jsonObject.opt(key);
                    if (opt == null) continue;
                    //解析JSONObject
                    if (opt instanceof JSONObject) {
                        optJsonObject(context, rootView, key, (JSONObject) opt);
                        continue;
                    }
                    //解析JSONArray
                    if (opt instanceof JSONArray) {
                        optJsonArray(context, rootView, key, (JSONArray) opt);
                        continue;
                    }
                    //添加内容
                    final TextView textView = new TextView(context);
                    textView.setText(createKeyHighLight("\"" + key + "\"" + ":", "\"" + opt.toString() + "\"" + NEXT_LINE));
                    rootView.mContent.addView(textView, params());
                    textView.setOnLongClickListener(new OnLongClickListener() {
                        @Override
                        public boolean onLongClick(View v) {

                            KitCopyDialogUtil kitCopyDialogUtil = new KitCopyDialogUtil(context);
                            kitCopyDialogUtil.setOnCopyCallBack(new KitCopyDialogUtil.OnCopyCallBack() {
                                @Override
                                public void onCopyAllListener() {
                                    KitClipBoardUtil.copyToClipBoard(bodyText);
                                    Toast.makeText(context, "复制成功", Toast.LENGTH_LONG).show();
                                    Log.d(TAG, "onCopyAllListener: " + bodyText);
                                }

                                @Override
                                public void onCopyLocalListener() {
                                    copyLocalListener(context, textView, null, null);
                                }
                            });
                            kitCopyDialogUtil.show();

                            return false;
                        }
                    });
                }
            }

            if (isInit) {
                rootView.setEndTagText("}" + NEXT_LINE);
                rootView.onExpand();
            }
        } catch (JSONException e) {
            e.printStackTrace();
            rootView.clearAllContent();
            rootView.setStartTagText(json);
        }
    }


    //初始化所有View
    public static void invalidateView2(final Context context, String json, ExpandCombineLayout rootView, boolean isInit) {

        if (TextUtils.isEmpty(json)) return;

        if (TextUtils.isEmpty("{}")) return;

        if (json.startsWith("[") && json.endsWith("]")) {
            try {
                optJsonArray(context, rootView, "", new JSONArray(json));
            } catch (JSONException e) {
                e.printStackTrace();
                rootView.clearAllContent();
                rootView.setStartTagText(json);
            }
        } else if (json.startsWith("{") && json.endsWith("}")) {
            invalidateView(context, json, rootView, isInit);
        } else {
            rootView.clearAllContent();
            rootView.setStartTagText(json);
        }

    }

    //渲染key:Value 时刻,key字段高亮
    private static SpannableString createKeyHighLight(String key, String appendText) {
        final SpannableString spannableString = new SpannableString(key + appendText);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#F44336")), 0, key.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    //解析JsonArray
    private static void optJsonArray(Context context, ExpandCombineLayout rootView, String key, JSONArray jsonArray) {
        ExpandCombineLayout subItems = new ExpandCombineLayout(context);
        rootView.mContent.addView(subItems, params());
        subItems.setStartTagText("\"" + key + "\"" + ":[" + NEXT_LINE);
        for (int j = 0; j < jsonArray.length(); j++) {
            ExpandCombineLayout ssItem = new ExpandCombineLayout(context);
            subItems.mContent.addView(ssItem, params());
            Object opt1 = jsonArray.opt(j);
            if (opt1 instanceof JSONObject) {
                ssItem.setStartTagText("{" + NEXT_LINE);
                JSONObject jsonObject1 = (JSONObject) opt1;
                invalidateView(context, jsonObject1.toString(), ssItem, false);
                ssItem.setEndTagText("}," + NEXT_LINE);
                continue;
            }
            //转化为String
            ssItem.hideTags();
            TextView textView = new TextView(context);
            textView.setText(opt1.toString() + "," + NEXT_LINE);
            ssItem.mContent.addView(textView);
        }
        subItems.setEndTagText("]" + NEXT_LINE);
    }

    //解析JsonObject
    private static void optJsonObject(Context context, ExpandCombineLayout rootView, String key, JSONObject jsonObject1) {
        if (jsonObject1.names() == null) {
            TextView textView = new TextView(context);
            textView.setText(createKeyHighLight("\"" + key + "\"" + ":", "\"" + jsonObject1.toString() + "\"" + NEXT_LINE));
            rootView.mContent.addView(textView, params());
        } else {
            ExpandCombineLayout subItems = new ExpandCombineLayout(context);
            rootView.mContent.addView(subItems, params());
            subItems.setStartTagText("\"" + key + "\"" + ":" + "{" + NEXT_LINE);
            invalidateView(context, jsonObject1.toString(), subItems, false);
            subItems.setEndTagText("}" + NEXT_LINE);
        }
    }

    private void initView() {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.kit_chuck_expand_combine_view, this, false);
        mEndTagView = view.findViewById(R.id.mEndTagView);
        mContent = view.findViewById(R.id.mContent);
        mStartTagView = view.findViewById(R.id.mStartTagView);
        mStartTagView.initView(mContent, mEndTagView);
        addView(view);
    }

    //设置起始tag内容
    public void setStartTagText(String text) {
        mStartTagView.setText(text);
        mStartTagView.init();
        mStartTagView.setOnLongClickListener(new OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                KitCopyDialogUtil kitCopyDialogUtil = new KitCopyDialogUtil(getContext());
                kitCopyDialogUtil.setOnCopyCallBack(new KitCopyDialogUtil.OnCopyCallBack() {
                    @Override
                    public void onCopyAllListener() {
                        KitClipBoardUtil.copyToClipBoard(bodyText);
                        Toast.makeText(getContext(), "复制成功", Toast.LENGTH_LONG).show();
                        Log.d(TAG, "onCopyAllListener: " + bodyText);
                    }

                    @Override
                    public void onCopyLocalListener() {
                        copyLocalListener(getContext(), mStartTagView, mContent, mEndTagView);
                    }
                });
                kitCopyDialogUtil.show();
                return false;
            }
        });
    }

    private static void copyLocalListener(Context context, TextView mStartTagView, LinearLayout mContent, TextView mEndTagView) {
        String startText = mStartTagView.getText().toString();
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(startText);

        listAllChild(mContent, stringBuilder);

        if (mEndTagView != null) {
            stringBuilder.append(mEndTagView.getText().toString());
        }

        String value = stringBuilder.toString();
        KitClipBoardUtil.copyToClipBoard(value);
        Toast.makeText(context, "复制成功", Toast.LENGTH_LONG).show();

        Log.d(TAG, "copyLocalListener: " + value);
    }

    private static void listAllChild(ViewGroup viewGroup, StringBuilder stringBuilder) {
        if (viewGroup != null) {
            int childCount = viewGroup.getChildCount();
            for (int i = 0; i < childCount; i++) {
                View childAt = viewGroup.getChildAt(i);
                if (childAt instanceof ViewGroup) {
                    listAllChild((ViewGroup) childAt, stringBuilder);
                    continue;
                }
                if (childAt instanceof TextView) {
                    TextView mTextView = (TextView) childAt;
                    stringBuilder.append(mTextView.getText().toString());
                }
            }
        }
    }

    //隐藏 首尾TAG
    public void hideTags() {
        mStartTagView.setVisibility(GONE);
        mEndTagView.setVisibility(GONE);
    }


    //设置尾部内容
    public void setEndTagText(String text) {
        mEndTagView.setText(text);
    }

    public void onExpand() {
        mStartTagView.onExpand();
    }


    //清除所有内容
    public void clearAllContent() {
        mContent.removeAllViews();
    }


    @Override
    protected void finalize() throws Throwable {
        super.finalize();
        Log.d(TAG, "finalize:============== ");
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        Log.d(TAG, "onDetachedFromWindow: ======");
    }

    private static final String TAG = "ExpandCombineLayout";
}
