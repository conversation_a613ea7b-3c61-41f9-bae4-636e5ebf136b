/*
 * Copyright (C) 2017 <PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.twf.develophelpertools.chunk.internal.ui;

import android.app.ProgressDialog;
import android.content.Context;
import android.database.Cursor;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Keep;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SearchView;
import androidx.fragment.app.Fragment;
import androidx.loader.app.LoaderManager;
import androidx.loader.content.CursorLoader;
import androidx.loader.content.Loader;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.kit.baselibrary.APiFilterBean;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.chunk.internal.data.ChuckContentProvider;
import com.twf.develophelpertools.chunk.internal.data.HttpTransaction;
import com.twf.develophelpertools.chunk.internal.listener.OnListFragmentInteractionListener;
import com.twf.develophelpertools.chunk.internal.support.NotificationHelper;
import com.twf.develophelpertools.view.ApiGroupFilterDialog;

import java.util.ArrayList;
import java.util.List;


@Keep
public class TransactionListFragment extends Fragment implements
        SearchView.OnQueryTextListener, LoaderManager.LoaderCallbacks<Cursor> {

    private String currentFilter;
    private OnListFragmentInteractionListener listener;
    private TransactionAdapter adapter;

    public TransactionListFragment() {
    }

    public static TransactionListFragment newInstance() {
        return new TransactionListFragment();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setHasOptionsMenu(true);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.kit_chuck_fragment_transaction_list, container, false);

        RecyclerView recyclerView = view.findViewById(R.id.list);
        recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        recyclerView.addItemDecoration(new DividerItemDecoration(getActivity(),
                DividerItemDecoration.VERTICAL));
        adapter = new TransactionAdapter(getContext(), listener);
        recyclerView.setAdapter(adapter);
        return view;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        getLoaderManager().initLoader(0, null, this);
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        if (context instanceof OnListFragmentInteractionListener) {
            listener = (OnListFragmentInteractionListener) context;
        } else {
            throw new RuntimeException(context.toString()
                    + " must implement OnListFragmentInteractionListener");
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        listener = null;
    }

    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        inflater.inflate(R.menu.kit_chuck_main, menu);
        MenuItem searchMenuItem = menu.findItem(R.id.search);
        SearchView searchView = (SearchView) searchMenuItem.getActionView();
        searchView.setOnQueryTextListener(this);
        searchView.setIconifiedByDefault(true);
        super.onCreateOptionsMenu(menu, inflater);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {

        if (item.getItemId() == R.id.clear) {
            getContext().getContentResolver().delete(ChuckContentProvider.TRANSACTION_URI, null, null);
            NotificationHelper.clearBuffer();
            return true;
        } else if (item.getItemId() == R.id.browse_sql) {

            ApiGroupFilterDialog apiGroupFilterDialog = new ApiGroupFilterDialog(getActivity());
            apiGroupFilterDialog.show(new ApiGroupFilterDialog.OnFilterSelectCallBack() {
                @Override
                public void onFilterSelectListener() {
                    showProgress = true;
                    getLoaderManager().restartLoader(0, null, TransactionListFragment.this);
                }
            });

            return true;
        } else {
            return super.onOptionsItemSelected(item);
        }
    }


    private boolean showProgress;

    private ProgressDialog progressDialog;


    private ProgressDialog getProgressDialog() {
        if (progressDialog == null) {
            progressDialog = new ProgressDialog(getContext());
        }
        return progressDialog;
    }

    @Override
    public Loader<Cursor> onCreateLoader(int id, Bundle args) {

        if (showProgress) {
            getProgressDialog().show();
            showProgress = false;
        }

        CursorLoader loader = new CursorLoader(getContext());
        loader.setUri(ChuckContentProvider.TRANSACTION_URI);

        final List<String> argues = new ArrayList<>();
        final StringBuilder sql = new StringBuilder();


        if (!TextUtils.isEmpty(currentFilter)) {
            argues.add("%" + currentFilter + "%");
            sql.append(" path LIKE ? ");
        }

        APiFilterBean selectFilterBean = ApiGroupFilterDialog.getSelectFilterBean();

        if (selectFilterBean != null) {

            List<APiFilterBean.APiUrlBean> aPiUrlBeans = selectFilterBean.getaPiUrlList();

            if (aPiUrlBeans != null) {

                for (APiFilterBean.APiUrlBean aPiUrlBean : aPiUrlBeans) {

                    if (aPiUrlBean == null) continue;


                    String likeUrl = aPiUrlBean.getLikeUrl();
                    String unLikeUrl = aPiUrlBean.getUnLikeUrl();

                    if (!TextUtils.isEmpty(likeUrl)) {

                        argues.add("%" + likeUrl + "%");

                        if (sql.length() > 0) {
                            sql.append(" and ");
                        }

                        sql.append(" path  LIKE ? ");

                    } else if (!TextUtils.isEmpty(unLikeUrl)) {
                        argues.add("%" + unLikeUrl + "%");

                        if (sql.length() > 0) {
                            sql.append(" and ");
                        }

                        sql.append(" path  NOT LIKE ? ");

                    }

                }
            }
        }


        if (sql.length() > 0) {
            loader.setSelection(sql.toString());
        }


        if (!argues.isEmpty()) {
            String[] arrays = new String[argues.size()];

            for (int i = 0; i < argues.size(); i++) {
                String value = argues.get(i);
                arrays[i] = value;
            }

            Log.d(TAG, "onCreateLoader: " + arrays.toString());

            loader.setSelectionArgs(arrays);
        }

        Log.d(TAG, "onCreateLoader: " + sql.toString());

        loader.setProjection(HttpTransaction.PARTIAL_PROJECTION);
        loader.setSortOrder("requestDate DESC");
        return loader;
    }

    private static final String TAG = "TransactionListFragment";



    @Override
    public void onLoadFinished(Loader<Cursor> loader, Cursor data) {
        adapter.swapCursor(data);
        getProgressDialog().dismiss();
    }

    @Override
    public void onLoaderReset(Loader<Cursor> loader) {
        adapter.swapCursor(null);
    }

    @Override
    public boolean onQueryTextSubmit(String query) {
        return true;
    }

    @Override
    public boolean onQueryTextChange(String newText) {
        currentFilter = newText;
        adapter.setFilterValue(currentFilter);
        getLoaderManager().restartLoader(0, null, this);
        return true;
    }
}
