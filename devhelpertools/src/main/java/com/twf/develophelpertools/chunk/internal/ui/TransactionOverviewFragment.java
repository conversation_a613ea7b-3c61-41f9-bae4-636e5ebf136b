/*
 * Copyright (C) 2017 <PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.twf.develophelpertools.chunk.internal.ui;

import android.graphics.Color;
import android.os.Bundle;

import androidx.annotation.Keep;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.core.content.ContextCompat;

import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.kit.baselibrary.AppCallBackItf;
import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.chunk.internal.data.HttpTransaction;

import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;

@Keep
public class TransactionOverviewFragment extends Fragment implements TransactionFragment {

    TextView url;
    TextView method;
    //过滤 网络请求 基础 参数
    private final List<String> commonList = new ArrayList<String>() {{
        add("curidentity");
        add("v");
        add("app_id");
        add("uniqid");
        add("req_time");
        add("client_info");
        add("t");
        add("sig");
    }};
    TextView protocol;
    TextView status;
    TextView response;
    TextView ssl;
    TextView requestTime;
    TextView responseTime;
    TextView duration;
    TextView requestSize;
    TextView responseSize;
    TextView totalSize;

    private HttpTransaction transaction;
    TextView params;

    public TransactionOverviewFragment() {
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setRetainInstance(true);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.kit_chuck_fragment_transaction_overview, container, false);
        url = (TextView) view.findViewById(R.id.url);
        method = (TextView) view.findViewById(R.id.method);
        params = (TextView) view.findViewById(R.id.params);
        protocol = (TextView) view.findViewById(R.id.protocol);
        status = (TextView) view.findViewById(R.id.status);
        response = (TextView) view.findViewById(R.id.response);
        ssl = (TextView) view.findViewById(R.id.ssl);
        requestTime = (TextView) view.findViewById(R.id.request_time);
        responseTime = (TextView) view.findViewById(R.id.response_time);
        duration = (TextView) view.findViewById(R.id.duration);
        requestSize = (TextView) view.findViewById(R.id.request_size);
        responseSize = (TextView) view.findViewById(R.id.response_size);
        totalSize = (TextView) view.findViewById(R.id.total_size);
        return view;
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        populateUI();
    }

    @Override
    public void transactionUpdated(HttpTransaction transaction) {
        this.transaction = transaction;
        populateUI();
    }

    private void populateUI() {
        if (isAdded() && transaction != null) {

            AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
            if (appCallBackItf != null) {
                String decryUrl;
                if ("GET".equals(transaction.getMethod())) {
                    decryUrl = appCallBackItf.decryHttpGetParams(transaction.getUrl(), transaction.getUrl());
                } else {
                    decryUrl = appCallBackItf.decryHttpPostParam(transaction.getUrl(), transaction.getUrl());
                }
                url.setText(decryUrl);
                params.setText(filterImportantParams(decryUrl));
            } else {
                url.setText(URLDecoder.decode(transaction.getUrl()));
                params.setText(filterImportantParams(URLDecoder.decode(transaction.getUrl())));
            }


            method.setText(transaction.getMethod());
            protocol.setText(transaction.getProtocol());
            status.setText(transaction.getStatus().toString());
            response.setText(transaction.getResponseSummaryText());
            ssl.setText((transaction.isSsl() ? R.string.kit_chuck_yes : R.string.kit_chuck_no));
            requestTime.setText(transaction.getRequestDateString());
            responseTime.setText(transaction.getResponseDateString());
            duration.setText(transaction.getDurationString());
            requestSize.setText(transaction.getRequestSizeString());
            responseSize.setText(transaction.getResponseSizeString());
            totalSize.setText(transaction.getTotalSizeString());

        }
    }

    /**
     * @param decodeUrl
     * @return
     */
    private SpannableString filterImportantParams(String decodeUrl) {
        SpannableString splitText = getSplitText(decodeUrl);
        if (TextUtils.isEmpty(splitText)) {
            return new SpannableString("无参数");
        }
        return splitText;
    }


    private SpannableString getSplitText(String decodeUrl) {
        if (decodeUrl == null) return null;
        //存储高亮索引
        final List<IndexBean> indexBeanList = new ArrayList<>();
        //构建新的数据
        final StringBuilder stringBuilder = new StringBuilder();

        int index = decodeUrl.indexOf("?");

        if (index != -1) {
            decodeUrl = decodeUrl.substring(index + 1);
        }

        String[] split = decodeUrl.split("&");
        int length = split.length;
        for (int i = 0; i < length; i++) {
            String item = split[i];
            String[] param = item.split("=");
            if (param.length == 2) {
                String key = param[0];
                String value = param[1];
//                if (commonList.contains(key)) continue;
                indexBeanList.add(IndexBean.getIndexBean(stringBuilder, key));

                stringBuilder.append(key);
                stringBuilder.append(":");
                stringBuilder.append(URLDecoder.decode(value));
                stringBuilder.append("\n");
            }
        }

        final SpannableString spannableString = new SpannableString(stringBuilder);
        for (IndexBean indexBean : indexBeanList) {
            spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#FF9800")), indexBean.start, indexBean.end, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        }

        return spannableString;
    }

    public static class IndexBean {
        public int start;
        public int end;

        public static IndexBean getIndexBean(StringBuilder stringBuilder, String key) {
            IndexBean indexBean = new IndexBean();
            int start = stringBuilder.length() - 1;
            if (start < 0) {
                start = 0;
            }
            indexBean.start = start;
            indexBean.end = stringBuilder.length() + key.length();
            return indexBean;
        }
    }

}
