package com.twf.develophelpertools.shake.callback;

import android.app.Activity;
import android.app.Application.ActivityLifecycleCallbacks;
import android.content.Context;
import android.os.Bundle;
import androidx.fragment.app.FragmentActivity;

import com.twf.develophelpertools.shake.utils.ShakeDebugUtils;
import com.twf.develophelpertools.util.ActivityNameStackUtils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;


/**
 * 监听Activity栈的状态信息
 * <p>
 * Created by quzhiyong on 2018/9/3
 */
public class ActivityCallbacks implements ActivityLifecycleCallbacks, FragmentCallbacks.OnFragmentResumeCallbacks {

    private Map<String, Integer> actMap = new HashMap<>();// String存放activity的名称，Integer存放activity引用的个数
    private Set<String> fragmentSet = new HashSet<>();// 存放activity对应的fragment，String类型为fragment名称+"@"+id

    public OnActCallbacks actCallbacks;
    private FragmentCallbacks fragmentCallbacks;

    public Context mContext;

    public ActivityCallbacks(Context context) {
        mContext = context;
    }

    public void setActCallbacks(OnActCallbacks callbacks) {
        actCallbacks = callbacks;
    }

    //摇一摇管理类
    private final Map<String, ShakeDebugUtils> shakeDebugUtilsMap = new HashMap<>();// String存放activity的名称


    @Override
    public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
        String actName = activity.getComponentName().getClassName();

        //摇一摇
        if (shakeDebugUtilsMap.get(actName) == null) {
            shakeDebugUtilsMap.put(actName, new ShakeDebugUtils(activity, this));
        }

        if (actMap.containsKey(actName)) {
            int count = actMap.get(actName);

            actMap.put(actName, count + 1);
        } else {
            actMap.put(actName, 1);
        }

        fragmentCallbacks = new FragmentCallbacks();
        fragmentCallbacks.setResumeCallbacks(this);

        try {
            ((FragmentActivity) activity).getSupportFragmentManager().registerFragmentLifecycleCallbacks(
                    fragmentCallbacks, false);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onActivityStarted(Activity activity) {
    }

    @Override
    public void onActivityResumed(Activity activity) {
        String actName = activity.getComponentName().getClassName();

        fragmentSet.clear();
        if (actCallbacks != null) {
            actCallbacks.onActResume(actName, actMap.get(actName));

            int allRefCount = 0;
            for (Integer count : actMap.values()) {
                allRefCount += count;
            }

            actCallbacks.onAllActRef(allRefCount);
        }

        //摇一摇
        ShakeDebugUtils shakeDebugUtils = shakeDebugUtilsMap.get(actName);
        if (shakeDebugUtils != null) {
            shakeDebugUtils.onResume();
        }

        ActivityNameStackUtils.getInstance().pushName(actName);
    }

    @Override
    public void onActivityPaused(Activity activity) {
        String actName = activity.getComponentName().getClassName();
        ShakeDebugUtils shakeDebugUtils = shakeDebugUtilsMap.get(actName);
        if (shakeDebugUtils != null) {
            shakeDebugUtils.onPause();
        }
    }

    @Override
    public void onActivityStopped(Activity activity) {
    }

    @Override
    public void onActivitySaveInstanceState(Activity activity, Bundle outState) {
    }

    @Override
    public void onActivityDestroyed(Activity activity) {
        String actName = activity.getComponentName().getClassName();

        if (actMap.containsKey(actName)) {
            int count = actMap.get(actName);

            if (count <= 1) {
                actMap.remove(actName);
            } else {
                actMap.put(actName, count - 1);
            }
        }

        try {
            ((FragmentActivity) activity).getSupportFragmentManager()
                    .unregisterFragmentLifecycleCallbacks(fragmentCallbacks);
        } catch (Exception e) {
            e.printStackTrace();
        }

        //删除摇一摇
        ShakeDebugUtils shakeDebugUtils = shakeDebugUtilsMap.get(actName);
        if (shakeDebugUtils != null) {
            shakeDebugUtilsMap.remove(actName);
        }
    }

    @Override
    public void onFragmentResume(String nameAndId) {
        fragmentSet.add(nameAndId);

        if (actCallbacks != null) {
            actCallbacks.onFragmentResume(fragmentSet);
        }
    }

    public interface OnActCallbacks {
        /**
         * activity执行onResume的回调
         *
         * @param actName  activity名称，如com.example.AActivity
         * @param refCount activity实例的引用个数
         */
        void onActResume(String actName, int refCount);

        /**
         * activity执行onResume的回调
         *
         * @param refCount 所有activity的引用个数
         */
        void onAllActRef(int refCount);

        /**
         * fragment执行onResume的回调
         *
         * @param fragmentSet 当前fragment的集合
         */
        void onFragmentResume(Set<String> fragmentSet);
    }
}
