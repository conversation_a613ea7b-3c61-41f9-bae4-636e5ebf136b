package com.twf.develophelpertools.shake.callback;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

/**
 * 监听Fragment生命周期，记录Activity包含的Fragment
 *
 * Created by quz<PERSON>yong on 2018/9/3
 */
public class FragmentCallbacks extends FragmentManager.FragmentLifecycleCallbacks {

    private OnFragmentResumeCallbacks onResumeCallbacks;

    public void setResumeCallbacks(OnFragmentResumeCallbacks onFragmentCallbacks) {
        this.onResumeCallbacks = onFragmentCallbacks;
    }

    @Override
    public void onFragmentResumed(FragmentManager fm, Fragment f) {
        super.onFragmentResumed(fm, f);

        if (onResumeCallbacks != null) {
            onResumeCallbacks.onFragmentResume(f.getClass().getName() + "@" + f.getId());
        }
    }

    public interface OnFragmentResumeCallbacks {
        void onFragmentResume(String nameAndId);
    }
}
