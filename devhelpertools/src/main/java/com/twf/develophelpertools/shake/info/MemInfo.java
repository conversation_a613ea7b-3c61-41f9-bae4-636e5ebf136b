package com.twf.develophelpertools.shake.info;

import android.app.Activity;
import android.app.ActivityManager;
import android.os.Debug;

import java.lang.ref.WeakReference;

/**
 * 内存信息
 *
 * Created by quzhiyong on 2018/9/3
 */
public class MemInfo {
    private long totalMemory = 0;// 当前分配的总内存

    private WeakReference<Activity> activityWeakReference;

    public MemInfo(Activity activity) {
        activityWeakReference = new WeakReference<>(activity);

        initMemInfo();
    }

    private void initMemInfo() {
        Activity activity = activityWeakReference.get();
        if (activity == null) return;

        Debug.MemoryInfo[] myMemoryInfo = ((ActivityManager) activity.getSystemService(activity.ACTIVITY_SERVICE))
                .getProcessMemoryInfo(new int[]{android.os.Process.myPid()});

        for (Debug.MemoryInfo info : myMemoryInfo) {
            int tempMemory = info.dalvikPrivateDirty + info.dalvikPss + info.dalvikSharedDirty
                    + info.nativePrivateDirty + info.nativePss + info.nativeSharedDirty
                    + info.otherPrivateDirty + info.otherPss + info.otherSharedDirty;
            totalMemory += tempMemory;
        }
    }

    public long getTotalMemory() {
        return totalMemory;
    }
}
