package com.twf.develophelpertools.shake.utils;

import java.util.HashSet;
import java.util.Arrays;

/**
 * 获取线程信息
 *
 * Created by quz<PERSON>yong on 2018/9/3
 */
public class ThreadUtils {

    /**
     * 获取除系统外的所有线程名称
     */
    public static String[] getThreadNames() {
        ThreadGroup group = Thread.currentThread().getThreadGroup();
        ThreadGroup parent = null;

        while ((parent = group.getParent()) != null) {
            group = parent;
        }

        Thread[] threads = new Thread[group.activeCount()];
        group.enumerate(threads);
        HashSet set = new HashSet();

        for (int i=0; i < threads.length; ++i) {
            if (threads[i] != null && threads[i].isAlive()) {
                try {
                    if (!"system".equals(threads[i].getThreadGroup().getName())) {// 过滤掉系统线程名
                        if (!"queued-work-looper".equals(threads[i].getName())) {// 过滤掉默认Handler线程名
                            set.add(threads[i].getName());
                        }
                    }
                } catch (Throwable e) {e.printStackTrace();}
            }
        }

        String[] result = (String[]) set.toArray(new String[0]);
        Arrays.sort(result);

        return result;
    }

}
