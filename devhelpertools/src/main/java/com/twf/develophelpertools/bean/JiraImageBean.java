package com.twf.develophelpertools.bean;

import android.os.Parcel;
import android.os.Parcelable;

import java.io.Serializable;

/**
 * create by guofeng
 * date on 2021/5/8
 */

public class JiraImageBean implements Serializable{

    public static final String IMAGE = "image";

    public static final String VIDEO = "video";

    private String localPath;

    private String url;

    private boolean isAdd;

    /**
     * @see #IMAGE
     * @see #VIDEO
     */
    private String fileType;

    public JiraImageBean() {
    }

    public boolean isAdd() {
        return isAdd;
    }

    public void setAdd(boolean add) {
        isAdd = add;
    }

    public String getLocalPath() {
        return localPath;
    }

    public String getUrl() {
        return url;
    }

    public String getFileType() {
        return fileType;
    }

    public void setLocalPath(String localPath) {
        this.localPath = localPath;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    @Override
    public String toString() {
        return "JiraImageBean{" +
                "localPath='" + localPath + '\'' +
                ", url='" + url + '\'' +
                ", isAdd=" + isAdd +
                ", fileType='" + fileType + '\'' +
                '}';
    }
}