package com.twf.develophelpertools.bean;

import android.view.View;

/**
 * create by guofeng
 * date on 2021/8/4
 */

public class CommonToolsBean {

    private String name;


    private int resourceBg;


    private View.OnClickListener mListener;

    public void setName(String name) {
        this.name = name;
    }

    public void setResourceBg(int resourceBg) {
        this.resourceBg = resourceBg;
    }

    public void setmListener(View.OnClickListener mListener) {
        this.mListener = mListener;
    }

    public String getName() {
        return name;
    }

    public int getResourceBg() {
        return resourceBg;
    }

    public View.OnClickListener getmListener() {
        return mListener;
    }
}