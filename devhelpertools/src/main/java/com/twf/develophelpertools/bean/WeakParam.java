package com.twf.develophelpertools.bean;

/**
 * Created by guofeng
 * on 2019/7/9.
 */
public class WeakParam {

    private int status;
    private int timeOut;

    private int requestLimit;
    private int responseLimit;

    public void setStatus(int status) {
        this.status = status;
    }

    public void setTimeOut(int timeOut) {
        this.timeOut = timeOut;
    }

    public void setRequestLimit(int requestLimit) {
        this.requestLimit = requestLimit;
    }

    public void setResponseLimit(int responseLimit) {
        this.responseLimit = responseLimit;
    }

    public int getStatus() {
        return status;
    }

    public int getTimeOut() {
        return timeOut;
    }

    public int getRequestLimit() {
        return requestLimit;
    }

    public int getResponseLimit() {
        return responseLimit;
    }
}
