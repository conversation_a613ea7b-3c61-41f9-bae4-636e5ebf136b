package com.twf.develophelpertools.bean;

import java.io.Serializable;

public class AnalyseSpBean implements Serializable {

    private String successValue;

    private String failedValue;

    private long timeMillSecond;

    public void setTimeMillSecond(long timeMillSecond) {
        this.timeMillSecond = timeMillSecond;
    }

    public void setSuccessValue(String successValue) {
        this.successValue = successValue;
    }

    public void setFailedValue(String failedValue) {
        this.failedValue = failedValue;
    }

    public long getTimeMillSecond() {
        return timeMillSecond;
    }

    public String getSuccessValue() {
        return successValue;
    }

    public String getFailedValue() {
        return failedValue;
    }
}
