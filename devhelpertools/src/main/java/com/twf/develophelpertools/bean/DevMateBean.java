package com.twf.develophelpertools.bean;

import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;

/**
 * create by guofeng
 * date on 2021/5/8
 */

public class DevMateBean {

    private String name;
    private String email;
    private String weixinNumber;
    private String mGroupName;
    private boolean isSelect;

    public DevMateBean() {
    }

    public boolean isSelect() {
        return isSelect;
    }

    public void setSelect(boolean select) {
        isSelect = select;
    }

    public void setmGroupName(String mGroupName) {
        this.mGroupName = mGroupName;
    }

    public String getmGroupName() {
        return mGroupName;
    }

    public String getName() {
        return name;
    }

    public String getEmail() {
        return email;
    }

    public String getWeixinNumber() {
        return weixinNumber;
    }


    public void setName(String name) {
        this.name = name;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public void setWeixinNumber(String weixinNumber) {
        this.weixinNumber = weixinNumber;
    }


    public boolean isSameMateBean(DevMateBean mateBean) {
        if (TextUtils.equals(this.getName(), mateBean.getName())
                && TextUtils.equals(this.getWeixinNumber(), mateBean.getWeixinNumber())
                && TextUtils.equals(this.getmGroupName(), mateBean.getmGroupName())
                && TextUtils.equals(this.getEmail(), mateBean.getEmail())
        ) {
            return true;
        }
        return false;
    }

    @Override
    public String toString() {
        return "DevMateBean{" +
                "name='" + name + '\'' +
                ", email='" + email + '\'' +
                ", weixinNumber='" + weixinNumber + '\'' +
                ", mGroupName='" + mGroupName + '\'' +
                ", isSelect=" + isSelect +
                '}';
    }
}