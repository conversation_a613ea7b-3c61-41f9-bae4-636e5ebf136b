package com.twf.develophelpertools.activity;

import android.graphics.Color;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.kit.baselibrary.AppCallBackItf;
import com.kit.baselibrary.ButtonConfigBean;
import com.kit.baselibrary.DetailConfigBean;
import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.adapter.MainPanelPagerAdapter;
import com.twf.develophelpertools.model.fragment.ButtonConfigFragment;
import com.twf.develophelpertools.model.fragment.CommonToolsFragment;
import com.twf.develophelpertools.model.fragment.DetailConfigFragment;
import com.twf.develophelpertools.model.fragment.SwitchConfigFragment;
import com.twf.develophelpertools.model.fragment.UIToolsFragment;

import java.util.ArrayList;
import java.util.List;

/**
 * create by guofeng
 * date on 2021/8/4
 */

public class MainPanelActivity extends BaseKitActivity {


    private LinearLayout mTabView;

    private ViewPager mViewPager;



    private final List<TabFragment> tabFragmentList = new ArrayList<TabFragment>() {
        {
            AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
            if (appCallBackItf != null) {

                add(new TabFragment("通用工具", CommonToolsFragment.newInstance()));

                add(new TabFragment("UI工具", UIToolsFragment.newInstance()));

                List<ButtonConfigBean> buttonConfigList = appCallBackItf.getButtonConfigList();
                if (buttonConfigList != null && buttonConfigList.size() > 0) {
                    add(new TabFragment("按钮配置", ButtonConfigFragment.newInstance()));
                }

                add(new TabFragment("开关配置", SwitchConfigFragment.newInstance()));

                List<DetailConfigBean> detailConfigBeans = appCallBackItf.getDetailConfigList();
                if (detailConfigBeans != null && detailConfigBeans.size() > 0) {
                    add(new TabFragment("详情配置", DetailConfigFragment.newInstance()));
                }

            }

        }
    };


    private static class TabFragment {

        private String tabName;

        private Fragment fragment;

        public TabFragment(String tabName, Fragment fragment) {
            this.tabName = tabName;
            this.fragment = fragment;
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.kit_main_panel_activity);

        mTabView = findViewById(R.id.mTabView);

        mViewPager = findViewById(R.id.mViewPager);

        findViewById(R.id.mExit).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        refreshAdapter();
    }


    private MainPanelPagerAdapter adapter;

    private void refreshAdapter() {
        if (adapter == null) {
            adapter = new MainPanelPagerAdapter(getSupportFragmentManager());
        }
        mViewPager.setAdapter(adapter);
        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                refreshTabView();
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
        adapter.setData(getData());
        refreshTabView();
    }


    private List<Fragment> getData() {
        List<Fragment> data = new ArrayList<>();
        for (TabFragment tabFragment : tabFragmentList) {
            Fragment fragment = tabFragment.fragment;
            data.add(fragment);
        }
        return data;
    }


    private void refreshTabView() {
        mTabView.removeAllViews();
        int defIndex = mViewPager.getCurrentItem();
        for (int i = 0; i < tabFragmentList.size(); i++) {
            TabFragment tabFragment = tabFragmentList.get(i);
            String tabName = tabFragment.tabName;
            final View tabView = getTabView(tabName, defIndex == i);
            mTabView.addView(tabView);

            final int index = i;
            tabView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    mViewPager.setCurrentItem(index);
                }
            });
        }
    }

    private View getTabView(String tabName, boolean isSelect) {
        TextView textView = new TextView(this);
        textView.setText(tabName);
        textView.setGravity(Gravity.CENTER);

        if (isSelect) {
            textView.setTextColor(Color.parseColor("#0000FF"));
            textView.getPaint().setFakeBoldText(true);
        } else {
            textView.setTextColor(Color.parseColor("#000000"));
            textView.getPaint().setFakeBoldText(false);
        }


        textView.setTextSize(17);

        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.MATCH_PARENT);
        params.weight = 1;
        textView.setLayoutParams(params);

        return textView;
    }

} 