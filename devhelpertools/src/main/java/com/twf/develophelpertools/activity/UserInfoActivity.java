package com.twf.develophelpertools.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.widget.Toast;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.chunk.internal.view.ExpandCombineLayout;
import com.twf.develophelpertools.util.KitMainHandler;

/**
 * create by guofeng
 * date on 2020/10/23
 */
public class UserInfoActivity extends BaseKitActivity {

    private static String resultvalue;


    public static void jumpToUserInfo(Context context, String json) {
        resultvalue = json;
        if (TextUtils.isEmpty(resultvalue)) {
            Toast.makeText(context, "jsonValue 为空", Toast.LENGTH_SHORT).show();
            return;
        }
        Intent intent = new Intent(context, UserInfoActivity.class);
        context.startActivity(intent);
    }


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.kit_userinfo_activity);

        Toast.makeText(this, "解析数据中", Toast.LENGTH_SHORT).show();

        KitMainHandler.getMainHandler().postDelayed(new Runnable() {
            @Override
            public void run() {
                ExpandCombineLayout mExpandCombineView = findViewById(R.id.mExpandCombineView);
                ExpandCombineLayout.invalidateView(UserInfoActivity.this, resultvalue, mExpandCombineView, true);
            }
        }, 500);

    }
}