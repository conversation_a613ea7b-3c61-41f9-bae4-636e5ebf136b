package com.twf.develophelpertools.activity;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;

import com.kit.baselibrary.AppCallBackItf;
import com.kit.baselibrary.EnvironmentChangeCallBack;
import com.kit.baselibrary.EnvironmentConfigBean;
import com.kit.baselibrary.EnvironmentInfoBean;
import com.kit.baselibrary.ZpTagCallBack;
import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.R;
import com.twf.develophelpertools.chunk.internal.ui.BaseChuckActivity;

import java.util.ArrayList;
import java.util.List;

/**
 * create by guofeng
 * date on 2021/7/30
 */

public class EnvironmentChangeActivity extends BaseChuckActivity {

    private TextView tvCurrentApiHost;
    private TextView tvCurrentChatHost;

    private TextView tvBuildinApiHost;
    private TextView tvBuildinChatHost;

    private TextView tvSaveZpTag;

    private EditText etZpTag;

    private Spinner spinnerHostBildin;

    private Button btSave;
    private Button btReset;
    private EnvironmentInfoBean currentEnvironmentConfig;

    public static void show(Activity context) {
        Intent intent = new Intent(context, EnvironmentChangeActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.kit_environment_change);
        findView();
        initView();
    }


    private void findView() {
        tvCurrentApiHost = findViewById(R.id.tv_current_api_host);
        tvCurrentChatHost = findViewById(R.id.tv_current_chat_host);
        tvBuildinApiHost = findViewById(R.id.tv_buildin_api_host);
        tvBuildinChatHost = findViewById(R.id.tv_buildin_chat_host);
        etZpTag = findViewById(R.id.et_zp_tag);
        tvSaveZpTag = findViewById(R.id.tv_zp_tag_save);

        spinnerHostBildin = findViewById(R.id.spinner_buildin);

        btSave = findViewById(R.id.bt_save);
        btReset = findViewById(R.id.bt_reset);
    }

    private void initView() {
        btSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AlertDialog.Builder alertDialogBuilder = new AlertDialog.Builder(EnvironmentChangeActivity.this);
                alertDialogBuilder.setPositiveButton("确定", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.dismiss();
                        finish();
                        if (currentEnvironmentConfig == null) return;
                        AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
                        if (appCallBackItf != null) {
                            EnvironmentConfigBean environmentConfigBean = appCallBackItf.getEnvironmentConfigBean();
                            if (environmentConfigBean != null) {
                                EnvironmentChangeCallBack environmentChangeCallBack = environmentConfigBean.getEnvironmentChangeCallBack();
                                if (environmentChangeCallBack != null) {
                                    environmentChangeCallBack.onEnvironmentChangeListener(currentEnvironmentConfig);
                                }
                            }
                        }
                    }
                });
                alertDialogBuilder.setNegativeButton("取消", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.dismiss();
                    }
                });
                alertDialogBuilder.setTitle("信息");
                alertDialogBuilder.setMessage("确定要更新域名配置么");
                alertDialogBuilder.setCancelable(true);
                AlertDialog alertDialog = alertDialogBuilder.create();
                alertDialog.show();//将dialog显示出来
            }
        });
        btReset.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AlertDialog.Builder alertDialogBuilder = new AlertDialog.Builder(EnvironmentChangeActivity.this);
                alertDialogBuilder.setPositiveButton("确定", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.dismiss();
                        AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
                        if (appCallBackItf != null) {
                            EnvironmentConfigBean environmentConfigBean = appCallBackItf.getEnvironmentConfigBean();
                            if (environmentConfigBean != null) {
                                View.OnClickListener onResetListener = environmentConfigBean.getOnResetListener();
                                if (onResetListener != null) {
                                    onResetListener.onClick(null);
                                }
                            }
                        }
                    }
                });
                alertDialogBuilder.setNegativeButton("取消", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.dismiss();
                    }
                });
                alertDialogBuilder.setTitle("信息");
                alertDialogBuilder.setMessage("确定要重置域名配置么");
                alertDialogBuilder.setCancelable(true);
                AlertDialog alertDialog = alertDialogBuilder.create();
                alertDialog.show();//将dialog显示出来
            }
        });


        final AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
        if (appCallBackItf == null) return;

        final EnvironmentConfigBean environmentConfigBean = appCallBackItf.getEnvironmentConfigBean();
        if (environmentConfigBean != null) {
            currentEnvironmentConfig = environmentConfigBean.getCurrentEnvironment();
        }

        if (currentEnvironmentConfig == null) return;


        tvCurrentApiHost.setText(currentEnvironmentConfig.apiAddr);
        tvCurrentChatHost.setText(currentEnvironmentConfig.mqAddr);

        tvBuildinApiHost.setText(currentEnvironmentConfig.apiAddr);
        tvBuildinChatHost.setText(currentEnvironmentConfig.mqAddr);


        initSpinner();

        tvSaveZpTag.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String tempUrl = etZpTag.getText().toString().trim();
                if (TextUtils.isEmpty(tempUrl)) {
                    Toast.makeText(EnvironmentChangeActivity.this, "url不能为空", Toast.LENGTH_SHORT).show();
                    return;
                }
                if (environmentConfigBean != null) {
                    ZpTagCallBack zpTagCallBack = environmentConfigBean.getZpTagCallBack();
                    if (zpTagCallBack != null) {
                        zpTagCallBack.onZpTagChangeListener(tempUrl);
                    }
                }
                Toast.makeText(EnvironmentChangeActivity.this, "保存成功", Toast.LENGTH_SHORT).show();
            }
        });

        if (environmentConfigBean != null) {
            etZpTag.setText(environmentConfigBean.getZpTag());
        }

    }

    private void initSpinner() {
        final AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
        if (appCallBackItf == null) return;

        List<EnvironmentInfoBean> environmentConfigList = null;

        EnvironmentConfigBean environmentConfigBean = appCallBackItf.getEnvironmentConfigBean();
        if (environmentConfigBean != null) {
            environmentConfigList = environmentConfigBean.getConfigList();
        }


        if (environmentConfigList == null || environmentConfigList.size() == 0) return;

        List<String> strings = new ArrayList<>();
        for (EnvironmentInfoBean environmentInfoBean : environmentConfigList) {
            if (environmentInfoBean == null) continue;
            strings.add(environmentInfoBean.name);
        }


        final List<EnvironmentInfoBean> tempConfigList = environmentConfigList;

        ArrayAdapter<String> adapter = new ArrayAdapter<>(this,
                android.R.layout.simple_spinner_item, strings);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerHostBildin.setAdapter(adapter);
        spinnerHostBildin.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view,
                                       int pos, long id) {

                currentEnvironmentConfig = tempConfigList.get(pos);
                tvBuildinApiHost.setText(currentEnvironmentConfig.apiAddr);
                tvBuildinChatHost.setText(currentEnvironmentConfig.mqAddr);
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                // Another interface refresh
            }
        });


    }


}