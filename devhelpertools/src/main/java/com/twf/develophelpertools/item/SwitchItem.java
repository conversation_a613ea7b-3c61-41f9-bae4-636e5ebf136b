package com.twf.develophelpertools.item;


import androidx.annotation.IntDef;

import com.twf.develophelpertools.bean.Element;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;


public class SwitchItem extends ElementItem {

    @Type
    private int type;
    private boolean isChecked;

    public SwitchItem(String name, Element element, @Type int type) {
        super(name, element);
        this.type = type;
    }

    public SwitchItem(String name, Element element, @Type int type, boolean isChecked) {
        super(name, element);
        this.type = type;
        this.isChecked = isChecked;
    }

    public void setChecked(boolean checked) {
        isChecked = checked;
    }

    public boolean isChecked() {
        return isChecked;
    }

    public int getType() {
        return type;
    }

    @IntDef({
            Type.TYPE_IS_BOLD,
            Type.TYPE_MOVE,
            Type.TYPE_SHOW_VALID_VIEWS,
    })
    @Retention(RetentionPolicy.SOURCE)
    public @interface Type {
        int TYPE_IS_BOLD = 1;
        int TYPE_MOVE = 2;
        int TYPE_SHOW_VALID_VIEWS = 3;
    }
}
