package com.twf.develophelpertools.item;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.view.TreeNodeWrapperView;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

public class TreeNode {
    public static final String NODES_ID_SEPARATOR = ":";
    private int mId;
    private int mLastId;
    private TreeNode mParent;
    private boolean mSelected;
    private boolean mSelectable = true;
    private final List<TreeNode> children = new ArrayList();
    private TreeNode.BaseNodeViewHolder mViewHolder;
    private TreeNode.TreeNodeClickListener mClickListener;
    private TreeNode.TreeNodeLongClickListener mLongClickListener;
    private Object mValue;
    private boolean mExpanded;

    public static TreeNode root() {
        TreeNode root = new TreeNode((Object)null);
        root.setSelectable(false);
        return root;
    }

    private int generateId() {
        return ++this.mLastId;
    }

    public TreeNode(Object value) {
        this.mValue = value;
    }

    public TreeNode addChild(TreeNode childNode) {
        childNode.mParent = this;
        childNode.mId = this.generateId();
        this.children.add(childNode);
        return this;
    }

    public TreeNode addChildren(TreeNode... nodes) {
        TreeNode[] var2 = nodes;
        int var3 = nodes.length;

        for(int var4 = 0; var4 < var3; ++var4) {
            TreeNode n = var2[var4];
            this.addChild(n);
        }

        return this;
    }

    public TreeNode addChildren(Collection<TreeNode> nodes) {
        Iterator var2 = nodes.iterator();

        while(var2.hasNext()) {
            TreeNode n = (TreeNode)var2.next();
            this.addChild(n);
        }

        return this;
    }

    public int deleteChild(TreeNode child) {
        for(int i = 0; i < this.children.size(); ++i) {
            if (child.mId == ((TreeNode)this.children.get(i)).mId) {
                this.children.remove(i);
                return i;
            }
        }

        return -1;
    }

    public List<TreeNode> getChildren() {
        return Collections.unmodifiableList(this.children);
    }

    public int size() {
        return this.children.size();
    }

    public TreeNode getParent() {
        return this.mParent;
    }

    public int getId() {
        return this.mId;
    }

    public boolean isLeaf() {
        return this.size() == 0;
    }

    public Object getValue() {
        return this.mValue;
    }

    public boolean isExpanded() {
        return this.mExpanded;
    }

    public TreeNode setExpanded(boolean expanded) {
        this.mExpanded = expanded;
        return this;
    }

    public void setSelected(boolean selected) {
        this.mSelected = selected;
    }

    public boolean isSelected() {
        return this.mSelectable && this.mSelected;
    }

    public void setSelectable(boolean selectable) {
        this.mSelectable = selectable;
    }

    public boolean isSelectable() {
        return this.mSelectable;
    }

    public String getPath() {
        StringBuilder path = new StringBuilder();
        TreeNode node = this;

        while(node.mParent != null) {
            path.append(node.getId());
            node = node.mParent;
            if (node.mParent != null) {
                path.append(":");
            }
        }

        return path.toString();
    }

    public int getLevel() {
        int level = 0;

        for(TreeNode root = this; root.mParent != null; ++level) {
            root = root.mParent;
        }

        return level;
    }

    public boolean isLastChild() {
        if (!this.isRoot()) {
            int parentSize = this.mParent.children.size();
            if (parentSize > 0) {
                List<TreeNode> parentChildren = this.mParent.children;
                return ((TreeNode)parentChildren.get(parentSize - 1)).mId == this.mId;
            }
        }

        return false;
    }

    public TreeNode setViewHolder(TreeNode.BaseNodeViewHolder viewHolder) {
        this.mViewHolder = viewHolder;
        if (viewHolder != null) {
            viewHolder.mNode = this;
        }

        return this;
    }

    public TreeNode setClickListener(TreeNode.TreeNodeClickListener listener) {
        this.mClickListener = listener;
        return this;
    }

    public TreeNode.TreeNodeClickListener getClickListener() {
        return this.mClickListener;
    }

    public TreeNode setLongClickListener(TreeNode.TreeNodeLongClickListener listener) {
        this.mLongClickListener = listener;
        return this;
    }

    public TreeNode.TreeNodeLongClickListener getLongClickListener() {
        return this.mLongClickListener;
    }

    public TreeNode.BaseNodeViewHolder getViewHolder() {
        return this.mViewHolder;
    }

    public boolean isFirstChild() {
        if (!this.isRoot()) {
            List<TreeNode> parentChildren = this.mParent.children;
            return ((TreeNode)parentChildren.get(0)).mId == this.mId;
        } else {
            return false;
        }
    }

    public boolean isRoot() {
        return this.mParent == null;
    }

    public TreeNode getRoot() {
        TreeNode root;
        for(root = this; root.mParent != null; root = root.mParent) {
        }

        return root;
    }

    public abstract static class BaseNodeViewHolder<E> {
        protected AndroidTreeView tView;
        protected TreeNode mNode;
        private View mView;
        protected int containerStyle;
        protected Context context;

        public BaseNodeViewHolder(Context context) {
            this.context = context;
        }

        public View getView() {
            if (this.mView != null) {
                return this.mView;
            } else {
                View nodeView = this.getNodeView();
                TreeNodeWrapperView nodeWrapperView = new TreeNodeWrapperView(nodeView.getContext(), this.getContainerStyle());
                nodeWrapperView.insertNodeView(nodeView);
                this.mView = nodeWrapperView;
                return this.mView;
            }
        }

        public void setTreeViev(AndroidTreeView treeViev) {
            this.tView = treeViev;
        }

        public AndroidTreeView getTreeView() {
            return this.tView;
        }

        public void setContainerStyle(int style) {
            this.containerStyle = style;
        }

        public View getNodeView() {
            return this.createNodeView(this.mNode, (E) this.mNode.getValue());
        }

        public ViewGroup getNodeItemsView() {
            return (ViewGroup)this.getView().findViewById(R.id.kit_node_items);
        }

        public boolean isInitialized() {
            return this.mView != null;
        }

        public int getContainerStyle() {
            return this.containerStyle;
        }

        public abstract View createNodeView(TreeNode var1, E var2);

        public void toggle(boolean active) {
        }

        public void toggleSelectionMode(boolean editModeEnabled) {
        }
    }

    public interface TreeNodeLongClickListener {
        boolean onLongClick(TreeNode var1, Object var2);
    }

    public interface TreeNodeClickListener {
        void onClick(TreeNode var1, Object var2);
    }
}
