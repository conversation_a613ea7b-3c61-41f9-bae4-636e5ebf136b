package com.twf.develophelpertools.item;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.ContextThemeWrapper;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.Transformation;
import android.widget.LinearLayout;
import android.widget.ScrollView;

import com.twf.develophelpertools.R;
import com.twf.develophelpertools.binder.SimpleViewHolder;
import com.twf.develophelpertools.view.TwoDScrollView;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

public class AndroidTreeView {

    private static final String NODES_PATH_SEPARATOR = ";";
    protected TreeNode mRoot;
    private Context mContext;
    private boolean applyForRoot;
    private int containerStyle = 0;
    private Class<? extends TreeNode.BaseNodeViewHolder> defaultViewHolderClass = SimpleViewHolder.class;
    private TreeNode.TreeNodeClickListener nodeClickListener;
    private TreeNode.TreeNodeLongClickListener nodeLongClickListener;
    private boolean mSelectionModeEnabled;
    private boolean mUseDefaultAnimation = false;
    private boolean use2dScroll = false;
    private boolean enableAutoToggle = true;

    public AndroidTreeView(Context context) {
        this.mContext = context;
    }

    public void setRoot(TreeNode mRoot) {
        this.mRoot = mRoot;
    }

    public AndroidTreeView(Context context, TreeNode root) {
        this.mRoot = root;
        this.mContext = context;
    }

    public void setDefaultAnimation(boolean defaultAnimation) {
        this.mUseDefaultAnimation = defaultAnimation;
    }

    public void setDefaultContainerStyle(int style) {
        this.setDefaultContainerStyle(style, false);
    }

    public void setDefaultContainerStyle(int style, boolean applyForRoot) {
        this.containerStyle = style;
        this.applyForRoot = applyForRoot;
    }

    public void setUse2dScroll(boolean use2dScroll) {
        this.use2dScroll = use2dScroll;
    }

    public boolean is2dScrollEnabled() {
        return this.use2dScroll;
    }

    public void setUseAutoToggle(boolean enableAutoToggle) {
        this.enableAutoToggle = enableAutoToggle;
    }

    public boolean isAutoToggleEnabled() {
        return this.enableAutoToggle;
    }

    public void setDefaultViewHolder(Class<? extends TreeNode.BaseNodeViewHolder> viewHolder) {
        this.defaultViewHolderClass = viewHolder;
    }

    public void setDefaultNodeClickListener(TreeNode.TreeNodeClickListener listener) {
        this.nodeClickListener = listener;
    }

    public void setDefaultNodeLongClickListener(TreeNode.TreeNodeLongClickListener listener) {
        this.nodeLongClickListener = listener;
    }

    public void expandAll() {
        this.expandNode(this.mRoot, true);
    }

    public void collapseAll() {
        Iterator var1 = this.mRoot.getChildren().iterator();

        while(var1.hasNext()) {
            TreeNode n = (TreeNode)var1.next();
            this.collapseNode(n, true);
        }

    }

    public View getView(int style) {
        Object view;
        if (style > 0) {
            ContextThemeWrapper newContext = new ContextThemeWrapper(this.mContext, style);
            view = this.use2dScroll ? new TwoDScrollView(newContext) : new ScrollView(newContext);
        } else {
            view = this.use2dScroll ? new TwoDScrollView(this.mContext) : new ScrollView(this.mContext);
        }

        Context containerContext = this.mContext;
        if (this.containerStyle != 0 && this.applyForRoot) {
            containerContext = new ContextThemeWrapper(this.mContext, this.containerStyle);
        }

        final LinearLayout viewTreeItems = new LinearLayout((Context)containerContext, (AttributeSet)null, this.containerStyle);
        viewTreeItems.setId(R.id.kit_tree_items);
        viewTreeItems.setOrientation(LinearLayout.VERTICAL);
        ((ViewGroup)view).addView(viewTreeItems);
        this.mRoot.setViewHolder(new TreeNode.BaseNodeViewHolder(this.mContext) {
            public View createNodeView(TreeNode node, Object value) {
                return null;
            }

            public ViewGroup getNodeItemsView() {
                return viewTreeItems;
            }
        });
        this.expandNode(this.mRoot, false);
        return (View)view;
    }

    public View getView() {
        return this.getView(-1);
    }

    public void expandLevel(int level) {
        Iterator var2 = this.mRoot.getChildren().iterator();

        while(var2.hasNext()) {
            TreeNode n = (TreeNode)var2.next();
            this.expandLevel(n, level);
        }

    }

    private void expandLevel(TreeNode node, int level) {
        if (node.getLevel() <= level) {
            this.expandNode(node, false);
        }

        Iterator var3 = node.getChildren().iterator();

        while(var3.hasNext()) {
            TreeNode n = (TreeNode)var3.next();
            this.expandLevel(n, level);
        }

    }

    public void expandNode(TreeNode node) {
        this.expandNode(node, false);
    }

    public void collapseNode(TreeNode node) {
        this.collapseNode(node, false);
    }

    public String getSaveState() {
        StringBuilder builder = new StringBuilder();
        this.getSaveState(this.mRoot, builder);
        if (builder.length() > 0) {
            builder.setLength(builder.length() - 1);
        }

        return builder.toString();
    }

    public void restoreState(String saveState) {
        if (!TextUtils.isEmpty(saveState)) {
            this.collapseAll();
            String[] openNodesArray = saveState.split(";");
            Set<String> openNodes = new HashSet(Arrays.asList(openNodesArray));
            this.restoreNodeState(this.mRoot, openNodes);
        }

    }

    private void restoreNodeState(TreeNode node, Set<String> openNodes) {
        Iterator var3 = node.getChildren().iterator();

        while(var3.hasNext()) {
            TreeNode n = (TreeNode)var3.next();
            if (openNodes.contains(n.getPath())) {
                this.expandNode(n);
                this.restoreNodeState(n, openNodes);
            }
        }

    }

    private void getSaveState(TreeNode root, StringBuilder sBuilder) {
        Iterator var3 = root.getChildren().iterator();

        while(var3.hasNext()) {
            TreeNode node = (TreeNode)var3.next();
            if (node.isExpanded()) {
                sBuilder.append(node.getPath());
                sBuilder.append(";");
                this.getSaveState(node, sBuilder);
            }
        }

    }

    public void toggleNode(TreeNode node) {
        if (node.isExpanded()) {
            this.collapseNode(node, false);
        } else {
            this.expandNode(node, false);
        }

    }

    private void collapseNode(TreeNode node, boolean includeSubnodes) {
        node.setExpanded(false);
        TreeNode.BaseNodeViewHolder nodeViewHolder = this.getViewHolderForNode(node);
        if (this.mUseDefaultAnimation) {
            collapse(nodeViewHolder.getNodeItemsView());
        } else {
            nodeViewHolder.getNodeItemsView().setVisibility(View.GONE);
        }

        nodeViewHolder.toggle(false);
        if (includeSubnodes) {
            Iterator var4 = node.getChildren().iterator();

            while(var4.hasNext()) {
                TreeNode n = (TreeNode)var4.next();
                this.collapseNode(n, includeSubnodes);
            }
        }

    }

    private void expandNode(TreeNode node, boolean includeSubnodes) {
        node.setExpanded(true);
        TreeNode.BaseNodeViewHolder parentViewHolder = this.getViewHolderForNode(node);
        parentViewHolder.getNodeItemsView().removeAllViews();
        parentViewHolder.toggle(true);
        Iterator var4 = node.getChildren().iterator();

        while(true) {
            TreeNode n;
            do {
                if (!var4.hasNext()) {
                    if (this.mUseDefaultAnimation) {
                        expand(parentViewHolder.getNodeItemsView());
                    } else {
                        parentViewHolder.getNodeItemsView().setVisibility(View.VISIBLE);
                    }

                    return;
                }

                n = (TreeNode)var4.next();
                this.addNode(parentViewHolder.getNodeItemsView(), n);
            } while(!n.isExpanded() && !includeSubnodes);

            this.expandNode(n, includeSubnodes);
        }
    }

    private void addNode(ViewGroup container, final TreeNode n) {
        TreeNode.BaseNodeViewHolder viewHolder = this.getViewHolderForNode(n);
        View nodeView = viewHolder.getView();
        container.addView(nodeView);
        if (this.mSelectionModeEnabled) {
            viewHolder.toggleSelectionMode(this.mSelectionModeEnabled);
        }

        nodeView.setOnClickListener(new View.OnClickListener() {
            public void onClick(View v) {
                if (n.getClickListener() != null) {
                    n.getClickListener().onClick(n, n.getValue());
                } else if (AndroidTreeView.this.nodeClickListener != null) {
                    AndroidTreeView.this.nodeClickListener.onClick(n, n.getValue());
                }

                if (AndroidTreeView.this.enableAutoToggle) {
                    AndroidTreeView.this.toggleNode(n);
                }

            }
        });
        nodeView.setOnLongClickListener(new View.OnLongClickListener() {
            public boolean onLongClick(View view) {
                if (n.getLongClickListener() != null) {
                    return n.getLongClickListener().onLongClick(n, n.getValue());
                } else if (AndroidTreeView.this.nodeLongClickListener != null) {
                    return AndroidTreeView.this.nodeLongClickListener.onLongClick(n, n.getValue());
                } else {
                    if (AndroidTreeView.this.enableAutoToggle) {
                        AndroidTreeView.this.toggleNode(n);
                    }

                    return false;
                }
            }
        });
    }

    public void setSelectionModeEnabled(boolean selectionModeEnabled) {
        if (!selectionModeEnabled) {
            this.deselectAll();
        }

        this.mSelectionModeEnabled = selectionModeEnabled;
        Iterator var2 = this.mRoot.getChildren().iterator();

        while(var2.hasNext()) {
            TreeNode node = (TreeNode)var2.next();
            this.toggleSelectionMode(node, selectionModeEnabled);
        }

    }

    public <E> List<E> getSelectedValues(Class<E> clazz) {
        List<E> result = new ArrayList();
        List<TreeNode> selected = this.getSelected();
        Iterator var4 = selected.iterator();

        while(var4.hasNext()) {
            TreeNode n = (TreeNode)var4.next();
            Object value = n.getValue();
            if (value != null && value.getClass().equals(clazz)) {
                result.add((E) value);
            }
        }

        return result;
    }

    public boolean isSelectionModeEnabled() {
        return this.mSelectionModeEnabled;
    }

    private void toggleSelectionMode(TreeNode parent, boolean mSelectionModeEnabled) {
        this.toogleSelectionForNode(parent, mSelectionModeEnabled);
        if (parent.isExpanded()) {
            Iterator var3 = parent.getChildren().iterator();

            while(var3.hasNext()) {
                TreeNode node = (TreeNode)var3.next();
                this.toggleSelectionMode(node, mSelectionModeEnabled);
            }
        }

    }

    public List<TreeNode> getSelected() {
        return (List)(this.mSelectionModeEnabled ? this.getSelected(this.mRoot) : new ArrayList());
    }

    private List<TreeNode> getSelected(TreeNode parent) {
        List<TreeNode> result = new ArrayList();

        TreeNode n;
        for(Iterator var3 = parent.getChildren().iterator(); var3.hasNext(); result.addAll(this.getSelected(n))) {
            n = (TreeNode)var3.next();
            if (n.isSelected()) {
                result.add(n);
            }
        }

        return result;
    }

    public void selectAll(boolean skipCollapsed) {
        this.makeAllSelection(true, skipCollapsed);
    }

    public void deselectAll() {
        this.makeAllSelection(false, false);
    }

    private void makeAllSelection(boolean selected, boolean skipCollapsed) {
        if (this.mSelectionModeEnabled) {
            Iterator var3 = this.mRoot.getChildren().iterator();

            while(var3.hasNext()) {
                TreeNode node = (TreeNode)var3.next();
                this.selectNode(node, selected, skipCollapsed);
            }
        }

    }

    public void selectNode(TreeNode node, boolean selected) {
        if (this.mSelectionModeEnabled) {
            node.setSelected(selected);
            this.toogleSelectionForNode(node, true);
        }

    }

    private void selectNode(TreeNode parent, boolean selected, boolean skipCollapsed) {
        parent.setSelected(selected);
        this.toogleSelectionForNode(parent, true);
        boolean toContinue = skipCollapsed ? parent.isExpanded() : true;
        if (toContinue) {
            Iterator var5 = parent.getChildren().iterator();

            while(var5.hasNext()) {
                TreeNode node = (TreeNode)var5.next();
                this.selectNode(node, selected, skipCollapsed);
            }
        }

    }

    private void toogleSelectionForNode(TreeNode node, boolean makeSelectable) {
        TreeNode.BaseNodeViewHolder holder = this.getViewHolderForNode(node);
        if (holder.isInitialized()) {
            this.getViewHolderForNode(node).toggleSelectionMode(makeSelectable);
        }

    }

    private TreeNode.BaseNodeViewHolder getViewHolderForNode(TreeNode node) {
        TreeNode.BaseNodeViewHolder viewHolder = node.getViewHolder();
        if (viewHolder == null) {
            try {
                Object object = this.defaultViewHolderClass.getConstructor(Context.class).newInstance(this.mContext);
                viewHolder = (TreeNode.BaseNodeViewHolder)object;
                node.setViewHolder(viewHolder);
            } catch (Exception var4) {
                throw new RuntimeException("Could not instantiate class " + this.defaultViewHolderClass);
            }
        }

        if (viewHolder.getContainerStyle() <= 0) {
            viewHolder.setContainerStyle(this.containerStyle);
        }

        if (viewHolder.getTreeView() == null) {
            viewHolder.setTreeViev(this);
        }

        return viewHolder;
    }

    private static void expand(final View v) {
        v.measure(-1, -2);
        final int targetHeight = v.getMeasuredHeight();
        v.getLayoutParams().height = 0;
        v.setVisibility(View.VISIBLE);
        Animation a = new Animation() {
            protected void applyTransformation(float interpolatedTime, Transformation t) {
                v.getLayoutParams().height = interpolatedTime == 1.0F ? -2 : (int)((float)targetHeight * interpolatedTime);
                v.requestLayout();
            }

            public boolean willChangeBounds() {
                return true;
            }
        };
        a.setDuration((long)((int)((float)targetHeight / v.getContext().getResources().getDisplayMetrics().density)));
        v.startAnimation(a);
    }

    private static void collapse(final View v) {
        final int initialHeight = v.getMeasuredHeight();
        Animation a = new Animation() {
            protected void applyTransformation(float interpolatedTime, Transformation t) {
                if (interpolatedTime == 1.0F) {
                    v.setVisibility(View.GONE);
                } else {
                    v.getLayoutParams().height = initialHeight - (int)((float)initialHeight * interpolatedTime);
                    v.requestLayout();
                }

            }

            public boolean willChangeBounds() {
                return true;
            }
        };
        a.setDuration((long)((int)((float)initialHeight / v.getContext().getResources().getDisplayMetrics().density)));
        v.startAnimation(a);
    }

    public void addNode(TreeNode parent, TreeNode nodeToAdd) {
        parent.addChild(nodeToAdd);
        if (parent.isExpanded()) {
            TreeNode.BaseNodeViewHolder parentViewHolder = this.getViewHolderForNode(parent);
            this.addNode(parentViewHolder.getNodeItemsView(), nodeToAdd);
        }

    }

    public void removeNode(TreeNode node) {
        if (node.getParent() != null) {
            TreeNode parent = node.getParent();
            int index = parent.deleteChild(node);
            if (parent.isExpanded() && index >= 0) {
                TreeNode.BaseNodeViewHolder parentViewHolder = this.getViewHolderForNode(parent);
                parentViewHolder.getNodeItemsView().removeViewAt(index);
            }
        }

    }
}
