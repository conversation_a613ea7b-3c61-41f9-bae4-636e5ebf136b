<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.twf.develophelpertools">

    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_MOCK_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <uses-permission android:name="android.permission.READ_LOGS" />
    <uses-permission android:name="android.permission.INJECT_EVENTS" />
    <uses-permission android:name ="android.permission.INTERNET"/>

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <uses-feature
        android:glEsVersion="0x00030000"
        android:required="true" />

    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />

    <!-- Window -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW" />

    <!--   //暂时去掉  影响直播android:requestLegacyExternalStorage="true"-->

    <application>
        <activity
            tools:targetApi="q"
            android:forceDarkAllowed="false"
            android:configChanges="orientation|keyboardHidden"
            android:name=".activity.TransferCenterKitActivity"
            android:screenOrientation="portrait" />

        <provider
            android:name=".chunk.internal.data.ChuckContentProvider"
            android:authorities="${applicationId}.chuck.provider"
            android:exported="false" />


        <activity
            tools:targetApi="q"
            android:forceDarkAllowed="false"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden"
            android:name=".chunk.internal.ui.MainActivity"
            android:label="@string/kit_chuck_name"
            android:launchMode="singleTask"
            android:taskAffinity="com.readystatesoftware.chuck.task" />

        <activity
            tools:targetApi="q"
            android:forceDarkAllowed="false"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden"
            android:name=".chunk.internal.ui.TransactionActivity"
            android:parentActivityName=".chunk.internal.ui.MainActivity" />

        <activity
            tools:targetApi="q"
            android:forceDarkAllowed="false"
            android:name=".activity.UserInfoActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden" />
        <activity
            tools:targetApi="q"
            android:forceDarkAllowed="false"
            android:name=".media.PreviewMedialActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden" />

        <service
            tools:targetApi="q"
            android:forceDarkAllowed="false"
            android:name=".chunk.internal.support.ClearTransactionsService"
            android:exported="false" />

        <service
            tools:targetApi="q"
            android:forceDarkAllowed="false"
            android:name=".service.MockClickService"
            android:exported="false" />

        <activity
            tools:targetApi="q"
            android:forceDarkAllowed="false"
            android:configChanges="orientation|keyboardHidden"
            android:name=".zxing.activity.CaptureActivity"
            android:screenOrientation="portrait" />
        <activity
            tools:targetApi="q"
            android:forceDarkAllowed="false"
            android:launchMode="singleTask"
            android:name=".activity.EnvironmentChangeActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden" />
        <activity
            tools:targetApi="q"
            android:forceDarkAllowed="false"
            android:name=".activity.MainPanelActivity"
            android:screenOrientation="portrait"
            android:launchMode="singleTask"
            android:configChanges="orientation|keyboardHidden"
             />

        <activity
            android:name=".activity.KITUITransparentActivity"
            android:launchMode="singleTask"
            android:theme="@style/Kit_UI_Theme.AppCompat.Translucent"/>

    </application>
</manifest>
