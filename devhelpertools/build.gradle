import java.util.function.Consumer

//apply from: rootProject.file('bzl-push.gradle')

apply from: "$rootDir/common.gradle"
apply from: "$rootDir/library.gradle"

android {
    namespace = "com.twf.develophelpertools"
}


dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    api project(path: ':BaseLibrary')
//    api project(path: ':MainModule')

    project.ext.devHelperToolsdencyId.each {

        k, v -> implementation v;

    }
}

//遍历项目里面所有的第三方的资源
gradle.buildFinished {

    Set<String> result = new HashSet<>();

    //遍历所有的project模块
    getGradle().allprojects(new Action<Project>() {
        @Override
        void execute(Project project) {


            println "====projectName=" + project.getName()

            ConfigurationContainer configurations = project.getConfigurations()

            configurations.forEach(new Consumer<Configuration>() {
                @Override
                void accept(Configuration files) {
                    println "name=" + files.getName()
                    def name = files.getName()
                    if (name != null) {
                        def dep = files.getDependencies()
                        dep.forEach(new Consumer<Dependency>() {
                            @Override
                            void accept(Dependency dependency) {
                                if (dependency.group != null) {
                                    def dependencyInfo = dependency.group + ":" + dependency.name + ":" + dependency.version
                                    result.add(dependencyInfo)
                                }
                            }
                        })
                    }
                }
            })
        }
    })


    StringBuilder value = new StringBuilder();

    result.forEach(new Consumer<String>() {
        @Override
        void accept(String s) {
            value.append(s);
            value.append("###");
            println s
        }
    })

    File file = new File("${projectDir}/src/main/java/com/twf/develophelpertools/LibraryInfo.java");
    file.write("package com.twf.develophelpertools;\n" +
            "\n" +
            "/**\n" +
            " * create by guofeng\n" +
            " * date on 2021/10/20\n" +
            " */\n" +
            "\n" +
            "public class LibraryInfo {\n" +
            "\n" +
            "   public  static String libraryInfo =\" " + value + "\"; "
            +
            "\n" + "\n" +
            "} ")

    print("filePath=${file.getAbsolutePath()}")

}

//只有当前模块
//project.afterEvaluate {
//    project ->
//        if (project.state.failure) {
//            println "Evaluation of $project FAILED"
//        } else {
//            println "Evaluation of $project succeeded"
//        }
//}

//只有当前模块
//afterEvaluate {
//    project ->
//        if (project.state.failure) {
//            println "Evaluation of $project FAILED"
//        } else {
//            println "Evaluation of $project succeeded"
//        }
//}
//所有的模块
//gradle.afterProject { project ->
//    if (project.state.failure) {
//        println "Evaluation of $project FAILED"
//    } else {
//        println "Evaluation of $project succeeded"
//    }
//}

