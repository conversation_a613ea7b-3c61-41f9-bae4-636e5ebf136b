project.ext {

    compileSdkVersions = 34
    buildToolsVersions = "29.0.0"
    minSdkVersions = 24
    targetSdkVersions = 34
    versionCodes = 1
    versionNames = "1.0"

    multiDexEnabled = true


    //设置 Application的配置熊熊
    def setAppDefaultConfig = {
        project ->
            project.apply plugin: 'com.android.application'
            setAndroidConfig project.android

            project.android.defaultConfig {
                applicationId "com.android.gradle"
            }
    }

    //设置 第三方库的配置熊熊
    def setLibraryDefaultConfig = {
        project ->
            project.apply plugin: "com.android.library"
            setAndroidConfig project.android

    }


    setAndroidConfig = {

        android ->

            android.compileSdkVersion project.compileSdkVersions
            android.buildToolsVersion project.buildToolsVersions

            android.defaultConfig {

                minSdkVersion project.minSdkVersions
                targetSdkVersion project.targetSdkVersions
                versionCode project.versionCodes
                versionName project.versionNames
                testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
                multiDexEnabled project.multiDexEnabled
                consumerProguardFiles "consumer-rules.pro"
            }

            android.signingConfigs {
                release {
//                    storeFile file('/Users/<USER>/boss/kit/keystore2')
                    storeFile file('/Users/<USER>/Downloads/Code/MyCode/sunflower/keystore2')
                    storePassword 'gf1990'
                    keyAlias 'key2'
                    keyPassword 'gf1990'
                }
            }


            android.compileOptions {
                sourceCompatibility JavaVersion.VERSION_1_8
                targetCompatibility JavaVersion.VERSION_1_8
            }


    }


    def projectName = project.getName()
    def isAppModule = projectName == 'app'


    if (isAppModule) {
        setAppDefaultConfig project
    } else {
        setLibraryDefaultConfig project
    }


}