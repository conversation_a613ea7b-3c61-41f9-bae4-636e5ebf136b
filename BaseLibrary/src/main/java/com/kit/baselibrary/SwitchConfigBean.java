package com.kit.baselibrary;

import android.widget.CompoundButton;

/**
 * create by guofeng
 * date on 2021/8/4
 */

public class SwitchConfigBean {

    private String switchKey;

    private boolean defaultIsSelect;

    private String switchName;

    private CompoundButton.OnCheckedChangeListener onChangeListener;

    public SwitchConfigBean(boolean defaultIsSelect, String switchName, CompoundButton.OnCheckedChangeListener onChangeListener) {
        this.defaultIsSelect = defaultIsSelect;
        this.switchName = switchName;
        this.onChangeListener = onChangeListener;
    }

    public SwitchConfigBean(String switchKey, boolean defaultIsSelect, String switchName, CompoundButton.OnCheckedChangeListener onChangeListener) {
        this.switchKey = switchKey;
        this.defaultIsSelect = defaultIsSelect;
        this.switchName = switchName;
        this.onChangeListener = onChangeListener;
    }

    public String getSwitchKey() {
        return switchKey;
    }

    public boolean isDefaultIsSelect() {
        return defaultIsSelect;
    }

    public String getSwitchName() {
        return switchName;
    }

    public CompoundButton.OnCheckedChangeListener getOnChangeListener() {
        return onChangeListener;
    }
}