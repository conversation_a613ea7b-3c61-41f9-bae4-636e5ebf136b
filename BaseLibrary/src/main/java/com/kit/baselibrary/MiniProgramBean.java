package com.kit.baselibrary;

/**
 * create by guofeng
 * date on 2021/8/5
 */

public class MiniProgramBean {

    public static final int RELEASE = 0;

    public static final int DEVELOP = 1;

    public static final int EXPERIENCE = 2;


    private String getCurrentType;


    private OnMiniProgramChangeCallBack onMiniProgramChangeCallBack;


    public void setGetCurrentType(String getCurrentType) {
        this.getCurrentType = getCurrentType;
    }

    public void setOnMiniProgramChangeCallBack(OnMiniProgramChangeCallBack onMiniProgramChangeCallBack) {
        this.onMiniProgramChangeCallBack = onMiniProgramChangeCallBack;
    }

    public String getGetCurrentType() {
        return getCurrentType;
    }

    public OnMiniProgramChangeCallBack getOnMiniProgramChangeCallBack() {
        return onMiniProgramChangeCallBack;
    }
}