package com.kit.baselibrary;

/**
 * create by guofeng
 * date on 2021/8/9
 */

public class MessageLoginBean {

    private Class<?> activityClass;

    private Class<?> fragmentClass;

    public void setActivityClass(Class<?> activityClass) {
        this.activityClass = activityClass;
    }

    public void setFragmentClass(Class<?> fragmentClass) {
        this.fragmentClass = fragmentClass;
    }

    public Class<?> getActivityClass() {
        return activityClass;
    }

    public Class<?> getFragmentClass() {
        return fragmentClass;
    }
}