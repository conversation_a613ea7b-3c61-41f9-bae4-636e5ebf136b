package com.kit.baselibrary;

import java.util.List;

/**
 * create by guofeng
 * date on 2021/8/14
 */

public class APiFilterBean {

    private String groupName;

    private List<APiUrlBean> aPiUrlList;


    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public void setaPiUrlList(List<APiUrlBean> aPiUrlList) {
        this.aPiUrlList = aPiUrlList;
    }

    public String getGroupName() {
        return groupName;
    }

    public List<APiUrlBean> getaPiUrlList() {
        return aPiUrlList;
    }

    public static class APiUrlBean {

        private String likeUrl;

        private String unLikeUrl;


        public void setLikeUrl(String likeUrl) {
            this.likeUrl = likeUrl;
        }

        public void setUnLikeUrl(String unLikeUrl) {
            this.unLikeUrl = unLikeUrl;
        }

        public String getLikeUrl() {
            return likeUrl;
        }

        public String getUnLikeUrl() {
            return unLikeUrl;
        }
    }

}