package com.kit.baselibrary;


import java.util.List;

import okhttp3.Headers;
import okhttp3.Request;
import okhttp3.ResponseBody;

/**
 * create by guofeng
 * date on 2021/7/27
 */

public interface AppCallBackItf {

    /**
     * 打开htmp5的点击事件,方便测试内置的webViewActivity
     *
     * @param url
     */
    void openHtmp5UrlListener(String url);


    /**
     * 获得app相关信息
     *
     * @return
     */
    AppInfoBean getAppInfo();

    /**
     * 获得埋点的接口集合，kit 判断是埋点的接口，显示到埋点列表里面
     *
     * @return
     */
    List<String> getAnalysisUrlList();

    /**
     * mock数据配置数据
     *
     * @return
     */
    MockConfigBean getMockConfigBean();

    /**
     * 获得小程序配置
     *
     * @return
     */
    MiniProgramBean getMiniProgramBean();

    /**
     * 获得上报API配置信息
     *
     * @return
     */
    ApiReportConfigBean getApiReportConfigBean();

    /**
     * 获得应用层的webViewActivity集合，用于判断是否h5界面,h5界面会加哥水印标志
     *
     * @return
     */
    List<Class<?>> getWebViewActivityList();


    /**
     * 获得跳过广告的集合
     *
     * @return
     */
    List<AdvertiseJumpBean> getAdvertiseJumpList();


    /**
     * 获得切换环境配置信息
     *
     * @return
     */
    EnvironmentConfigBean getEnvironmentConfigBean();


    /**
     * 获得动态配置的点击按钮
     *
     * @return
     */
    List<ButtonConfigBean> getButtonConfigList();


    /**
     * 动态配置点击开关
     *
     * @return
     */
    List<SwitchConfigBean> getSwitchConfigList();

    /**
     * 动态获得详情配置
     *
     * @return
     */
    List<DetailConfigBean> getDetailConfigList();

    /**
     * 获得 分组 api 筛选集合
     *
     * @return
     */
    APiFilterConfigBean getGroupApiFilterList();

    /**
     * 获得APM配置
     *
     * @return
     */
    APMConfigBean getApmConfigInfo();


    /**
     * 获得解密文件
     *
     * @param encryText
     * @return
     */
    String decryHttpPostParam(String encryText, String url);


    /**
     * 解密httpGet
     *
     * @param encryText
     * @return
     */
    String decryHttpGetParams(String encryText, String url);



    String getReportDecryParams(String encryText, String url);



    String getDecryBgAction(String encryText,String url);


    /**
     * 是否下载的请求
     *
     * @param header
     * @param url
     * @return
     */
    boolean isInterceptRequest(Headers header, ResponseBody responseBody, String url);


    MessageLoginBean getMessageCodeInfo();

    /**
     * 自动输入测试验证码
     */
    void onAutoInputMessageCode(boolean unLimitSendCode, String encryUrl);


    /**
     * 上报接口返回数据
     *
     * @param request
     */
    void reportHttpResponse(Request request);


    /**
     * 获取扩展配置列表
     * @return
     */
    List<IExtraBean> getExtraConfigList();

}