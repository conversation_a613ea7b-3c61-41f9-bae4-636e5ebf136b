package com.kit.baselibrary;

import java.util.List;

/**
 * create by guofeng
 * date on 2021/8/16
 */

public class APiFilterConfigBean {

    private int defaultSelectIndex;

    private List<APiFilterBean> aPiFilterBeanList;

    public void setDefaultSelectIndex(int defaultSelectIndex) {
        this.defaultSelectIndex = defaultSelectIndex;
    }

    public void setaPiFilterBeanList(List<APiFilterBean> aPiFilterBeanList) {
        this.aPiFilterBeanList = aPiFilterBeanList;
    }

    public int getDefaultSelectIndex() {
        return defaultSelectIndex;
    }

    public List<APiFilterBean> getaPiFilterBeanList() {
        return aPiFilterBeanList;
    }
}