package com.kit.baselibrary;

import android.view.View;

import java.util.List;

/**
 * create by guofeng
 * date on 2021/8/5
 */

public class EnvironmentConfigBean {


    private List<EnvironmentInfoBean> configList;

    private EnvironmentInfoBean currentEnvironment;

    private String zpTag;

    private ZpTagCallBack zpTagCallBack;

    private EnvironmentChangeCallBack environmentChangeCallBack;

    private View.OnClickListener onResetListener;

    public List<EnvironmentInfoBean> getConfigList() {
        return configList;
    }

    public EnvironmentInfoBean getCurrentEnvironment() {
        return currentEnvironment;
    }

    public String getZpTag() {
        return zpTag;
    }

    public ZpTagCallBack getZpTagCallBack() {
        return zpTagCallBack;
    }

    public EnvironmentChangeCallBack getEnvironmentChangeCallBack() {
        return environmentChangeCallBack;
    }

    public View.OnClickListener getOnResetListener() {
        return onResetListener;
    }

    public void setConfigList(List<EnvironmentInfoBean> configList) {
        this.configList = configList;
    }

    public void setCurrentEnvironment(EnvironmentInfoBean currentEnvironment) {
        this.currentEnvironment = currentEnvironment;
    }

    public void setZpTag(String zpTag) {
        this.zpTag = zpTag;
    }

    public void setZpTagCallBack(ZpTagCallBack zpTagCallBack) {
        this.zpTagCallBack = zpTagCallBack;
    }

    public void setEnvironmentChangeCallBack(EnvironmentChangeCallBack environmentChangeCallBack) {
        this.environmentChangeCallBack = environmentChangeCallBack;
    }

    public void setOnResetListener(View.OnClickListener onResetListener) {
        this.onResetListener = onResetListener;
    }
}