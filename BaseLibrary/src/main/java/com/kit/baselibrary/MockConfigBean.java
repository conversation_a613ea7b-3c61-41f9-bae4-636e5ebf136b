package com.kit.baselibrary;

import java.util.List;

/**
 * create by guofeng
 * date on 2021/8/5
 */

public class MockConfigBean {

    private List<Class<?>> mockUrlClassList;

    private String mockReplacePrefixUrl;

    private List<String> mockFilter;

    public List<Class<?>> getMockUrlClassList() {
        return mockUrlClassList;
    }

    public String getMockReplacePrefixUrl() {
        return mockReplacePrefixUrl;
    }

    public List<String> getMockFilter() {
        return mockFilter;
    }

    public void setMockUrlClassList(List<Class<?>> mockUrlClassList) {
        this.mockUrlClassList = mockUrlClassList;
    }

    public void setMockReplacePrefixUrl(String mockReplacePrefixUrl) {
        this.mockReplacePrefixUrl = mockReplacePrefixUrl;
    }

    public void setMockFilter(List<String> mockFilter) {
        this.mockFilter = mockFilter;
    }
}