package com.kit.baselibrary;

import android.app.Application;

import okhttp3.Interceptor;

/**
 * create by guofeng
 * date on 2021/7/27
 */
public interface KitItf {

    public void install(Application application, AppCallBackItf appCallBackItf);

    public Interceptor getWeakConnectInterceptor();

//    public Interceptor getQaMockInterceptor();
//
//    public Interceptor getChuckInterceptor();

    public Interceptor getAnalyInterceptor();

    public Interceptor getReportInterceptor();

    public void inflateMockLocation(Class<?> tClass, Object object);


    public void openLeakCanary();
}
