/*
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.zl.collection.yuchaofei.anti;

import android.content.Context;

import com.zl.collection.yuchaofei.util.DeviceUtils;

import java.util.List;

//public class ElfinChecker implements Runnable {
//    private static String[] KEYS = {"cyjh"};
//
//    private String[] mKeys = KEYS;
//    private Callback mCallback;
//    private Context mContext;
//
//    public ElfinChecker(String[] keys, Context context, Callback callback) {
//        if (keys != null) mKeys = keys;
//        mContext = context;
//        mCallback = callback;
//        new Thread(this).start();
//    }
//
//    @Override
//    public void run() {
//        try {
//            if (DeviceUtils.isRoot()) {
//                if (!findElfin()) {
//                    mCallback.onNotExist(true);
//                    registerPackageAdded();
//                }
//            } else {
//                mCallback.onNotExist(false);
//            }
//        } catch (Throwable e) {
//            CrashReport.postCatchedException(e);
//        }
//    }
//
//    private boolean findElfin() {
//        Intent intent = new Intent();
//        intent.addCategory(Intent.CATEGORY_LAUNCHER);
//        intent.setAction(Intent.ACTION_MAIN);
//        List<ResolveInfo> list = mContext.getPackageManager().queryIntentActivities(intent, 0);
//        for (ResolveInfo resolveInfo : list) {
//            if (isElfin(resolveInfo)) {
//                return true;
//            }
//        }
//        return false;
//    }
//
//    private boolean isElfin(ResolveInfo resolveInfo) {
//        if (resolveInfo != null && resolveInfo.activityInfo != null) {
//            String packageName = resolveInfo.activityInfo.packageName;
//            String activityName = resolveInfo.activityInfo.name;
//            for (String key : mKeys) {
//                if (activityName.contains(key) || packageName.contains(key)) {
//                    try {
//                        JSONObject jsonArray = new JSONObject();
//                        jsonArray.put("activity", activityName);
//                        jsonArray.put("package", packageName);
//                        mCallback.onFind(key, jsonArray.toString());
//                        return true;
//                    } catch (JSONException e) {
//                        CrashReport.postCatchedException(e);
//                    }
//                }
//            }
//        }
//        return false;
//    }
//
//    private void registerPackageAdded() {
//        BroadcastReceiver broadcastReceiver = new BroadcastReceiver() {
//            @Override
//            public void onReceive(Context context, Intent intent) {
//                try {
//                    Intent intent2 = new Intent();
//                    intent2.setPackage(intent.getData().getSchemeSpecificPart());
//                    intent2.addCategory(Intent.CATEGORY_LAUNCHER);
//                    intent2.setAction(Intent.ACTION_MAIN);
//                    List<ResolveInfo> list = context.getPackageManager().queryIntentActivities(intent2, 0);
//                    for (ResolveInfo resolveInfo : list) {
//                        if (isElfin(resolveInfo)) {
//                            return;
//                        }
//                    }
//                } catch (Throwable e) {
//                    CrashReport.postCatchedException(e);
//                }
//            }
//        };
//
//        IntentFilter intentFilter = new IntentFilter();
//        intentFilter.addAction(Intent.ACTION_PACKAGE_ADDED);
//        intentFilter.addAction(Intent.ACTION_PACKAGE_INSTALL);
//        intentFilter.addDataScheme("package");
//        mContext.registerReceiver(broadcastReceiver, intentFilter);
//    }
//
//    public interface Callback {
//        void onFind(String key, String info);
//
//        void onNotExist(boolean isRoot);
//    }
//}
