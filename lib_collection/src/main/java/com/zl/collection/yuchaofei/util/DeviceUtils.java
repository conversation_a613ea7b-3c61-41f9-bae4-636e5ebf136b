package com.zl.collection.yuchaofei.util;

import java.io.File;

public class DeviceUtils {
    public static boolean isRoot() {
        String[] sus = {"/su", "/su/bin/su", "/sbin/su", "/data/local/xbin/su", "/data/local/bin/su", "/data/local/su", "/system/xbin/su", "/system/bin/su", "/system/sd/xbin/su", "/system/bin/failsafe/su", "/system/bin/cufsdosck", "/system/xbin/cufsdosck", "/system/bin/cufsmgr", "/system/xbin/cufsmgr", "/system/bin/cufaevdd", "/system/xbin/cufaevdd", "/system/bin/conbb", "/system/xbin/conbb"};
        for (String s : sus) {
            if (new File(s).exists()) {
                return true;
            }
        }
        return false;
    }


    public static boolean isHook(){
        return false;
    }
}
