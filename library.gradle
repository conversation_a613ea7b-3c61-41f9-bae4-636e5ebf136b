project.ext {

    squareupokhttp3Version = "3.11.0"

    constraintlayoutVersion = "1.1.3"

    junitVersion = "4.12"

    androidxTestJunit = "1.1.1"

    androidxrulesVersion = "1.1.1"

    androidxuiautomatorVersion = "2.2.0"

    appcompatVersion = "1.1.0"

    espressoCoreVersion = "3.2.0"

    androidXPreferenceVersion = "1.1.0"

    annotationVersion = "1.1.0"

    recyclerviewVersion = "1.0.0"

    squareupokhttpVersion = "3.11.0"

    facebookfrescoVersion = "1.13.0"

    gsonVersion = "2.7"

    amaplocationVersion = "5.6.2"

    amap3dmapVersion = "7.9.1"

    amapsearchVersion = "7.1.0"

    androidmaterialVersion = "1.0.0"

    androidxlegacysupportv4Version = "1.0.0"

    imagezoomLibraryVersion = "1.0.4"

    edsergeevTextFabVersion = "1.0.0"

    photoViewVersion = "2.0.0"

    cupboardVersion = "2.2.0"

    yokeywordFragmentationxVersion = "1.0.2"

    yokeywordSwipebackVersion = "1.0.2"

    yokeywordeventbusactivityVersion = "1.1.0"

    greenroboteventbusVersion = "3.2.0"

    androidxmultidexVersion = "2.0.0"

    squareupPicassoVersion = "2.5.2"

    bumptechGlideVersion = "4.9.0"

    glide = "4.12.0"

    arouterCompilerVersion = "1.1.4"

    arouterApiVersion = "1.3.1"

    butterknifeCompilerVersion = "9.0.0"

    butterknifeVersion = "9.0.0"

    zxing = "3.3.0"

//    apm = "1.3.29"

    lifecycle_extensions = "2.2.0"

    lifecycle_runtime = "2.2.0"

    support_recyclerview = "1.0.0"


    support_cardview = "1.0.0"


    //APP MODEL下面的依赖
    appDependencyId = [
            appcompat       : "androidx.appcompat:appcompat:${project.appcompatVersion}",
            constraintlayout: "androidx.constraintlayout:constraintlayout:${project.constraintlayoutVersion}",
            okhttp          : "com.squareup.okhttp3:okhttp:${project.squareupokhttpVersion}",
            multidex        : "androidx.multidex:multidex:${project.androidxmultidexVersion}"

    ]

    //BaseLibrary MODEL下面的依赖
    baseLibrarydencyId = [
            squareup: "com.squareup.okhttp3:okhttp:${project.squareupokhttpVersion}"
    ]


    //devHelperTools MODEL下面的依赖
    devHelperToolsdencyId = [
            appcompat       : "androidx.appcompat:appcompat:${project.appcompatVersion}",
            constraintlayout: "androidx.constraintlayout:constraintlayout:${project.constraintlayoutVersion}",
            okhttp          : "com.squareup.okhttp3:okhttp:${project.squareupokhttpVersion}",
            recyclerview    : "androidx.recyclerview:recyclerview:${project.recyclerviewVersion}",
            legacy          : "androidx.legacy:legacy-support-v4:${project.androidxlegacysupportv4Version}",
            material        : "com.google.android.material:material:${project.androidmaterialVersion}",
            cupboard        : "nl.qbusict:cupboard:${project.cupboardVersion}",
            gson            : "com.google.code.gson:gson:${project.gsonVersion}",
            location        : "com.amap.api:location:${project.amaplocationVersion}",
            d3map           : "com.amap.api:3dmap:${project.amap3dmapVersion}",
            search          : "com.amap.api:search:${project.amapsearchVersion}",
            fresco          : "com.facebook.fresco:fresco:${project.facebookfrescoVersion}",
            zxing           : "com.google.zxing:core:${project.zxing}"
//            apm             : "com.hpbr.bosszhipin:module-apm:${project.apm}"
    ]

    //devHelperToolsNoDencyId MODEL下面的依赖
    devHelperToolsNodencyId = [
            appcompat: "androidx.appcompat:appcompat:${project.appcompatVersion}",
            okhttp   : "com.squareup.okhttp3:okhttp:${project.squareupokhttpVersion}",

    ]


    //libmatisse MODEL下面的依赖
    libMatisswdencyId = [
            appcompat   : "androidx.appcompat:appcompat:${project.appcompatVersion}",
            annotation  : "androidx.annotation:annotation:${project.annotationVersion}",
            recyclerview: "androidx.recyclerview:recyclerview:${project.recyclerviewVersion}",
            imagezoom   : "it.sephiroth.android.library.imagezoom:library:${project.imagezoomLibraryVersion}",
    ]


    //MainModel  model下面的依赖
    mainModuledencyId = [

            appcompat          : "androidx.appcompat:appcompat:${project.appcompatVersion}",
            constraintlayout   : "androidx.constraintlayout:constraintlayout:${project.constraintlayoutVersion}",
            preference         : "androidx.preference:preference:${project.androidXPreferenceVersion}",


            lifecycleruntime   : "androidx.lifecycle:lifecycle-runtime:${project.lifecycle_runtime}",
            lifecycleextensions: "androidx.lifecycle:lifecycle-extensions:${project.lifecycle_extensions}",


            glide              : "com.github.bumptech.glide:glide:${project.glide}",
            recyclerview       : "androidx.recyclerview:recyclerview:${project.support_recyclerview}",
            design             : "com.google.android.material:material:${project.androidmaterialVersion}",
            cardview           : "androidx.cardview:cardview:${project.support_cardview}",

    ]

}





