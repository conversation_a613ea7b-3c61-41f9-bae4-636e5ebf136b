/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.leetcode.algorithm

/**
 * 二叉树的最大深度
 */
class TreeNodeMaxDepth {
    companion object {
        @JvmStatic
        fun main(args: Array<String>) {

            val root = TreeNode2(3)
            val left = TreeNode2(9)
            val right = TreeNode2(20)
            root.left = left
            root.right = right
            right.left = TreeNode2(15)
            right.right = TreeNode2(7)
            println(maxDepth(root))
        }

        private fun maxDepth(root:TreeNode2?):Int{
            if (root == null){
                return 0
            }
            val leftDepth = maxDepth(root.left)
            val rightDepth = maxDepth(root.right)
            return Math.max(leftDepth,rightDepth) + 1
        }
    }
}
class TreeNode2(var value: Int) {
    var left: TreeNode2? = null
    var right: TreeNode2? = null
}















