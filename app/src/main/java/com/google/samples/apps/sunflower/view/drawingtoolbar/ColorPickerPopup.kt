package com.google.samples.apps.sunflower.view.drawingtoolbar

import android.content.Context
import android.view.View
import com.google.samples.apps.sunflower.R
import com.google.samples.apps.sunflower.databinding.KlineDialogColorPickerBinding
import com.lxj.xpopup.core.AttachPopupView

/**
 * 颜色选择器弹窗
 */
class ColorPickerPopup(context: Context) : AttachPopupView(context) {

    private val mBinding: KlineDialogColorPickerBinding by lazy {
        KlineDialogColorPickerBinding.bind(popupImplView)
    }

    private var onColorPickListener: ((adapter: ColorGridAdapter, view: View, position: Int, item: ColorCell) -> Unit)? = null

    override fun getImplLayoutId(): Int = R.layout.kline_dialog_color_picker

    override fun onCreate() {
        super.onCreate()
//        popupInfo.xPopupCallback = object : XPopupCallbackImpl() {
//
//        }
        mBinding.rvGrid.setOnColorPickListener { adapter, view, position, colorItem ->
            onColorPickListener?.invoke(adapter, view, position, colorItem)
            dismiss()
        }
    }

    fun setOnColorPickListener(listener: (adapter: ColorGridAdapter, view: View, position: Int, item: ColorCell) -> Unit): ColorPickerPopup {
        onColorPickListener = listener
        return this
    }

}