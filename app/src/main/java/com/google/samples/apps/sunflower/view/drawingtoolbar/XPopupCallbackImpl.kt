/*
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.view.drawingtoolbar

import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.interfaces.XPopupCallback

open class XPopupCallbackImpl: XPopupCallback {
    override fun onCreated(popupView: BasePopupView?) {
        
        
    }

    override fun beforeShow(popupView: BasePopupView?) {

    }

    override fun onShow(popupView: BasePopupView?) {

    }

    override fun onDismiss(popupView: BasePopupView?) {

    }

    override fun beforeDismiss(popupView: BasePopupView?) {

    }

    override fun onBackPressed(popupView: BasePopupView?): Boolean {
        return false
    }

    override fun onKeyBoardStateChanged(popupView: BasePopupView?, height: Int) {

    }

    override fun onDrag(popupView: BasePopupView?, value: Int, percent: Float, upOrLeft: Boolean) {

    }

    override fun onClickOutside(popupView: BasePopupView?) {

    }
}