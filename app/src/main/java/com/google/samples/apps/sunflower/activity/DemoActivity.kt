/*
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.activity

import android.annotation.SuppressLint
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowInsets
import android.view.WindowInsetsController
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.samples.apps.sunflower.R
import com.google.samples.apps.sunflower.base.UiEvent
import com.google.samples.apps.sunflower.base.UiState
import com.google.samples.apps.sunflower.databinding.ActivityDemoBinding
import com.google.samples.apps.sunflower.view.SmartPopupWindow
import com.google.samples.apps.sunflower.view.drawingtoolbar.ColorPickerPopup
import com.google.samples.apps.sunflower.view.drawingtoolbar.LineDashedPickerPopup
import com.google.samples.apps.sunflower.view.drawingtoolbar.LineThicknessPickerPopup
import com.google.samples.apps.sunflower.view.drawingtoolbar.ToolItem
import com.google.samples.apps.sunflower.view.drawingtoolbar.XPopupCallbackImpl
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.enums.PopupPosition
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

@AndroidEntryPoint
class DemoActivity : AppCompatActivity() {

    private var isLandscape = false

    private val mBinding: ActivityDemoBinding by lazy {
        ActivityDemoBinding.inflate(layoutInflater)
    }

    private val viewModel: DemoViewModel by viewModels<DemoViewModel>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
//        setFullScreen()
        setContentView(mBinding.root)
        initView()
    }

    private fun toggleScreenOrientation() {
        isLandscape = !isLandscape

        requestedOrientation = if (isLandscape) {
            ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        } else {
            ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }
    }

    @SuppressLint("RestrictedApi")
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)

        when (newConfig.orientation) {
            Configuration.ORIENTATION_LANDSCAPE -> {
                // 横屏时的处理
                Log.d("zl_log", "横屏时的处理")
            }

            Configuration.ORIENTATION_PORTRAIT -> {
                // 竖屏时的处理
                Log.d("zl_log", "竖屏时的处理")
            }
        }
    }

    // 保存状态以便旋转后恢复
    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        Log.d("zl_log", "onSaveInstanceState")
        outState.putBoolean("isLandscape", isLandscape)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        isLandscape = savedInstanceState.getBoolean("isLandscape", false)
        Log.d("zl_log", "onRestoreInstanceState:isLandscape=${isLandscape}")
    }

    private fun initView() {
        initToolbar()
        mBinding.btnLandscape.setOnClickListener {
            toggleScreenOrientation()
        }
        mBinding.btnInput.setOnClickListener {
            showPopup(mBinding.btnInput)
        }

        mBinding.btnToolbar.setOnClickListener {
            mBinding.dlToolbar.visibility = if (mBinding.dlToolbar.visibility != View.VISIBLE) View.VISIBLE else View.INVISIBLE
        }
    }

    @SuppressLint("InflateParams")
    fun showPopup(anchorView: View) {
        // 创建SmartPopupWindow实例
        val popupWindow = SmartPopupWindow(
            this,
            anchorView,  // 锚点View
            LayoutInflater.from(this).inflate(R.layout.popup_demo, null),
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )

        // 显示PopupWindow
        popupWindow.showAsDropDown(anchorView, Gravity.NO_GRAVITY, 0, 0)
    }

    private fun setFullScreen() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // 1. 设置内容延伸到系统栏后面
            window.setDecorFitsSystemWindows(false)

            // 2. 获取 WindowInsetsController
            val controller = window.insetsController

            // 3. 隐藏状态栏和导航栏
            controller?.hide(WindowInsets.Type.systemBars())

            // 4. 设置系统栏行为 (可选)
            controller?.systemBarsBehavior =
                WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        } else {
            // 兼容旧版本
            window.decorView.systemUiVisibility = (
                    View.SYSTEM_UI_FLAG_FULLSCREEN
                            or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                            or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                            or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                            or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                            or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    )
        }
    }

    private fun initToolbar() {

        mBinding.viewToolbar.setOnToolClickListener { adapter, view, position, item ->
            Toast.makeText(this, "点击了按钮~ position=${position}", Toast.LENGTH_SHORT).show()

            when (item) {
                is ToolItem.PaintColor -> {
//                    val locations = IntArray(2)
//                    view.getLocationOnScreen(locations)
                    XPopup.Builder(this)
                        .popupPosition(PopupPosition.Top)
                        .atView(view)
                        .isCenterHorizontal(true)
//                        .atPoint(PointF(locations[0].toFloat(), locations[1].toFloat()))
                        .isViewMode(true)
                        .hasShadowBg(false)
                        .setPopupCallback(object : XPopupCallbackImpl() {
                            override fun onDismiss(popupView: BasePopupView?) {
                                item.expand = false
                                adapter.notifyItemChanged(position)
                            }
                        })
                        .asCustom(ColorPickerPopup(this).setOnColorPickListener() { adapter, view, position, colorCell ->
                            item.color = colorCell.color
//                            adapter.notifyItemChanged(position)
                        })
                        .show()
                }

                is ToolItem.LineThickness -> {
//                    val locations = IntArray(2)
//                    view.getLocationOnScreen(locations)
                    XPopup.Builder(this)
                        .popupPosition(PopupPosition.Top)
//                        .atPoint(PointF(locations[0].toFloat(), locations[1].toFloat()))
                        .atView(view)
                        .isCenterHorizontal(true)
                        .isViewMode(true)
                        .hasShadowBg(false)
                        .setPopupCallback(object : XPopupCallbackImpl() {
                            override fun onDismiss(popupView: BasePopupView?) {
                                item.expand = false
                                adapter.notifyItemChanged(position)
                            }
                        })
                        .asCustom(LineThicknessPickerPopup(this).setOnItemListener() { adapter, view, position, itemBean ->
                            item.iconResId = itemBean.icon
                        })
                        .show()
                }

                is ToolItem.LineDashed -> {
//                    val locations = IntArray(2)
//                    view.getLocationOnScreen(locations)
                    XPopup.Builder(this)
                        .popupPosition(PopupPosition.Top)
//                        .atPoint(PointF(locations[0].toFloat(), locations[1].toFloat()))
                        .atView(view)
                        .isCenterHorizontal(true)
                        .isViewMode(true)
                        .hasShadowBg(false)
                        .setPopupCallback(object : XPopupCallbackImpl() {
                            override fun onDismiss(popupView: BasePopupView?) {
                                item.expand = false
                                adapter.notifyItemChanged(position)
                            }
                        })
                        .asCustom(LineDashedPickerPopup(this).setOnItemListener() { adapter, view, position, itemBean ->
                            item.iconResId = itemBean.icon
                        })
                        .show()
                }

                is ToolItem.Lock -> {
                    item.open = !item.open
                    adapter.notifyItemChanged(position)
                }

                is ToolItem.Delete -> {
                    adapter.notifyItemChanged(position)
                }

                else -> {}
            }
        }
    }
}

class DemoViewModel : ViewModel() {
    private val _inputState = MutableStateFlow<DemoContract.InputTypeState>(DemoContract.InputTypeState.WithSign)
    val inputState = _inputState.asStateFlow()

    private val _event = MutableSharedFlow<UiEvent>()
    val event = _event.asSharedFlow()
    fun sendEvent(event: DemoContract.ClickEvent) {
        viewModelScope.launch {
            _event.emit(event)
        }
    }

//    fun changeInputType() {
//        _inputState.value = when (_inputState.value) {
//            DemoContract.InputTypeState.NoSign -> DemoContract.InputTypeState.WithSign
//            DemoContract.InputTypeState.WithSign -> DemoContract.InputTypeState.NoSign
//        }
//    }

}

class DemoContract {

    data class PageState(
        val inputType: InputTypeState,
    )

    sealed class InputTypeState : UiState {
        data object NoSign : InputTypeState()
        data object WithSign : InputTypeState()
    }

    sealed class ClickEvent : UiEvent {
        data object BtnClickEvent : ClickEvent()
        data object Btn2ClickEvent : ClickEvent()
        data object Btn3ClickEvent : ClickEvent()
    }

    sealed class InputTypeEvent : UiEvent {
        data object ChangeInputTypeEvent : InputTypeEvent()
    }
}
