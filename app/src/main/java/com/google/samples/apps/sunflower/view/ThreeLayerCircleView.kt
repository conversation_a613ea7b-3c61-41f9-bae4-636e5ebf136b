/*
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.view
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import com.google.samples.apps.sunflower.view.drawingtoolbar.dp2px

class ThreeLayerCircleView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)

    // 尺寸参数（dp单位）
    private val innerRadiusDp = 4f.dp2px()      // 内层实心圆半径
    private val middleStrokeWidthDp = 3f.dp2px()  // 中间圆环宽度
    private val outerStrokeWidthDp = 1f.dp2px()   // 外层圆环宽度

    // 颜色
    private val outerColor = Color.GRAY
    private val middleColor = Color.WHITE
    private val innerColor = Color.BLACK


    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val centerX = width / 2f
        val centerY = height / 2f

        // 1. 绘制最外层黑色圆环（1dp宽度）
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = outerStrokeWidthDp
        paint.color = outerColor
        canvas.drawCircle(
            centerX,
            centerY,
            innerRadiusDp + middleStrokeWidthDp + outerStrokeWidthDp / 2,
            paint
        )

        // 2. 绘制中间白色圆环（3dp宽度）
        paint.strokeWidth = middleStrokeWidthDp
        paint.color = middleColor
        canvas.drawCircle(
            centerX,
            centerY,
            innerRadiusDp + middleStrokeWidthDp / 2,
            paint
        )

        // 3. 绘制最内层黑色实心圆（4dp半径）
        paint.style = Paint.Style.FILL
        paint.color = innerColor
        canvas.drawCircle(centerX, centerY, innerRadiusDp, paint)
    }
}