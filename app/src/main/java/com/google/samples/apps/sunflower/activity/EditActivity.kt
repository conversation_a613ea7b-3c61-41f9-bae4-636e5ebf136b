/*
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.activity

import android.graphics.Color
import android.os.Bundle
import android.text.Editable
import android.text.InputFilter
import android.text.InputType
import android.text.Spanned
import android.text.TextWatcher
import android.util.Log
import android.view.View
import android.widget.EditText
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModel
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import androidx.transition.ChangeBounds
import androidx.transition.TransitionManager
import com.google.samples.apps.sunflower.base.UiEvent
import com.google.samples.apps.sunflower.base.UiState
import com.google.samples.apps.sunflower.base.launchRepeatOnLifecycle
import com.google.samples.apps.sunflower.databinding.ActivityEditBinding
import com.google.samples.apps.sunflower.span.setDashedUnderlineText
import com.wordsfairy.base.tools.toast
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@AndroidEntryPoint
class EditActivity : AppCompatActivity() {
    private val mBinding: ActivityEditBinding by lazy {
        ActivityEditBinding.inflate(layoutInflater)
    }

    private val viewModel: MyViewModel by viewModels<MyViewModel>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)
        initView()
    }

    private val watcher = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

        }

        override fun afterTextChanged(s: Editable?) {
            s?.withNegativeSign()
            s?.checkTakeProfitDigits(2)

        }
    }

    private fun Boolean.toInputTypeFlags() =
        InputType.TYPE_CLASS_NUMBER or InputType.TYPE_NUMBER_FLAG_DECIMAL or if (this) InputType.TYPE_NUMBER_FLAG_SIGNED else 0

    fun Editable.withNegativeSign() {
        runCatching {
            val temp = this.toString()
            if (temp.isEmpty()) {
                this.insert(0, "-")
            } else if (temp[0] != '-') {
                this.insert(0, "-")
            } else {

            }
        }.onFailure {
            it.printStackTrace()
        }
    }

    fun Editable.checkTakeProfitDigits(digits: Int) {
        runCatching {
            val temp = this.toString()
            if (temp.contains(".")) {
                val posDot = temp.indexOf(".")
                if (temp.length - posDot - 1 > digits) {
                    this.delete(posDot + digits + 1, posDot + 2 + digits)
                }
                if (posDot > 9 && posDot - 1 in 0..this.length && posDot in 0..this.length)
                    this.delete(posDot - 1, posDot)
            } else {
                if (temp.length > 9)
                    this.delete(temp.length - 1, temp.length)
            }
        }.onFailure {
            it.printStackTrace()
        }
    }

    fun EditText.setDecimalFilter() {
        val filter = object : InputFilter {

            override fun filter(
                source: CharSequence?,
                start: Int,
                end: Int,
                dest: Spanned?,
                dstart: Int,
                dend: Int
            ): CharSequence? {
                return try {
                    val input = dest?.subSequence(0, dstart).toString() +
                            source?.subSequence(start, end) +
                            dest?.subSequence(dend, dest.length).toString()
                    if (input.startsWith("+") || input.startsWith("-.")) {
                        ""
                    } else {
                        null// 返回 null 表示接受输入
                    }
                } catch (e: NumberFormatException) {
                    "" // 返回空字符串表示拒绝输入
                }
            }

        }

        filters = arrayOf(filter)
    }

    private fun lineDrawerVisible(visible: Boolean) {
        TransitionManager.beginDelayedTransition(mBinding.clAnimator, ChangeBounds())

//        if (visible) {
//            mContentBinding.ivLineMore.rotation = 0f
//            selectedTabIndex = 2
//            mContentBinding.groupLine.isVisible = true
//        } else {
//            mContentBinding.ivLineMore.rotation = 180f
//            mContentBinding.groupLine.isVisible = false
//        }
    }

    private fun initView() {
//        mBinding.loadingView.progressDrawable = ContextCompat.getDrawable(this, R.drawable.icon_loading)
        mBinding.clAnimator.setOnClickListener {
            TransitionManager.beginDelayedTransition(mBinding.clAnimator, ChangeBounds())
            mBinding.loadingView.setVisibility(if (mBinding.loadingView.visibility == View.VISIBLE) View.GONE else View.VISIBLE)
        }



        mBinding.volSeekBar.setProgress(0)

        mBinding.etInput.setDecimalFilter()
        mBinding.etInput.addTextChangedListener(watcher)
//        mBinding.etInput.inputType = signedDecimals.toInputTypeFlags()
        mBinding.btnInput.setOnClickListener {
//            signedDecimals = signedDecimals.not()
//            mBinding.etInput.inputType = signedDecimals.toInputTypeFlags()
//            viewModel.changeInputType()
//            mBinding.tvRun.requestFocus()
//            viewModel.sendEvent(EditContract.ClickEvent.BtnClickEvent)
            lifecycleScope.launch {
                Log.d("zl_log", "1")
                withContext(Dispatchers.IO) {
                    delay(2000)
                    Log.d("zl_log", "1.1")

//                    withContext(Dispatchers.Main) {
//                        Log.d("zl_log", "2.1")
//
//                        withContext(Dispatchers.IO) {
//                            Log.d("zl_log", "2.1.1")
//                        }
//                    }
//                    withContext(Dispatchers.Main) {
//                        Log.d("zl_log", "2.2")
//
//                        withContext(Dispatchers.IO) {
//                            Log.d("zl_log", "2.2.1")
//                        }
//                    }
                }
//                withContext(Dispatchers.Main) {
//                    Log.d("zl_log", "3")
//                }
            }
            lifecycleScope.launch {
                Log.d("zl_log", "2")
            }
            lifecycleScope.launch {
                Log.d("zl_log", "3")
            }
            lifecycleScope.launch {
                Log.d("zl_log", "4")
            }
            Log.d("zl_log", "5")

        }
        mBinding.btnInput2.setOnClickListener {
            viewModel.sendEvent(EditContract.ClickEvent.Btn2ClickEvent)
        }

        // 在 Activity 或 Fragment 中：
        mBinding.tvDemo.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
        mBinding.tvDemo.setDashedUnderlineText(
            fullText = "点击此处查看更多详情",
            targetText = "点击此处", // 明确指定需要加虚线的文本
            color = Color.RED,
            dashWidth = 8f,
            dashGap = 4f,
            onClick = { Toast.makeText(this, "虚线区域被点击", Toast.LENGTH_SHORT).show() }
        )

        // 订阅 StateFlow
        launchRepeatOnLifecycle {

            viewModel.inputState.collect { inputTypeState ->
                when (inputTypeState) {
                    EditContract.InputTypeState.NoSign -> {
                        mBinding.etInput.inputType = false.toInputTypeFlags()
                        toast("无符号")
                    }

                    EditContract.InputTypeState.WithSign -> {
                        mBinding.etInput.inputType = true.toInputTypeFlags()
                        toast("有符号")
                    }
                }
            }
        }

        launchRepeatOnLifecycle {

            viewModel.event.collect { event ->
                when (event) {
                    EditContract.ClickEvent.BtnClickEvent -> {
                        Toast.makeText(this@EditActivity, "点击了按钮~changeInputType", Toast.LENGTH_SHORT).show()
                        viewModel.changeInputType()
                    }

                    EditContract.ClickEvent.Btn2ClickEvent -> {
                        Toast.makeText(this@EditActivity, "点击了按钮2~", Toast.LENGTH_SHORT).show()
                    }
                }
            }
        }

    }

}

class MyViewModel : ViewModel() {
    private val _inputState = MutableStateFlow<EditContract.InputTypeState>(EditContract.InputTypeState.WithSign)
    val inputState = _inputState.asStateFlow()

    private val _event = MutableSharedFlow<UiEvent>()
    val event = _event.asSharedFlow()
    fun sendEvent(event: EditContract.ClickEvent) {
        viewModelScope.launch {
            _event.emit(event)
        }
    }

    fun changeInputType() {
        _inputState.value = when (_inputState.value) {
            EditContract.InputTypeState.NoSign -> EditContract.InputTypeState.WithSign
            EditContract.InputTypeState.WithSign -> EditContract.InputTypeState.NoSign
        }
    }

}

class EditContract {

    data class PageState(
        val inputType: InputTypeState,
    )

    sealed class InputTypeState : UiState {
        data object NoSign : InputTypeState()
        data object WithSign : InputTypeState()
    }

    sealed class ClickEvent : UiEvent {
        data object BtnClickEvent : ClickEvent()
        data object Btn2ClickEvent : ClickEvent()
        data object Btn3ClickEvent : ClickEvent()
    }

    sealed class InputTypeEvent : UiEvent {
        data object ChangeInputTypeEvent : InputTypeEvent()
    }
}

//fun TextView.setDashedUnderlineText(
//    text: String,
//    start: Int,
//    end: Int,
//    @ColorInt color: Int = Color.BLUE,
//    dashWidth: Float = 4f,
//    dashGap: Float = 4f,
//    strokeWidth: Float = 1f,
//    onClick: () -> Unit
//) {
//    val spannable = SpannableString(text)
//    val span = DashedUnderlineSpan(color, dashWidth, dashGap, strokeWidth).apply {
//        setTargetRange(start, end) // 关键：标记目标范围
//        setOnClickListener { onClick() }
//    }
//
//    spannable.setSpan(
//        span,
//        start,
//        end,
//        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
//    )
//
//    this.text = spannable
//    this.movementMethod = LinkMovementMethod.getInstance() // 启用点击
//    this.highlightColor = Color.TRANSPARENT // 移除点击时的背景高亮
//}
