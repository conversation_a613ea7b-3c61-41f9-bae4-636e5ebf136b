/*
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.view

import android.content.Context
import android.graphics.Rect
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.PopupWindow
import kotlin.math.max

class SmartPopupWindow : PopupWindow {
    private val mContext: Context
    private val mAnchorView: View
    private val mPreferredGravity: Int

    constructor(context: Context, anchorView: View, contentView: View?, width: Int, height: Int) : super(contentView, width, height, true) {
        this.mContext = context
        this.mAnchorView = anchorView
        this.mPreferredGravity = Gravity.NO_GRAVITY
        init()
    }

    constructor(context: Context, anchorView: View, contentView: View?, width: Int, height: Int, preferredGravity: Int) : super(contentView, width, height, true) {
        this.mContext = context
        this.mAnchorView = anchorView
        this.mPreferredGravity = preferredGravity
        init()
    }

    private fun init() {
        isOutsideTouchable = true
        isFocusable = true
    }

    override fun showAsDropDown(anchor: View) {
        showAtLocation(anchor, Gravity.NO_GRAVITY, 0, 0)
    }

    override fun showAtLocation(parent: View, gravity: Int, x: Int, y: Int) {
        // 计算锚点View在屏幕中的位置
        val anchorLocation = IntArray(2)
        mAnchorView.getLocationOnScreen(anchorLocation)
        val anchorX = anchorLocation[0]
        val anchorY = anchorLocation[1]
        val anchorWidth = mAnchorView.width
        val anchorHeight = mAnchorView.height

        // 获取屏幕尺寸
        val wm = mContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val screenRect = Rect()
        wm.defaultDisplay.getRectSize(screenRect)
        val screenWidth = screenRect.width()
        val screenHeight = screenRect.height()

        // 获取PopupWindow的尺寸
        val contentView = contentView
        contentView.measure(
            View.MeasureSpec.makeMeasureSpec(width, View.MeasureSpec.AT_MOST),
            View.MeasureSpec.makeMeasureSpec(height, View.MeasureSpec.AT_MOST)
        )
        val popupWidth = contentView.measuredWidth
        val popupHeight = contentView.measuredHeight

        // 计算最佳显示位置
        var xPos: Int
        var yPos: Int
        val gravityToUse = Gravity.TOP or Gravity.START

        // 水平位置计算
        if (mPreferredGravity == Gravity.START || (mPreferredGravity == Gravity.NO_GRAVITY && anchorX + popupWidth > screenWidth)) {
            // 靠左显示
            xPos = max(0.0, (anchorX - popupWidth).toDouble()).toInt()
            if (xPos < 0) xPos = 0
        } else if (mPreferredGravity == Gravity.END || (mPreferredGravity == Gravity.NO_GRAVITY && anchorX + anchorWidth + popupWidth <= screenWidth)) {
            // 靠右显示
            xPos = anchorX + anchorWidth
            if (xPos + popupWidth > screenWidth) {
                xPos = screenWidth - popupWidth
            }
        } else {
            // 居中显示
            xPos = anchorX + (anchorWidth - popupWidth) / 2
            if (xPos < 0) xPos = 0
            if (xPos + popupWidth > screenWidth) {
                xPos = screenWidth - popupWidth
            }
        }

        // 垂直位置计算
        if (mPreferredGravity == Gravity.TOP || (mPreferredGravity == Gravity.NO_GRAVITY && anchorY + anchorHeight + popupHeight > screenHeight)) {
            // 显示在锚点上方
            yPos = anchorY - popupHeight
            if (yPos < 0) {
                // 如果上方空间不足，尝试显示在下方
                yPos = anchorY + anchorHeight
                if (yPos + popupHeight > screenHeight) {
                    yPos = screenHeight - popupHeight
                }
            }
        } else {
            // 显示在锚点下方
            yPos = anchorY + anchorHeight
            if (yPos + popupHeight > screenHeight) {
                // 如果下方空间不足，尝试显示在上方
                yPos = anchorY - popupHeight
                if (yPos < 0) {
                    yPos = 0
                }
            }
        }

        // 调整位置，确保不超出屏幕
        if (xPos < 0) xPos = 0
        if (xPos + popupWidth > screenWidth) xPos = screenWidth - popupWidth
        if (yPos < 0) yPos = 0
        if (yPos + popupHeight > screenHeight) yPos = screenHeight - popupHeight

        // 转换为相对于父View的坐标
        val parentLocation = IntArray(2)
        (parent as ViewGroup).getLocationOnScreen(parentLocation)
        xPos -= parentLocation[0]
        yPos -= parentLocation[1]

        super.showAtLocation(parent, gravityToUse, xPos, yPos)
    }
}