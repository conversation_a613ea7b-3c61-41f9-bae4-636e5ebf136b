/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.leetcode.algorithm

import com.google.samples.apps.sunflower.leetcode.Demo.Companion.printIntersectionResult
import com.google.samples.apps.sunflower.leetcode.bean.ListNode

/**
 * 判断链表相交
 */
class IntersectionNode {
    companion object {
        @JvmStatic
        fun main(args: Array<String>) {

            //创建链表 A：1->2->3->4->5
            val nodeA1 = ListNode(1)
            val nodeA2 = ListNode(2)
            val nodeA3 = ListNode(3)
            val nodeA4 = ListNode(4)
            val nodeA5 = ListNode(5)
            nodeA1.next = nodeA2
            nodeA2.next = nodeA3
            nodeA3.next = nodeA4
            nodeA4.next = nodeA5
            //创建链表 B：6->7->8，然后与链表A的节点2相交
            val nodeB1 = ListNode(6)
            val nodeB2 = ListNode(7)
            val nodeB3 = ListNode(8)
            nodeB1.next = nodeB2
            nodeB2.next = nodeB3
            nodeB3.next = nodeA2

            printIntersectionResult(nodeA1, nodeB1)


        }

//        fun printIntersectionResult(headA: ListNode?, headB: ListNode?) {
//            val intersectionNode = getIntersectionNode2(headA, headB)
//            if (intersectionNode == null) {
//                println("两个链表不相交")
//            } else {
//                println("两个链表相交于值为 ${intersectionNode.value} 的节点")
//            }
//        }
//
//        fun getIntersectionNode2(headA: ListNode?, headB: ListNode?): ListNode? {
//            val visited = HashSet<ListNode>()
//            var temp = headA
//            while (temp != null) {
//                visited.add(temp)
//                temp = temp.next
//            }
//            temp = headB
//            while (temp != null) {
//                if (visited.contains(temp)) {
//                    return temp
//                }
//                temp = temp.next
//            }
//            return null
//        }
//
//
//
//
//
//
//
//
//        /**
//         * 判断相交链表
//         */
//        fun getIntersectionNode(headA: ListNode?, headB: ListNode?): ListNode? {
//            var a = headA
//            var b = headB
//            if (a == null || b == null) {
//                return null
//            }
//            //创建链表 A：1->2->3->4->5          a=1 c=4
//            //创建链表 B：6->7->8->2->3->4->5，然后与链表A的节点1相交 b=3 c=4
//            //创建链表 A：1->2->3->4->5->null->6->7   ->8->2->3->4   ->5->null
//            //创建链表 B：6->7->8->2->3->4   ->5->null->6->7->8
//            while (a != b) {
////                println("a.value= ${a?.value}")
//                println("a.value= ${a?.value}；b.value= ${b?.value}；headA.value= ${headA?.value}；headB.value= ${headB?.value}")
//                a = if (a == null) {
//                    headB
//                } else {
//                    a.next
//                }
////                println("b.value= ${b?.value}")
//                b = if (b == null) {
//                    headA
//                } else {
//                    b.next
//                }
//            }
//            println("a.value= ${a?.value}；b.value= ${b?.value}；headA.value= ${headA?.value}；headB.value= ${headB?.value}")
//            return a
//        }


    }
}















