package com.google.samples.apps.sunflower.view.drawingtoolbar

import android.content.Context
import android.view.View
import com.google.samples.apps.sunflower.R
import com.google.samples.apps.sunflower.databinding.KlineDialogLinePickerBinding
import com.lxj.xpopup.core.AttachPopupView

/**
 * 实现虚线样式选择器弹窗
 */
class LineDashedPickerPopup(context: Context) : AttachPopupView(context) {

    private val mBinding: KlineDialogLinePickerBinding by lazy {
        KlineDialogLinePickerBinding.bind(popupImplView)
    }

    private var onItemListener: ((adapter: LineStyleAdapter, view: View, position: Int, item: LineStyleBean) -> Unit)? = null

    override fun getImplLayoutId(): Int = R.layout.kline_dialog_line_picker

    override fun onCreate() {
        super.onCreate()

        val lineIcons = listOf(
            LineStyleBean(R.mipmap.ic_line_deshed_01, 0),
            LineStyleBean(R.mipmap.ic_line_deshed_02, 1),
            LineStyleBean(R.mipmap.ic_line_deshed_03, 2),
            LineStyleBean(R.mipmap.ic_line_deshed_04, 3),
        )

        mBinding.viewLinePicker.setLineStyleBeans(lineIcons)

        mBinding.viewLinePicker.setOnItemListener { adapter, view, position, item ->
            onItemListener?.invoke(adapter, view, position, item)
            dismiss()
        }
    }

    fun setOnItemListener(listener: (adapter: LineStyleAdapter, view: View, position: Int, item: LineStyleBean) -> Unit): LineDashedPickerPopup {
        onItemListener = listener
        return this
    }

}