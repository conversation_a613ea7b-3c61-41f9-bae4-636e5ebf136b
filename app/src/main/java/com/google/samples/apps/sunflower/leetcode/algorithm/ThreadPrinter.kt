/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.leetcode.algorithm

import okhttp3.internal.notify
import okhttp3.internal.wait

/**
 * 线程交替打印
 */
class ThreadPrinter {
    companion object {
        @JvmStatic
        fun main(args: Array<String>) {
            val printer = Printer()
            printer.printNumbers()
        }
    }
}

class Printer {
    private var number = 1
    private val lock = Any()
    private var printOdd = true//true:线程 1 打印奇数，线程 2 打印偶数。false:线程 2 打印奇数，线程 1 打印偶数。

    fun printNumbers() {
        Thread {
            while (number <= 99) {
                synchronized(lock) {
                    if (!printOdd) {
                        try {

                            lock.wait()
                        } catch (e: InterruptedException) {
                            e.printStackTrace()
                        }
                    }
                    if (number <= 99) {
                        println("Thread 1: ${number++}")
                        printOdd = false
                        lock.notify()
                    }
                }
            }
        }.start()


        Thread {
            while (number <= 99) {
                synchronized(lock) {
                    if (printOdd) {
                        try {
                            lock.wait()
                        } catch (e: InterruptedException) {
                            e.printStackTrace()
                        }
                    }

                    if (number <= 99) {
                        println("Thread 2: ${number++}")
                        printOdd = true
                        lock.notify()
                    }


                }
            }
        }.start()
    }

}


















