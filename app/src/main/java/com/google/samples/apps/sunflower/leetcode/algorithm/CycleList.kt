/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.leetcode.algorithm

/**
 * 判断链表是否有环
 */
class CycleList {
    companion object {
        @JvmStatic
        fun main(args: Array<String>) {

            //创建链表 A：1->2->3->4->5
            val nodeA1 = ListNode6(1)
            val nodeA2 = ListNode6(2)
            val nodeA3 = ListNode6(3)
            val nodeA4 = ListNode6(2)
            val nodeA5 = ListNode6(1)
            nodeA1.next = nodeA2
            nodeA2.next = nodeA3
            nodeA3.next = nodeA4
            nodeA4.next = nodeA5
            nodeA5.next = nodeA4

            println("是否是回文链表：${hasCycle(nodeA1)}")
        }

        private fun hasCycle(head: ListNode6?): Boolean {
            val visited = HashSet<ListNode6>()
            var current = head
            while (current!=null){
                if(visited.contains(current)){
                    return true
                }
                visited.add(current)
                current = current.next
            }
            return false
        }


    }
}

class ListNode6(var value: Int) {
    var next: ListNode6? = null
}














