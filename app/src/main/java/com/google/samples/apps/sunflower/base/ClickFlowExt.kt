/*
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.base

import android.view.View
import androidx.lifecycle.LifecycleCoroutineScope
import kotlinx.coroutines.channels.SendChannel
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlin.coroutines.cancellation.CancellationException

fun <E> SendChannel<E>.safeSend(value: E) = try {
    trySend(value)
} catch (e: CancellationException) {
    e.printStackTrace()
}

fun View.clickFlow(): Flow<View> {
    return callbackFlow {
        setOnClickListener {
            safeSend(it)
        }
        awaitClose { setOnClickListener(null) }
    }
}

inline fun View.click(lifecycle: LifecycleCoroutineScope, crossinline onClick: (view: View) -> Unit) {
    clickFlow().onEach {
        onClick(this)
    }.launchIn(lifecycle)
}

/**
 * 延迟第一次点击事件
 *
 * Example：
 *
 * view.clickDelayed(lifecycleScope) {
 *     showShortToast("xxx")
 * }
 */
inline fun View.clickDelayed(
    lifecycle: LifecycleCoroutineScope,
    delayMillis: Long = 500,
    crossinline onClick: (view: View) -> Unit
) {
    clickFlow().onEach {
        delay(delayMillis)
        onClick(this)
    }.launchIn(lifecycle)
}


fun getLastMillis(v: View, id: Int): Long {
    return if (v.getTag(id) != null) v.getTag(id) as Long else 0L
}

fun setLastMillis(v: View, id: Int, millis: Long) {
    v.setTag(id, millis)
}

inline fun View.clickTrigger(
    lifecycle: LifecycleCoroutineScope,
    intervalMillis: Long = 1000,
    crossinline onClick: (view: View) -> Unit
) {
    val id = 100001
    clickFlow().onEach {
        val currentMillis = System.currentTimeMillis()
        if (currentMillis - getLastMillis(this, id) < intervalMillis) {
            return@onEach
        }
        setLastMillis(this, id, currentMillis)
        onClick(this)
    }.launchIn(lifecycle)
}