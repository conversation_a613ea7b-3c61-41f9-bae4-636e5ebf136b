/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.leetcode.algorithm

import com.google.samples.apps.sunflower.leetcode.algorithm.ClimbStairs.Companion.climbStairs2
import kotlin.math.max

class MaxProfit {
    companion object {
        @JvmStatic
        fun main(args: Array<String>) {

            val prices = intArrayOf(7, 1, 5, 3, 6, 4)
            println("能获取的最大利润是 ${maxProfit2(prices)}")

        }


        fun maxProfit(prices: IntArray): Int {
            var maxProfit = 0
            var minPrice = prices[0]
            for (i in 1 until prices.size) {
                if (prices[i] < minPrice) {
                    minPrice = prices[i]
                } else {
                    maxProfit = maxOf(maxProfit, prices[i] - minPrice)
                }
            }
            return maxProfit
        }


        fun maxProfit2(prices: IntArray): Int {
            var maxProfit = 0
            var minPrice = prices[0]

            for (i in 1 until prices.size) {
                if (prices[i] < minPrice) {
                    minPrice = prices[i]
                } else {
                    maxProfit = maxOf(maxProfit, prices[i] - minPrice)
                }
            }

            return maxProfit
        }


    }
}