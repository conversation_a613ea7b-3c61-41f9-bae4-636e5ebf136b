/*
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.view

import android.content.Context
import com.google.samples.apps.sunflower.R
import com.google.samples.apps.sunflower.databinding.PopupCopyBinding
import com.lxj.xpopup.core.AttachPopupView

class CopyPopup(context: Context) : AttachPopupView(context) {

    private var mBinding: PopupCopyBinding? = null

    private var click: (() -> Unit)? = null

    override fun getImplLayoutId(): Int = R.layout.popup_copy

    override fun onCreate() {
        super.onCreate()
        mBinding = PopupCopyBinding.bind(popupImplView)

        mBinding?.root?.setOnClickListener {
            click?.invoke()
            dismiss()
        }
    }

    fun setClick(click: (() -> Unit)?): CopyPopup {
        this.click = click
        return this
    }

}