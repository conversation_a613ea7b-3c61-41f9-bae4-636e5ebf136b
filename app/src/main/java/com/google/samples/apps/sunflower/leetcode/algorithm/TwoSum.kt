/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.leetcode.algorithm

class TwoSum {
    companion object {
        @JvmStatic
        fun main(args: Array<String>) {
            val nums = intArrayOf(2, 7, 11, 15)
            val target = 9

            val result = towSum(nums,18)
            println("满足两数之和为 $target 的两个数的下标分别是：${result[0]} 和 ${result[1]}")

        }


        fun towSum(nums: IntArray, target: Int): IntArray {

            val numMap = mutableMapOf<Int, Int>()
            for (i in nums.indices) {
                val complement = target - nums[i]
                if (numMap.containsKey(complement)) {
                    return intArrayOf(i, numMap.get(complement)!!)
                }
                numMap[nums[i]] = i
            }

            return intArrayOf()

        }

    }
}