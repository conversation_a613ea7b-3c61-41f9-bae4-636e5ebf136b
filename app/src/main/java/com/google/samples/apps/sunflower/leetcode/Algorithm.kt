package com.google.samples.apps.sunflower.leetcode

import android.util.Log
import com.wordsfairy.base.utils.GsonUtils
import kotlin.math.max

class Algorithm {
    companion object {
        /**
         * 49. 字母异位词分组
         */
        fun groupAnagrams(strs: Array<String>?): MutableList<MutableList<String>> {

            if (strs.isNullOrEmpty()) return mutableListOf()
            val map = hashMapOf<String, MutableList<String>>()
            strs.forEach {

                val toCharArray = it.toCharArray()
                toCharArray.sort()
                val key = toCharArray.joinToString()
                val list = map.getOrDefault(key, mutableListOf<String>())
                list.add(it)
                map[key] = list
            }
            return map.values.map { it.toMutableList() }.toMutableList()
        }

        /**
         * 49. 字母异位词分组
         */
        fun groupAnagrams2(strs: Array<String>): List<List<String>> {

            val map = HashMap<String, MutableList<String>>()
            for (str in strs) {
                val array = str.toCharArray()
                array.sort()
                val key = String(array)
                val list = map.getOrDefault(key, mutableListOf())
                list.add(str)
                map[key] = list
            }

            return ArrayList(map.values)
        }


        /**
         * 283. 移动零
         */
        fun moveZeroes(nums: IntArray) {
            if (nums == null) return

            var j = 0
            for (i in 0 until nums.size) {
                if (nums[i] != 0) {
                    nums[j++] = nums[i]
                }
            }

            for (i in j until nums.size) {
                nums[i] = 0
            }

        }


        fun groupAnagrams3(strs: Array<String>): List<List<String>> {

            val map = HashMap<String, MutableList<String>>()
            strs.forEach {
                val toCharArray = it.toCharArray()
                toCharArray.sort()
                val key = toCharArray.toString()
                val list = map.getOrDefault(key, mutableListOf())
                list.add(it)
                map[key] = list
            }
            return ArrayList(map.values)

        }


        fun groupAnagrams4(strs: Array<String>): List<List<String>> {
            val map = HashMap<String, MutableList<String>>()
            strs.forEach {
                val toCharArray = it.toCharArray()
                toCharArray.sort()
                val key = toCharArray.joinToString("")
                Log.e("zl_log", "key=$key")
                val list = map.getOrDefault(key, mutableListOf())
                list.add(it)
                map[key] = list
            }

            return ArrayList(map.values)
        }

        fun groupAnagrams5(strs: Array<String>): List<List<String>> {
            val map = HashMap<String, MutableList<String>>()
            strs.forEach {
                val toCharArray = it.toCharArray()
                toCharArray.sort()
                val key = toCharArray.joinToString("")
                val list = map.getOrDefault(key, mutableListOf())
                list.add(it)
                map[key] = list
            }
            return ArrayList(map.values)
        }

        fun twoSum(nums: IntArray, target: Int): IntArray {
            val indexArray = IntArray(2)
            outer@ for (i in nums.indices) {
                for (j in nums.indices) {
                    if (i != j && nums[i] + nums[j] == target) {
                        indexArray[0] = i
                        indexArray[1] = j
                        break@outer
                    }
                }
            }
            return indexArray
        }

        fun twoSum2(nums: IntArray, target: Int): IntArray {
            val n = nums.size
            for (i in nums.indices) {
                for (j in i + 1 until n) {
                    if (nums[i] + nums[j] == target) {
                        return intArrayOf(i, j)
                    }
                }
            }
            return intArrayOf()
        }

        fun twoSum3(nums: IntArray, target: Int): IntArray {
            val map = mutableMapOf<Int, Int>()
            for (i in nums.indices) {
                val complement = target - nums[i]
                if (map.contains(complement)) {
                    return intArrayOf(map[complement]!!, i)
                }
                map[nums[i]] = i
            }
            return intArrayOf()
        }


        fun twoSum4(nums: IntArray, target: Int): IntArray {
            val map = hashMapOf<Int, Int>()
            for (i in nums.indices) {
                var complement = target - nums[i]
                if (map.contains(complement)) {
                    return intArrayOf(map[complement]!!, i)
                }
                map[nums[i]] = i
            }
            return intArrayOf()
        }

        /**
         * nums = [0,1,0,3,12]
         */
        fun moveZeroes2(nums: IntArray) {
            var j = 0
            nums.forEachIndexed { index, i ->
                if (i != 0) {
                    nums[j++] = i
                }
            }
            for (index in j until nums.size) {
                nums[index] = 0
            }
        }

        /**
         * nums = [0,1,0,3,12]
         */
        fun moveZeroes3(nums: IntArray) {
            var j = 0
            nums.forEachIndexed { index, i ->
                if (i != 0) {
                    var temp = i
                    nums[index] = 0
                    nums[j++] = temp
                }
            }
        }

        fun longestConsecutive(nums: IntArray): Int {
            val numSet = HashSet<Int>()
            for (num in nums) {
                numSet.add(num)
            }

            var longestStreck = 0

            for (num in numSet) {
                if (!numSet.contains(num - 1)) {
                    var currentNum = num
                    var currentStreak = 1
                    while (numSet.contains(currentNum + 1)) {
                        currentNum += 1
                        currentStreak += 1
                    }

                    longestStreck = maxOf(longestStreck, currentStreak)
                }
            }

            return longestStreck
        }

        /**
         * [-1, 0, 1, 2, -1, -4]
         * 1、排序
         * 2、枚举第一个数a，当前的数和前一个数相同，跳过这个数
         * 3、指针重合退出循环
         * 4、bc相加等于-a，加入list
         */
        fun threeSum(nums: IntArray): List<List<Int>> {
            val n = nums.size
            nums.sort()
            val ans = mutableListOf<List<Int>>()

            //枚举a
            for (first in 0 until n) {
                //需要和上一次枚举的数不相同
                if (first > 0 && nums[first] == nums[first - 1]) {
                    continue
                }
                //c对应的指针初始指向数组的最右端
                var third = n - 1
                val target = -nums[first]
                //枚举b
                for (second in first + 1 until n) {
                    //需要和上一次枚举的数不相同
                    if (second > first + 1 && nums[second] == nums[second - 1]) {
                        continue
                    }

                    //需要保证b的指针在c的指针的左侧
                    while (second < third && nums[second] + nums[third] > target) {
                        third--
                    }
                    //如果指针重合，随着b后续的增加
                    //就不会有满足a+b+c = 0 并且b<c的c了，可以退出循环
                    if (second == third) {
                        break
                    }
                    if (nums[second] + nums[third] == target) {
                        val list = listOf(nums[first], nums[second], nums[third])
                        ans.add(list)
                    }

                }
            }
            return ans
        }

        fun threeSum2(nums: IntArray): List<List<Int>> {

            val n = nums.size
            nums.sort()
            val ans = mutableListOf<List<Int>>()

            for (first in 0 until n) {
                if (first > 0 && nums[first] == nums[first - 1]) continue
                var third = n - 1
                val target = -nums[first]
                for (second in first + 1 until n) {
                    if (second > first + 1 && nums[second] == nums[second - 1]) continue
                    while (second < third && nums[second] + nums[third] > target) {
                        third--
                    }

                    if (second == third) {
                        break
                    }
                    if (nums[second] + nums[third] == target) {
                        ans.add(listOf(nums[first], nums[second], nums[third]))
                    }
                }
            }

            return ans
        }


        fun lengthLongestSubstring(s: String): Int {
            val occ = mutableSetOf<Char>()
            val n = s.length
            var rk = -1
            var ans = 0
            for (i in 0 until n) {
                if (i != 0) {
                    occ.remove(s[i - 1])
                }
                while (rk + 1 < n && !occ.contains(s[rk + 1])) {
                    occ.add(s[rk + 1])
                    ++rk
                }
                ans = max(ans, rk - i + 1)
            }
            return ans
        }


        fun lengthLongestSubstring2(s: String): Int {
            val occ = mutableSetOf<Char>()
            val n = s.length
            var rk = -1
            var ans = 0


            for (i in 0 until n) {
                if (i != 0) {
                    occ.remove(s[i - 1])
                }

                while (rk + 1 < n && !occ.contains(s[rk + 1])) {
                    occ.add(s[rk + 1])
                    ++rk
                }
                ans = max(ans, rk + 1 - i)
            }
            return ans
        }

        fun lengthLongestSubstring3(s: String): Int {
            val occ = mutableSetOf<Char>()

            val n = s.length
            var rk = -1
            var ans = 0
            for (i in 0 until n) {
                if (i != 0) {
                    occ.remove(s[i - 1])
                }

                while (rk + 1 < n && !occ.contains(s[rk + 1])) {
                    occ.add(s[rk + 1])
                    ++rk
                }
                ans = max(ans, rk + 1 - i)
            }
            return ans
        }


        /**
         * 找到字符串中所有字母异位词
         * 给定两个字符串 s 和 p，找到 s 中所有 p 的
         * 异位词的子串，返回这些子串的起始索引。不考虑答案输出的顺序。
         *
         * 当窗口中每种字母的数量与字符串 p 中每种字母的数量相同时，则说明当前窗口为字符串 p 的异位词
         */
        fun findAnagrams(s: String, p: String): List<Int> {
            val sLen = s.length
            val pLen = p.length

            if (sLen < pLen) {
                return emptyList()
            }

            val ans = mutableListOf<Int>()
            val sCount = IntArray(26)
            val pCount = IntArray(26)
            for (i in 0 until pLen) {
                sCount[s[i] - 'a']++
                pCount[p[i] - 'a']++
            }
            //sCount打印：[1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            //pCount打印：[1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            Log.e("zl_log", "sCount打印：${GsonUtils.toJson(sCount)}")
            Log.e("zl_log", "pCount打印：${GsonUtils.toJson(pCount)}")

            if (sCount.contentEquals(pCount)) {
                ans.add(0)
            }

            for (i in 0 until sLen - pLen) {
                sCount[s[i] - 'a']--
                sCount[s[i + pLen] - 'a']++
                Log.e("zl_log", "sCount打印2：${GsonUtils.toJson(sCount)}")
                if (sCount.contentEquals(pCount)) {
                    ans.add(i + 1)
                }
            }
//            Log.e("zl_log", "sCount打印2：${GsonUtils.toJson(sCount)}")
//            Log.e("zl_log", "pCount打印2：${GsonUtils.toJson(pCount)}")

            return ans


        }


    }


}

























