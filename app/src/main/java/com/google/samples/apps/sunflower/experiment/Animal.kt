/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.experiment

open class Animal {
    open fun makeSound() {
        println("Animal makes a sound")
    }
}

class Dog : Animal() {
    override fun makeSound() {
        println("Dog barks")
    }
}

class Cat : Animal() {
    override fun makeSound() {
        println("Cat meows")
    }
}

class AnimalHolder<T> {
    private var animal: T? = null
    fun setAnimal(animal: T) {
        this.animal = animal
    }

    fun getAnimal(): T? {
        return animal
    }
}

//class CovariantAnimalHolder<out T : Animal> {
//    private var animal: T? = null
//    fun getAnimal(): T? {
//        return animal
//    }
//}

class CovariantAnimalHolder<out T : Animal> {
    private var animal: T? = null
    fun getAnimal(): T? {
        return animal
    }
}

//fun main() {
////    val dogHolder = AnimalHolder<Dog>()
////    dogHolder.setAnimal(Dog())
//
////    val animalHolder: AnimalHolder<Animal> = dogHolder
//
////    val animalHolder :AnimalHolder<Animal> = AnimalHolder<Animal>()
////    animalHolder.setAnimal(Dog())
//
//
//    val dogHolder = CovariantAnimalHolder<Dog>()
//    val animalHolder:CovariantAnimalHolder<Animal> = dogHolder
//    val dog:Dog? = animalHolder.getAnimal()
//    dog?.makeSound()
//}

fun main() {
    val dogHolder = CovariantAnimalHolder<Dog>()
    val animalHolder: CovariantAnimalHolder<Animal> = dogHolder
    val animal: Animal? = animalHolder.getAnimal()
    if(animal is Dog){
        animal.makeSound()
    }

}