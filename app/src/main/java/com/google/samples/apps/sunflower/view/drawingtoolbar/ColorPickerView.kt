package com.google.samples.apps.sunflower.view.drawingtoolbar

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.GridLayoutManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.samples.apps.sunflower.R
import com.google.samples.apps.sunflower.databinding.KlineViewColorPickerBinding

/**
 * 颜色选择器
 */
class ColorPickerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val binding by lazy { KlineViewColorPickerBinding.inflate(LayoutInflater.from(context), this, true) }

    private var onColorPickListener: ((adapter: ColorGridAdapter, view: View, position: Int, item: ColorCell) -> Unit)? = null
    var mAdapter = ColorGridAdapter()

    init {
        setupRecyclerView()
    }

    private fun setupRecyclerView() {

        // 定义6种颜色
        val colors = listOf(
            ColorCell(Color.RED, 0),
            ColorCell(Color.BLUE, 1),
            ColorCell(Color.GREEN, 2),
            ColorCell(Color.YELLOW, 3),
            ColorCell(Color.MAGENTA, 4),
            ColorCell(Color.CYAN, 5)
        )

        binding.rvList.layoutManager = object : GridLayoutManager(context, 3) {
            override fun canScrollVertically(): Boolean = false // 禁用垂直滚动
            override fun canScrollHorizontally(): Boolean = false // 禁用水平滚动
        }
        binding.rvList.itemAnimator = null // 禁用默认动画
        binding.rvList.layoutAnimation = null // 禁用默认动画
        binding.rvList.isNestedScrollingEnabled = false
        binding.rvList.setHasFixedSize(true)

        binding.rvList.adapter = mAdapter

        // 添加分割线
        binding.rvList.addItemDecoration(
            DividerItemDecoration(context, DividerItemDecoration.HORIZONTAL).apply {
                setDrawable(ContextCompat.getDrawable(context, R.drawable.divider_grid)!!)
            }
        )
        binding.rvList.addItemDecoration(
            DividerItemDecoration(context, DividerItemDecoration.VERTICAL).apply {
                setDrawable(ContextCompat.getDrawable(context, R.drawable.divider_grid)!!)
            }
        )

        mAdapter.setOnItemClickListener { adapter, view, position ->
            val item = mAdapter.getItem(position) ?: return@setOnItemClickListener
            onColorPickListener?.invoke(mAdapter, view, position, item)

        }

        setColorCells(colors)
    }

    fun setOnColorPickListener(listener: (adapter: ColorGridAdapter, view: View, position: Int, item: ColorCell) -> Unit) {
        onColorPickListener = listener
    }

    // 批量添加工具项
    fun setColorCells(items: List<ColorCell>) {
        mAdapter.setList(items)
    }

    // 获取所有工具项
    fun getColorCells(): List<ColorCell> = mAdapter.data
}

class ColorGridAdapter() : BaseQuickAdapter<ColorCell, BaseViewHolder>(R.layout.kline_item_color_picker) {
    override fun convert(holder: BaseViewHolder, item: ColorCell) {
        holder.setBackgroundColor(R.id.viewColor, item.color)
    }

}

data class ColorCell(
    val color: Int, // 颜色资源ID
    val position: Int // 位置标识
)

//fun <T : Number> T.dp2px(): T {
//    val result = TypedValue.applyDimension(
//        TypedValue.COMPLEX_UNIT_DIP,
//        this.toFloat(),
//        Resources.getSystem().displayMetrics
//    )
//
//    return when (this) {
//        is Int -> result.toInt() as T
//        is Long -> result.toLong() as T
//        is Float -> result as T
//        is Double -> result.toDouble() as T
//        else -> result as T
//    }
//}