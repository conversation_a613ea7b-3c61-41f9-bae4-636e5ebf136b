/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.leetcode.algorithm

import com.google.samples.apps.sunflower.leetcode.Demo.Companion.getIntersectionNode
import com.google.samples.apps.sunflower.leetcode.Demo.Companion.getIntersectionNode2
import com.google.samples.apps.sunflower.leetcode.Demo.Companion.printIntersectionResult
import com.google.samples.apps.sunflower.leetcode.bean.ListNode

/**
 * 判断链表相交
 */
class IntersectionNode2 {
    companion object {
        @JvmStatic
        fun main(args: Array<String>) {

            //创建链表 A：1->2->3->4->5
            val nodeA1 = ListNode4(1)
            val nodeA2 = ListNode4(2)
            val nodeA3 = ListNode4(3)
            val nodeA4 = ListNode4(4)
            val nodeA5 = ListNode4(5)
            nodeA1.next = nodeA2
            nodeA2.next = nodeA3
            nodeA3.next = nodeA4
            nodeA4.next = nodeA5
            //创建链表 B：6->7->8->2->3->4->5
            val nodeB1 = ListNode4(6)
            val nodeB2 = ListNode4(7)
            val nodeB3 = ListNode4(8)
            nodeB1.next = nodeB2
            nodeB2.next = nodeB3
            nodeB3.next = nodeA3

            val intersectionNode = getIntersectionNode4(nodeA1, nodeB1)
            if (intersectionNode == null) {
                println("两个链表不相交")
            } else {
                println("两个链表相交于值为 ${intersectionNode.value} 的节点")
            }

        }


        private fun getIntersectionNode4(headA: ListNode4?, headB: ListNode4?): ListNode4? {
            val visited = HashSet<ListNode4>()
            var nodeA = headA
            while (nodeA != null) {
                visited.add(nodeA)
                nodeA = nodeA.next
            }
            var nodeB = headB
            while (nodeB != null) {
                if (visited.contains(nodeB)) {
                    return nodeB
                }
                nodeB = nodeB.next
            }

            return null
        }


    }
}

class ListNode4(var value: Int) {
    var next: ListNode4? = null
}















