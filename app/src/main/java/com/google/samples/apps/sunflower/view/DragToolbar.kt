/*
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import androidx.customview.widget.ViewDragHelper
import com.google.samples.apps.sunflower.R
import kotlin.math.abs
import kotlin.math.max

class DragToolbar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : FrameLayout(context, attrs, defStyle) {

    private val dragHelper = ViewDragHelper.create(this, 1f, DragCallback())
    private var lastAction = MotionEvent.ACTION_DOWN
    private var startX = 0f
    private var startY = 0f

    // 边界限制
    private var parentWidth = 0
    private var parentHeight = 0

    // 按钮布局参数
    private val buttonMargin = 8.dp
    private var buttonCount = 0

    init {
        isClickable = true
        setWillNotDraw(false)
        clipChildren = false
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        post { updateParentDimensions() }
    }

    private fun updateParentDimensions() {
        (parent as? ViewGroup)?.let {
            parentWidth = it.width - width
            parentHeight = it.height - height
            parentWidth = max(0, parentWidth - 16.dp)
            parentHeight = max(0, parentHeight - 16.dp)
        }
    }

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        return when (ev.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                startX = ev.x
                startY = ev.y
                dragHelper.shouldInterceptTouchEvent(ev)
            }
            MotionEvent.ACTION_MOVE -> {
                val dx = abs(ev.x - startX)
                val dy = abs(ev.y - startY)
                dx > 10 || dy > 10 || dragHelper.shouldInterceptTouchEvent(ev)
            }
            else -> super.onInterceptTouchEvent(ev)
        }
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        dragHelper.processTouchEvent(event)
        when (event.actionMasked) {
            MotionEvent.ACTION_UP -> {
                if (isClickAction(event)) performClick()
                lastAction = MotionEvent.ACTION_UP
            }
        }
        return true
    }

    private inner class DragCallback : ViewDragHelper.Callback() {
        override fun tryCaptureView(child: View, pointerId: Int): Boolean {
            return child == this@DragToolbar
        }

        override fun clampViewPositionHorizontal(child: View, left: Int, dx: Int): Int {
            return left.coerceIn(0, parentWidth)
        }

        override fun clampViewPositionVertical(child: View, top: Int, dy: Int): Int {
            return top.coerceIn(0, parentHeight)
        }

        override fun onViewReleased(releasedChild: View, xvel: Float, yvel: Float) {
            // 实现惯性滑动[8](@ref)
            dragHelper.settleCapturedViewAt(
                if (releasedChild.left < parentWidth/2) 0 else parentWidth,
                releasedChild.top.coerceIn(0, parentHeight)
            )
            invalidate()
        }

        override fun getViewHorizontalDragRange(child: View) = width
        override fun getViewVerticalDragRange(child: View) = height
    }

    // 添加按钮方法（解决重叠问题）
    fun addButton(icon: Drawable?, onClick: () -> Unit) {
        if (icon == null) return
        val btn = ImageButton(context).apply {
            setImageDrawable(icon)
            scaleType = ImageView.ScaleType.CENTER_INSIDE
            setOnClickListener { if (!isDragging) onClick() }
            background = ContextCompat.getDrawable(context, R.drawable.btn_selector)
        }

        val params = LayoutParams(60.dp, 60.dp).apply {
            gravity = Gravity.START or Gravity.TOP
            marginStart = buttonMargin + (buttonCount % 3) * 68.dp
            topMargin = buttonMargin + (buttonCount / 3) * 68.dp
        }
        addView(btn, params)
        buttonCount++
    }

    override fun computeScroll() {
        if (dragHelper.continueSettling(true)) invalidate()
    }

    private val isDragging get() = dragHelper.viewDragState == ViewDragHelper.STATE_DRAGGING

    private fun isClickAction(event: MotionEvent): Boolean {
        val dx = abs(event.x - startX)
        val dy = abs(event.y - startY)
        return dx < 10 && dy < 10 && !isDragging
    }

    private val Int.dp: Int get() = (this * resources.displayMetrics.density).toInt()
}