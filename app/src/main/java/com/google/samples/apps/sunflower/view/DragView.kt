/*
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.view


import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.app.Activity
import android.app.Application
import android.content.Context
import android.content.Context.MODE_PRIVATE
import android.content.res.Resources
import android.util.AttributeSet
import android.util.DisplayMetrics
import android.util.Log
import android.view.MotionEvent
import android.view.MotionEvent.ACTION_CANCEL
import android.view.MotionEvent.ACTION_DOWN
import android.view.MotionEvent.ACTION_MOVE
import android.view.MotionEvent.ACTION_UP
import android.view.ViewConfiguration
import android.view.ViewTreeObserver
import android.view.animation.AccelerateInterpolator
import android.widget.RelativeLayout
import kotlin.math.absoluteValue


class DragView
@JvmOverloads
constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RelativeLayout(context, attrs, defStyleAttr) {


    companion object {
        const val TAG = "DragView"
    }

    private var sharedPreferences =
        context.getSharedPreferences("drag_view_configuration", MODE_PRIVATE)

    private var innerX: Float = 0f
    private var innerY: Float = 0f

    //距离右边缘距离
    private var padingRight: Int = 10.px
    private var type = -1


    private val displayHeight = context.displayScreenHeight()
    private val displayWidth = context.displayScreenWidth()
    private val statusBarHeight = context.statusBarHeight()
    private var mLastY = -1f
    private var changeY = false
    private val globalLayoutImpl = GlobalLayoutImpl()


    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        Log.d(TAG, "${hashCode()} on attach window")
        gotoLastPosition()
        viewTreeObserver.addOnGlobalLayoutListener(globalLayoutImpl)

    }

    fun onHiddenChanged(hidden: Boolean, isResume: Boolean) {
        if (!hidden) {
            gotoLastPosition()
        }
    }


    private fun gotoLastPosition() {
        val (lastX, lastY) = getLastPosition()
        Log.d(TAG, "${hashCode()} to last position: [$lastX,$lastY]")

        if (lastX < 0f || lastY < 0f || lastX >= displayWidth || lastY >= displayHeight) {
            post {
                var height = 0
                if (height == 0 || height == null) {
                    if (type < 0) {
                        x = 0f
                        y = (displayHeight / 2).toFloat()
                        mLastY = y
                        changeY = true
                    } else if (type == 1) {
                        if (left > 0) {
                            x = left.toFloat()
                        } else {
                            x = (displayWidth - 35.f_px)
                        }
                        y = 240.f_px
                        recordY()
                    } else if (type == 2) {
                        if (left > 0) {
                            x = left.toFloat()
                        } else {
                            x = 0f
                        }
                        y = (displayHeight / 2).toFloat()
                        recordY()
                    }
                } else {
                    post {
                        y = height.f_px
                        x = 0f
                        recordY()
                    }
                }
            }
        } else {
            post {
                x = lastX
                y = lastY
                recordY()
            }
        }
    }

    private fun recordY() {
        changeY = true
        mLastY = y
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        Log.d(TAG, "${hashCode()} on detach window")

    }


    private fun isLeftTop(): Boolean {
        return x < displayWidth / 2 && y < displayHeight / 2
    }


    private fun isLeftBottom(): Boolean {
        return x < displayWidth / 2 && y >= displayHeight / 2
    }


    override fun dispatchTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            ACTION_DOWN -> {
                innerX = event.x
                innerY = event.y
                parent.requestDisallowInterceptTouchEvent(true)
            }
            ACTION_UP, ACTION_CANCEL -> {
                parent.requestDisallowInterceptTouchEvent(false)
            }
        }
        return super.dispatchTouchEvent(event)
    }


    override fun onInterceptTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            ACTION_MOVE -> {
                val offsetX = (event.x - innerX).absoluteValue
                val offsetY = (event.y - innerY).absoluteValue
                val touchSlop = ViewConfiguration.get(context).scaledTouchSlop
                if (offsetX > touchSlop || offsetY > touchSlop) {
                    return true
                }
            }
        }
        return super.onInterceptTouchEvent(event)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            ACTION_DOWN -> {
                return true
            }
            ACTION_MOVE -> {
                parent.requestDisallowInterceptTouchEvent(true)

                val offsetX = event.rawX - innerX
                val offsetY = event.rawY - innerY

                val rightEdge = displayWidth - measuredWidth
                val topEdge = statusBarHeight
                val bottomEdge = displayHeight - measuredHeight - 44.f_px

                x = when {
                    offsetX <= 0 -> 0f
                    offsetX >= rightEdge -> rightEdge.toFloat()
                    else -> offsetX
                }
                y = when {
                    offsetY <= topEdge -> topEdge.toFloat()
                    offsetY <= 0 -> 0f
                    offsetY >= bottomEdge -> bottomEdge.toFloat()
                    else -> offsetY
                }
            }
            ACTION_CANCEL, ACTION_UP -> {
                autoEdge(event)
                parent.requestDisallowInterceptTouchEvent(false)
                return true
            }
        }
        return false
    }

    private fun autoEdge(event: MotionEvent) {
        val offsetX = event.rawX - innerX
        val rightEdge = displayWidth - measuredWidth - padingRight
        val startX =
            if (offsetX <= 0) 0f else if (offsetX >= rightEdge) rightEdge.toFloat() else offsetX
        if (event.rawX > displayWidth / 2) {
            animateToEdge(startX, rightEdge.toFloat())
        } else {
            animateToEdge(startX, 0f)
        }
    }

    private fun animateToEdge(start: Float, end: Float) {
        val valueAnimator = ValueAnimator.ofFloat(start, end)
        valueAnimator.duration = 200
        valueAnimator.interpolator = AccelerateInterpolator()
        valueAnimator.addUpdateListener { animation ->
            val value = animation.animatedValue as Float
            x = value
        }
        valueAnimator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {

                saveLastPosition(x, y)
            }
        })
        valueAnimator.start()
    }


    private fun getLastPosition(): Pair<Float, Float> {
        if (!sharedPreferences.getBoolean("userChanged", false)) {
            return Pair(-1f, -1f)
        }
        val lastX = sharedPreferences.getFloat("last_x", -1f)
        val lastY = sharedPreferences.getFloat("last_y", -1f)
        return Pair(lastX, lastY)
    }

    @Synchronized
    private fun saveLastPosition(lastX: Float, lastY: Float) {
        val editor = sharedPreferences.edit()
        editor.putFloat("last_x", lastX)
        editor.putFloat("last_y", lastY)
        editor.putBoolean("userChanged", true)
        editor.commit()
        Log.d(TAG, "${hashCode()} save last position: [$lastX,$lastY]")
    }


    private inner class GlobalLayoutImpl : ViewTreeObserver.OnGlobalLayoutListener {
        override fun onGlobalLayout() {
            if (changeY) {
                if (mLastY > 0 && y != mLastY) {
                    y = mLastY
                    viewTreeObserver.removeOnGlobalLayoutListener(globalLayoutImpl)
                    return
                }
            }
        }
    }
}



/**
 * 获取当前屏幕显示区域的宽度
 */
fun Context.displayScreenWidth(): Int {
    return getDisplayMetrics().widthPixels
}

/**
 * 获取当前屏幕显示区域的高度, 包括statusbar, 不包括navigation bar
 */
fun Context.displayScreenHeight(): Int {
    return getDisplayMetrics().heightPixels
}

/**
 * 获取状态栏的高度
 */
fun Context.statusBarHeight(): Int {
    var height = 0
    val resId = this.resources.getIdentifier("status_bar_height", "dimen", "android")
    if (resId > 0) {
        height = this.resources.getDimensionPixelSize(resId)
    }
    return height
}


private fun Context.getDisplayMetrics(): DisplayMetrics {
    when {
        this is Application -> return  this.resources.displayMetrics
        this is Activity -> {
            val windowManager = this.windowManager
            val d = windowManager.defaultDisplay

            val displayMetrics = DisplayMetrics()
            d.getMetrics(displayMetrics)
            return displayMetrics
        }

    }
    return DisplayMetrics()
}

val Int.px: Int
    get() = (this * Resources.getSystem().displayMetrics.density).toInt()

val Int.f_px: Float
    get() = (this * Resources.getSystem().displayMetrics.density)