/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.leetcode

import android.util.Printer
import com.google.samples.apps.sunflower.leetcode.bean.ListNode
import okhttp3.internal.notify
import okhttp3.internal.wait

class Demo {
    companion object {
        @JvmStatic
        fun main(args: Array<String>) {

            //创建链表 A：1->2->3->4->5
            val nodeA1 = ListNode(1)
            val nodeA2 = ListNode(2)
            val nodeA3 = ListNode(3)
            val nodeA4 = ListNode(4)
            val nodeA5 = ListNode(5)
            nodeA1.next = nodeA2
            nodeA2.next = nodeA3
            nodeA3.next = nodeA4
            nodeA4.next = nodeA5
            //创建链表 B：6->7->8，然后与链表A的节点2相交
//            val nodeB1 = ListNode(6)
//            val nodeB2 = ListNode(7)
//            val nodeB3 = ListNode(8)
//            nodeB1.next = nodeB2
//            nodeB2.next = nodeB3
//            nodeB3.next = nodeA2

//            printIntersectionResult(nodeA1, nodeB1)

            var reverseList2 = reverseList2(nodeA1)
            while (reverseList2 != null) {
                println("reverseList2.value=${reverseList2.value}")
                reverseList2 = reverseList2.next
            }


//            val printer = Printer()
//            printer.printNumbers()


//            val nums = intArrayOf(0, 1, 0, 3, 1, 2)
//            moveZeroes(nums)
//            println("After moving zeros: ${nums.contentToString()}")


        }

        /**
         * 移动0
         */
        fun moveZeroes(nums: IntArray) {
            var lastNonZeroFountAt = 0
            for (i in nums.indices) {
                if (nums[i] != 0) {
                    nums[lastNonZeroFountAt] = nums[i]
                    lastNonZeroFountAt++
                }
            }
            for (i in lastNonZeroFountAt until nums.size) {
                nums[i] = 0
            }
        }

        /**
         * 判断相交链表
         */
        fun getIntersectionNode(headA: ListNode?, headB: ListNode?): ListNode? {
            var a = headA
            var b = headB
            if (a == null || b == null) {
                return null
            }
            //创建链表 A：1->2->3->4->5          a=1 c=4
            //创建链表 B：6->7->8->2->3->4->5，然后与链表A的节点1相交 b=3 c=4
            //创建链表 A：1->2->3->4->5->null->6->7   ->8->2->3->4   ->5->null
            //创建链表 B：6->7->8->2->3->4   ->5->null->6->7->8
            while (a != b) {
//                println("a.value= ${a?.value}")
                println("a.value= ${a?.value}；b.value= ${b?.value}；headA.value= ${headA?.value}；headB.value= ${headB?.value}")
                a = if (a == null) {
                    headB
                } else {
                    a.next
                }
//                println("b.value= ${b?.value}")
                b = if (b == null) {
                    headA
                } else {
                    b.next
                }
            }
            println("a.value= ${a?.value}；b.value= ${b?.value}；headA.value= ${headA?.value}；headB.value= ${headB?.value}")
            return a
        }

        fun printIntersectionResult(headA: ListNode?, headB: ListNode?) {
            val intersectionNode = getIntersectionNode2(headA, headB)
            if (intersectionNode == null) {
                println("两个链表不相交")
            } else {
                println("两个链表相交于值为 ${intersectionNode.value} 的节点")
            }
        }

        fun getIntersectionNode2(headA: ListNode?, headB: ListNode?): ListNode? {
            val visited = HashSet<ListNode>()
            var temp = headA
            while (temp != null) {
                visited.add(temp)
                temp = temp.next
            }
            temp = headB
            while (temp != null) {
                if (visited.contains(temp)) {
                    return temp
                }
                temp = temp.next
            }
            return null
        }


        /**
         * 反转链表
         * 1->2->3->4->5->null
         * 5->4->3->2->1->null
         */
        fun reverseList(head: ListNode?): ListNode? {
            var prev: ListNode? = null
            var curr: ListNode? = head
            while (curr != null) {
                println("curr.value= ${curr.value} ")
                val next: ListNode? = curr.next
                curr.next = prev
                prev = curr
                curr = next
            }
            return prev

        }

        fun reverseList2(head: ListNode?): ListNode? {
            var prev: ListNode? = null
            var curr: ListNode? = head
            while (curr != null) {
                val next = curr.next

                curr.next = prev
                prev = curr

                curr = next


            }



            return prev

        }
    }
}

class Printer {
    private var number = 1
    private val lock = Any()
    private var printOdd = true//true:线程 1 打印奇数，线程 2 打印偶数。false:线程 2 打印奇数，线程 1 打印偶数。

    fun printNumbers() {
        Thread {
            while (number <= 99) {
                synchronized(lock) {
                    if (!printOdd) {
                        try {

                            lock.wait()
                        } catch (e: InterruptedException) {
                            e.printStackTrace()
                        }
                    }
                    if (number <= 99) {
                        println("Thread 1: ${number++}")
                        printOdd = false
                        lock.notify()
                    }
                }
            }
        }.start()


        Thread {
            while (number <= 99) {
                synchronized(lock) {
                    if (printOdd) {
                        try {
                            lock.wait()
                        } catch (e: InterruptedException) {
                            e.printStackTrace()
                        }
                    }

                    if (number <= 99) {
                        println("Thread 2: ${number++}")
                        printOdd = true
                        lock.notify()
                    }


                }
            }
        }.start()
    }

}















