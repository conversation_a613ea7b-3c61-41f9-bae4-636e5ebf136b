/*
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.span


import android.graphics.Canvas
import android.graphics.Color
import android.graphics.DashPathEffect
import android.graphics.Paint
import android.text.Spannable
import android.text.SpannableString
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.LineBackgroundSpan
import android.view.View
import android.widget.TextView
import androidx.annotation.ColorInt

class DashedUnderlineSpan(
    @ColorInt private val color: Int = Color.BLACK,
    private val dashWidth: Float = 4f,  // 虚线线段长度
    private val dashGap: Float = 4f,    // 虚线间隔
    private val strokeWidth: Float = 1f // 虚线宽度
) : ClickableSpan(), LineBackgroundSpan {

    // 新增：保存目标文本范围
    private var targetStart: Int = 0
    private var targetEnd: Int = 0

    fun setTargetRange(start: Int, end: Int) {
        targetStart = start
        targetEnd = end
    }


    private var onClickListener: (() -> Unit)? = null

    // 点击事件回调
    fun setOnClickListener(listener: () -> Unit) {
        this.onClickListener = listener
    }

    override fun drawBackground(
        canvas: Canvas, paint: Paint,
        left: Int, right: Int,
        top: Int, baseline: Int, bottom: Int,
        text: CharSequence, start: Int, end: Int,
        lineNumber: Int
    ) {
        // 保存 Paint 原始状态
        val originalColor = paint.color
        val originalStrokeWidth = paint.strokeWidth
        val originalPathEffect = paint.pathEffect
        val originalStyle = paint.style

        // 配置虚线样式
        paint.apply {
            color = <EMAIL>
            strokeWidth = <EMAIL>
            pathEffect = DashPathEffect(
                floatArrayOf(
                    dashWidth.coerceAtLeast(1f),
                    dashGap.coerceAtLeast(1f)
                ),
                0f
            )
            style = Paint.Style.STROKE
        }

        // 关键修正：精准计算目标文本的起始位置
        val textWidth = paint.measureText(text.subSequence(start, end).toString())
        val startX = left + paint.measureText(text.subSequence(0, start).toString())

        // 绘制虚线（仅覆盖目标文本）
        canvas.drawLine(
            startX,
            baseline + strokeWidth * 2, // 调整基线偏移量
            startX + textWidth,
            baseline + strokeWidth * 2,
            paint
        )

        // 恢复 Paint 原始状态
        paint.apply {
            color = originalColor
            strokeWidth = originalStrokeWidth
            pathEffect = originalPathEffect
            style = originalStyle
        }
    }

//    override fun drawBackground(
//        canvas: Canvas, paint: Paint,
//        left: Int, right: Int,
//        top: Int, baseline: Int, bottom: Int,
//        text: CharSequence, start: Int, end: Int,
//        lineNumber: Int
//    ) {
//        // 保存原始 Paint 状态
//        val originalColor = paint.color
//        val originalStrokeWidth = paint.strokeWidth
//        val originalPathEffect = paint.pathEffect
//        val originalStyle = paint.style
//
//        // 配置虚线样式
//        paint.apply {
//            color = <EMAIL>
//            strokeWidth = <EMAIL>
//            pathEffect = DashPathEffect(
//                floatArrayOf(
//                    dashWidth.coerceAtLeast(1f),
//                    dashGap.coerceAtLeast(1f)
//                ),
//                0f
//            )
//            style = Paint.Style.STROKE
//        }
//
//        // 绘制虚线（兼容低版本）
//        val y = baseline + strokeWidth
//        canvas.drawLine(left.toFloat(), y, right.toFloat(), y, paint)
//
//        // 恢复 Paint 状态
//        paint.apply {
//            color = originalColor
//            strokeWidth = originalStrokeWidth
//            pathEffect = originalPathEffect
//            style = originalStyle
//        }
//    }


    // 处理点击事件
    override fun onClick(widget: View) {
        onClickListener?.invoke()
    }

    // 更新文本样式（可选：点击时文字颜色变化）
    override fun updateDrawState(ds: TextPaint) {
        super.updateDrawState(ds)
        ds.isUnderlineText = false // 禁用默认下划线
    }
}

class DashedUnderlineSpan2(
    @ColorInt private val color: Int = Color.BLACK,
    private val dashWidth: Float = 4f,
    private val dashGap: Float = 4f,
    private val strokeWidth: Float = 1f
) : ClickableSpan(), LineBackgroundSpan {

    private var onClickListener: (() -> Unit)? = null
    private var targetText: String = "" // 新增：存储目标文本

    // 设置目标文本（关键修改）
    fun setTargetText(text: String) {
        this.targetText = text
    }

    override fun drawBackground(
        canvas: Canvas, paint: Paint,
        left: Int, right: Int,
        top: Int, baseline: Int, bottom: Int,
        text: CharSequence, start: Int, end: Int,
        lineNumber: Int
    ) {
        // 仅当是目标文本时才绘制
        if (text.subSequence(start, end).toString() == targetText) {
            val originalPaint = Paint(paint) // 复制原始Paint对象

            // 计算目标文本的起始X坐标（关键修正）
            val textBefore = text.subSequence(0, start).toString()
            val startX = left + paint.measureText(textBefore)

            // 计算目标文本宽度
            val textWidth = paint.measureText(targetText)

            // 配置虚线样式
            paint.apply {
                this.color = <EMAIL>
                strokeWidth = <EMAIL>
                pathEffect = DashPathEffect(floatArrayOf(dashWidth, dashGap), 0f)
                style = Paint.Style.STROKE
            }

            // 绘制虚线（仅覆盖目标文本）
            val y = baseline + strokeWidth * 2
            canvas.drawLine(startX, y, startX + textWidth, y, paint)

            // 恢复原始Paint（避免影响其他Span）
            paint.set(originalPaint)
        }
    }

    override fun onClick(widget: View) {
        onClickListener?.invoke()
    }

    // 更新文本样式（可选：点击时文字颜色变化）
    override fun updateDrawState(ds: TextPaint) {
        super.updateDrawState(ds)
        ds.isUnderlineText = false // 禁用默认下划线
    }
}

fun TextView.setDashedUnderlineText(
    fullText: String,
    targetText: String, // 明确指定需要加虚线的文本
    @ColorInt color: Int = Color.BLUE,
    dashWidth: Float = 4f,
    dashGap: Float = 4f,
    strokeWidth: Float = 1f,
    onClick: () -> Unit
) {
    // 确保禁用硬件加速
    setLayerType(View.LAYER_TYPE_SOFTWARE, null)

    val spannable = SpannableString(fullText)
    val start = fullText.indexOf(targetText)
    val end = start + targetText.length

    if (start >= 0) {
        val span = DashedUnderlineSpan2(color, dashWidth, dashGap, strokeWidth).apply {
            setTargetText(targetText) // 关键：设置目标文本
            setOnClickListener{onClick()}
        }
        spannable.setSpan(span, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
    }

    this.text = spannable
    movementMethod = LinkMovementMethod.getInstance()
}