/*
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.view

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.widget.Button
import android.widget.LinearLayout
import kotlin.math.abs

class DraggableToolbar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private var lastX = 0f
    private var lastY = 0f

    init {
        orientation = HORIZONTAL
    }

    @SuppressLint("ClickableViewAccessibility")
    fun addButton(text: String, onClick: () -> Unit) {
        val button = Button(context)
        button.text = text
        button.setOnClickListener {
            onClick()
        }
        button.setOnTouchListener { _, event ->
            onTouchEvent(event)
            false
        }
        addView(button)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        val x = event.rawX
        val y = event.rawY
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                lastX = x
                lastY = y
            }

            MotionEvent.ACTION_MOVE -> {
                val dx = x - lastX
                val dy = y - lastY

                var newLeft = (left + dx).toInt()
                var newTop = (top + dy).toInt()
                var newRight = (right + dx).toInt()
                var newBottom = (bottom + dy).toInt()

                //确保工具条在父布局内
                val parent = parent as View
                newLeft = newLeft.coerceIn(0, parent.width - width)
                newTop = newTop.coerceIn(0, parent.height - height)
                newRight = newLeft + width
                newBottom = newTop + height

                layout(newLeft, newTop, newRight, newBottom)
                lastX = x
                lastY = y
            }

            MotionEvent.ACTION_UP -> {
            }

        }
        return true

    }

}