/*
 * Copyright 2018 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.utilities

/**
 * Constants used throughout the app.
 */
const val DATABASE_NAME = "sunflower-db"
const val PLANT_DATA_FILENAME = "plants.json"
//const val ZL_DATABASE_NAME = "zl-sunflower-db"
const val ZL_DATA_FILENAME = "zl_plants.json"
