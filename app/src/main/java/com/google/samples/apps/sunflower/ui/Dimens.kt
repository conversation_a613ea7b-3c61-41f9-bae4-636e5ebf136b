package com.google.samples.apps.sunflower.ui

import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

val ToolBarHeight = 48.dp
val TabBarHeight = 48.dp
val SearchBarHeight = 42.dp
val BottomNavBarHeight = 56.dp
val ListTitleHeight = 30.dp

val PrimaryButtonHeight = 36.dp
val MediumButtonHeight = 28.dp
val SmallButtonHeight = 28.dp

val H1 = 46.sp  //超大号标题
val H2 = 36.sp  //大号标题
val H3 = 24.sp  //主标题
val H4 = 20.sp  //普通标题
val H5 = 16.sp  //内容文本
val H6 = 13.sp  //普通文字尺寸
val H7 = 12.sp  //提示语尺寸

val ToolBarTitleSize = 18.sp

val cardCorner = 5.dp   //卡片的圆角
val buttonCorner = 3.dp //按钮的圆角
val buttonHeight = 36.dp //按钮的高度
