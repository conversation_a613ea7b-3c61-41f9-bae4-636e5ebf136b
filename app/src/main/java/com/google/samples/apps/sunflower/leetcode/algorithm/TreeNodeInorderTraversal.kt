/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.leetcode.algorithm

/**
 * 二叉树的中序遍历 返回List
 */
class TreeNodeInorderTraversal {
    companion object {
        @JvmStatic
        fun main(args: Array<String>) {

            val root = TreeNode(1)
            val rootRight = TreeNode(2)
            root.right = rootRight
            rootRight.left = TreeNode(3)

            val result: List<Int> = inorderTraversal(root)
            println(result)

        }

        private fun inorderTraversal(root: TreeNode?): List<Int> {
            val result = mutableListOf<Int>()
            inorder(root, result)
            return result
        }

        private fun inorder(root: TreeNode?, result: MutableList<Int>) {
//            println("1: ${root?.value}")
            if (root == null) return
            println("root.left.value: ${root.left?.value}")
            inorder(root.left, result)
            println("root.value: ${root.value}")
            result.add(root.value)
            println("root.right.value: ${root.right?.value}")
            inorder(root.right, result)
        }

    }
}

//data class TreeNode(var value: Int, var left: TreeNode? = null, var right: TreeNode? = null)
class TreeNode(var value: Int) {
    var left: TreeNode? = null
    var right: TreeNode? = null
}















