/*
 * Copyright 2018 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.data

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ZLGardenPlantingRepository @Inject constructor(
    private val gardenPlantingDao: GardenPlantingDao
) {

    suspend fun createGardenPlanting(plantId: String) {
        val gardenPlanting = GardenPlanting(plantId)
        gardenPlantingDao.insertGardenPlanting(gardenPlanting)
    }

    suspend fun removeGardenPlanting(gardenPlanting: GardenPlanting) {
        gardenPlantingDao.deleteGardenPlanting(gardenPlanting)
    }

    fun isPlanted(plantId: String) =
        gardenPlantingDao.isPlanted(plantId)

    fun getPlantedGardens() = gardenPlantingDao.getPlantedGardens()

    fun getAlgorithms(): Flow<List<AlgorithmBean>> {
        val list = mutableListOf<AlgorithmBean>()
        list.add(AlgorithmBean(Algorithm.ID_10009, "跳转原生页面-DemoActivity"))
        list.add(AlgorithmBean(Algorithm.ID_10008, "跳转原生页面-输入框"))
        list.add(AlgorithmBean(Algorithm.ID_10007, "【滑动窗口】找到字符串中所有字母异位词"))
        list.add(AlgorithmBean(Algorithm.ID_10006, "【滑动窗口】无重复字符的最长子串"))
        list.add(AlgorithmBean(Algorithm.ID_10005, "【双指针】15.三数之和"))
        list.add(AlgorithmBean(Algorithm.ID_10004, "【哈希】128.最长连续序列"))
        list.add(AlgorithmBean(Algorithm.ID_10003, "【双指针】283. 移动零"))
        list.add(AlgorithmBean(Algorithm.ID_10002, "【哈希】1.两数之和"))
        list.add(AlgorithmBean(Algorithm.ID_10001, "【哈希】49.字母异位词分组"))
        return flowOf(list)
    }

    companion object {

        // For Singleton instantiation
        @Volatile
        private var instance: ZLGardenPlantingRepository? = null

        fun getInstance(gardenPlantingDao: GardenPlantingDao) =
            instance ?: synchronized(this) {
                instance ?: ZLGardenPlantingRepository(gardenPlantingDao).also { instance = it }
            }
    }
}
