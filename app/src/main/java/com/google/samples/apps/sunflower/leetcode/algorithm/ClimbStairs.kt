/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.leetcode.algorithm

class ClimbStairs {
    companion object {
        @JvmStatic
        fun main(args: Array<String>) {

            val n1 = 2
            val n2 = 3
            val n3 = 5
            println("爬到第 $n1 阶楼梯的方法数：${climbStairs2(n1)}")
            println("爬到第 $n2 阶楼梯的方法数：${climbStairs2(n2)}")
            println("爬到第 $n3 阶楼梯的方法数：${climbStairs2(n3)}")

        }


        fun climbStairs(n: Int): Int {
            val dp = IntArray(n + 1)
            dp[0] = 1
            dp[1] = 1
            for (i in 2..n) {
                dp[i] = dp[i - 1] + dp[i - 2]
            }
            return dp[n]
        }

        fun climbStairs2(n: Int): Int {
            var dp = IntArray(n + 1)
            dp[0] = 1
            dp[1] = 1
            for (i in 2..n) {
                dp[i] = dp[i - 1] + dp[i - 2]
            }

            return dp[n]
        }


    }
}