/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.leetcode.algorithm


fun main() {
    val head = ListNode9(1)
    var cur = head
    for (i in 2..5) {
        val node = ListNode9(i)
        cur.next = node
        cur = node
    }
    val k = 2
    val result = findKthToTail(head, k)
    if (result != null) {
        println("倒数第${k}个节点的值是：${result.value}")
    } else {
        println("输入的k值不合法或者链表为空")
    }
}

fun findKthToTail(head: ListNode9?, k: Int): ListNode9? {
    if (head == null) {
        return null
    }
    var fast: ListNode9? = head
    var slow: ListNode9? = head
    //快指针先移动k步
    for (i in 1..k) {
        if (fast == null) {
            return null
        }
        fast = fast.next
    }
    //快慢指针同时移动。知道快指针到达链表末尾
    while (fast != null) {
        fast = fast.next
        slow = slow?.next
    }

    return slow
}

class ListNode9(var value: Int) {
    var next: ListNode9? = null
}