/*
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.compose.zlplant

import android.content.Intent
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.bumptech.glide.integration.compose.ExperimentalGlideComposeApi
import com.google.samples.apps.sunflower.MainApplication
import com.google.samples.apps.sunflower.R
import com.google.samples.apps.sunflower.activity.DemoActivity
import com.google.samples.apps.sunflower.activity.EditActivity
import com.google.samples.apps.sunflower.data.AlgorithmBean
import com.google.samples.apps.sunflower.leetcode.Algorithm
import com.google.samples.apps.sunflower.ui.black
import com.google.samples.apps.sunflower.ui.red2
import com.google.samples.apps.sunflower.ui.widgets.Item
import com.wordsfairy.base.tools.toast
import com.wordsfairy.base.utils.GsonUtils
import com.wordsfairy.note.ui.widgets.toast.ToastModel
import com.wordsfairy.note.ui.widgets.toast.showToast
import net.engawapg.lib.zoomable.rememberZoomState
import net.engawapg.lib.zoomable.zoomable

@OptIn(
    ExperimentalGlideComposeApi::class
)


@Composable
fun ZLGardenListItem10009(
    plant: AlgorithmBean
) {
    val cardSideMargin = dimensionResource(id = R.dimen.card_side_margin)
    val marginNormal = dimensionResource(id = R.dimen.margin_normal)
    val context = LocalContext.current


    OutlinedCard(
        modifier = Modifier.padding(
            start = cardSideMargin,
            end = cardSideMargin,
            bottom = dimensionResource(id = R.dimen.card_bottom_margin)
        ),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.secondaryContainer),
        border = BorderStroke(1.dp, black),

        ) {
        Column(Modifier.fillMaxWidth()) {
            Text(
                text = plant.name,
                Modifier
                    .padding(top = marginNormal)
                    .align(Alignment.Start),
                style = MaterialTheme.typography.titleSmall,
                color = red2,
            )

            Button(
                shape = MaterialTheme.shapes.medium,
                onClick = {
                    val intent = Intent(context, DemoActivity::class.java)
                    context.startActivity(intent)
                }
            ) {
                Text(text = "跳转")
            }

        }
    }
}

@Composable
fun ZLGardenListItem10008(
    plant: AlgorithmBean
) {
    val cardSideMargin = dimensionResource(id = R.dimen.card_side_margin)
    val marginNormal = dimensionResource(id = R.dimen.margin_normal)
    val context = LocalContext.current


    OutlinedCard(
        modifier = Modifier.padding(
            start = cardSideMargin,
            end = cardSideMargin,
            bottom = dimensionResource(id = R.dimen.card_bottom_margin)
        ),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.secondaryContainer),
        border = BorderStroke(1.dp, black),

        ) {
        Column(Modifier.fillMaxWidth()) {
            Text(
                text = plant.name,
                Modifier
                    .padding(top = marginNormal)
                    .align(Alignment.Start),
                style = MaterialTheme.typography.titleSmall,
                color = red2,
            )

            Button(
                shape = MaterialTheme.shapes.medium,
                onClick = {
                    val intent = Intent(context, EditActivity::class.java)
                    context.startActivity(intent)
                }
            ) {
                Text(text = "跳转")
            }

        }
    }
}




@Composable
fun ZLGardenListItem10007(
    plant: AlgorithmBean
) {
//    val vm = PlantAndGardenPlantingsViewModel(plant)

    // Dimensions
    val cardSideMargin = dimensionResource(id = R.dimen.card_side_margin)
    val marginNormal = dimensionResource(id = R.dimen.margin_normal)
    val s = "dcbaebabacd"
    val p = "abc"


    OutlinedCard(
        modifier = Modifier.padding(
            start = cardSideMargin,
            end = cardSideMargin,
            bottom = dimensionResource(id = R.dimen.card_bottom_margin)
        ),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.secondaryContainer),
        border = BorderStroke(1.dp, black),

        ) {
        Column(Modifier.fillMaxWidth()) {
            Text(
                text = plant.name,
                Modifier
                    .padding(top = marginNormal)
                    .align(Alignment.Start),
                style = MaterialTheme.typography.titleSmall,
                color = red2,
            )

            Item(title = "输入内容", content = "s:$s, p:$p")

            Button(
                shape = MaterialTheme.shapes.medium,
                onClick = {

                    ToastModel(
                        message = "这些子串的起始索引：" + GsonUtils.toJson(Algorithm.findAnagrams(s, p)),
                        type = ToastModel.Type.Info,
                        durationTime = 30000
                    ).showToast()
                }
            ) {
                Text(text = "执行")
            }

            val painter = painterResource(id = R.mipmap.ic_find_anagrams)
            val zoomState = rememberZoomState(contentSize = painter.intrinsicSize)

            Image(
                painter = painter,
                contentDescription = "Zoomable image",
                contentScale = ContentScale.Fit,
                modifier = Modifier
                    .fillMaxSize()
                    .aspectRatio((1593f / 2086))
                    .zoomable(zoomState),
            )

        }
    }
}



@Composable
fun ZLGardenListItem10006(
    plant: AlgorithmBean
) {
//    val vm = PlantAndGardenPlantingsViewModel(plant)

    // Dimensions
    val cardSideMargin = dimensionResource(id = R.dimen.card_side_margin)
    val marginNormal = dimensionResource(id = R.dimen.margin_normal)
    val words = "pwwkeaw"


    OutlinedCard(
        modifier = Modifier.padding(
            start = cardSideMargin,
            end = cardSideMargin,
            bottom = dimensionResource(id = R.dimen.card_bottom_margin)
        ),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.secondaryContainer),
        border = BorderStroke(1.dp, black),

        ) {
        Column(Modifier.fillMaxWidth()) {
            Text(
                text = plant.name,
                Modifier
                    .padding(top = marginNormal)
                    .align(Alignment.Start),
                style = MaterialTheme.typography.titleSmall,
                color = red2,
            )

            Item(title = "输入内容", content = words)

            Button(
                shape = MaterialTheme.shapes.medium,
                onClick = {

                    ToastModel(
                        message = "不含有重复字符的最长子串的长度：" + Algorithm.lengthLongestSubstring3(
                            words
                        ),
                        type = ToastModel.Type.Info,
                        durationTime = 30000
                    ).showToast()
                }
            ) {
                Text(text = "执行")
            }

            val painter = painterResource(id = R.mipmap.ic_length_longest_substring)
            val zoomState = rememberZoomState(contentSize = painter.intrinsicSize)

            Image(
                painter = painter,
                contentDescription = "Zoomable image",
                contentScale = ContentScale.Fit,
                modifier = Modifier
                    .fillMaxSize()
                    .aspectRatio((1603f / 1604))
                    .zoomable(zoomState),
            )

        }
    }
}

@Composable
fun ZLGardenListItem10005(
    plant: AlgorithmBean
) {
//    val vm = PlantAndGardenPlantingsViewModel(plant)

    // Dimensions
    val cardSideMargin = dimensionResource(id = R.dimen.card_side_margin)
    val marginNormal = dimensionResource(id = R.dimen.margin_normal)
    val words = intArrayOf(-1, 0, 1, 2, -1, -4)


    OutlinedCard(
        modifier = Modifier.padding(
            start = cardSideMargin,
            end = cardSideMargin,
            bottom = dimensionResource(id = R.dimen.card_bottom_margin)
        ),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.secondaryContainer),
        border = BorderStroke(1.dp, black),

        ) {
        Column(Modifier.fillMaxWidth()) {
            Text(
                text = plant.name,
                Modifier
                    .padding(top = marginNormal)
                    .align(Alignment.Start),
                style = MaterialTheme.typography.titleSmall,
                color = red2,
            )

            Item(title = "输入内容", content = GsonUtils.toJson(words) ?: "")

            Button(
                shape = MaterialTheme.shapes.medium,
                onClick = {

                    ToastModel(
                        message = "所有和为0且不重复的三元组：" + Algorithm.threeSum2(words),
                        type = ToastModel.Type.Info,
                        durationTime = 30000
                    ).showToast()
                }
            ) {
                Text(text = "执行")
            }

            val painter = painterResource(id = R.mipmap.ic_three_sum)
            val zoomState = rememberZoomState(contentSize = painter.intrinsicSize)

            Image(
                painter = painter,
                contentDescription = "Zoomable image",
                contentScale = ContentScale.Fit,
                modifier = Modifier
                    .fillMaxSize()
                    .aspectRatio((1615f / 2510))
                    .zoomable(zoomState),
            )

        }
    }
}


@Composable
fun ZLGardenListItem10004(
    plant: AlgorithmBean
) {
//    val vm = PlantAndGardenPlantingsViewModel(plant)

    // Dimensions
    val cardSideMargin = dimensionResource(id = R.dimen.card_side_margin)
    val marginNormal = dimensionResource(id = R.dimen.margin_normal)
    val words = intArrayOf(0, 3, 7, 2, 5, 9, 4, 6, 0, 1)


    OutlinedCard(
        modifier = Modifier.padding(
            start = cardSideMargin,
            end = cardSideMargin,
            bottom = dimensionResource(id = R.dimen.card_bottom_margin)
        ),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.secondaryContainer),
        border = BorderStroke(1.dp, black),

        ) {
        Column(Modifier.fillMaxWidth()) {
            Text(
                text = plant.name,
                Modifier
                    .padding(top = marginNormal)
                    .align(Alignment.Start),
                style = MaterialTheme.typography.titleSmall,
                color = red2,
            )

            Item(title = "输入内容", content = GsonUtils.toJson(words) ?: "")

            Button(
                shape = MaterialTheme.shapes.medium,
                onClick = {

                    ToastModel(
                        message = "最长连续子列的长度是：" + Algorithm.longestConsecutive(words),
                        type = ToastModel.Type.Info,
                        durationTime = 60000
                    ).showToast()
                }
            ) {
                Text(text = "执行")
            }

            val painter = painterResource(id = R.mipmap.ic_longest_consecutive)
            val zoomState = rememberZoomState(contentSize = painter.intrinsicSize)

            Image(
                painter = painter,
                contentDescription = "Zoomable image",
                contentScale = ContentScale.Fit,
                modifier = Modifier
                    .fillMaxSize()
                    .aspectRatio((1056f / 1480))
                    .zoomable(zoomState),
            )

        }
    }
}


@OptIn(
    ExperimentalGlideComposeApi::class
)
@Composable
fun ZLGardenListItem10003(
    plant: AlgorithmBean
) {
//    val vm = PlantAndGardenPlantingsViewModel(plant)

    // Dimensions
    val cardSideMargin = dimensionResource(id = R.dimen.card_side_margin)
    val marginNormal = dimensionResource(id = R.dimen.margin_normal)
    val words = intArrayOf(0, 1, 0, 3, 12)


    ElevatedCard(
        modifier = Modifier.padding(
            start = cardSideMargin,
            end = cardSideMargin,
            bottom = dimensionResource(id = R.dimen.card_bottom_margin)
        ),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.secondaryContainer)
    ) {
        Column(Modifier.fillMaxWidth()) {
            Text(
                text = plant.name,
                Modifier
                    .padding(top = marginNormal)
                    .align(Alignment.Start),
                style = MaterialTheme.typography.titleSmall,
                color = red2,
            )

            Item(title = "输入内容", content = GsonUtils.toJson(words) ?: "")

            Button(
                shape = MaterialTheme.shapes.medium,
                onClick = {
                    Algorithm.moveZeroes3(words)
                    ToastModel(
                        message = GsonUtils.toJson(words) ?: "",
                        type = ToastModel.Type.Info,
                        durationTime = 60000
                    ).showToast()
                }
            ) {
                Text(text = "执行")
            }

            val painter = painterResource(id = R.mipmap.ic_move_zeroes)
            val zoomState = rememberZoomState(contentSize = painter.intrinsicSize)

            Image(
                painter = painter,
                contentDescription = "Zoomable image",
                contentScale = ContentScale.Fit,
                modifier = Modifier
                    .fillMaxSize()
                    .aspectRatio((1634f / 1140))
                    .zoomable(zoomState),
            )

        }
    }
}


@OptIn(
    ExperimentalGlideComposeApi::class
)
@Composable
fun ZLGardenListItem10002(

    plant: AlgorithmBean
) {
//    val vm = PlantAndGardenPlantingsViewModel(plant)

    // Dimensions
    val cardSideMargin = dimensionResource(id = R.dimen.card_side_margin)
    val marginNormal = dimensionResource(id = R.dimen.margin_normal)
    val words = intArrayOf(2, 7, 11, 15)
    var wordsJson by remember { mutableStateOf("") }


    ElevatedCard(
        modifier = Modifier.padding(
            start = cardSideMargin,
            end = cardSideMargin,
            bottom = dimensionResource(id = R.dimen.card_bottom_margin)
        ),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.secondaryContainer)
    ) {
        Column(Modifier.fillMaxWidth()) {
            Text(
                text = plant.name,
                Modifier
                    .padding(top = marginNormal)
                    .align(Alignment.Start),
                style = MaterialTheme.typography.titleSmall,
                color = red2,
            )

            Item(title = "输入内容", content = (GsonUtils.toJson(words) ?: "") + ", target=18")

            Button(
                shape = MaterialTheme.shapes.medium,
                onClick = {
                    ToastModel(
                        message = GsonUtils.toJson(Algorithm.twoSum4(words, 18)) ?: "",
                        type = ToastModel.Type.Info,
                        durationTime = 60000
                    ).showToast()
                }
            ) {
                Text(text = "执行")
            }

            val painter = painterResource(id = R.mipmap.ic_two_sum)
            val zoomState = rememberZoomState(contentSize = painter.intrinsicSize)

            Image(
                painter = painter,
                contentDescription = "Zoomable image",
                contentScale = ContentScale.Fit,
                modifier = Modifier
                    .fillMaxSize()
                    .aspectRatio(1640f / 1126)
                    .zoomable(zoomState),
            )

        }
    }
}


@OptIn(
    ExperimentalGlideComposeApi::class
)
@Composable
fun ZLGardenListItem10001(
    plant: AlgorithmBean
) {
//    val vm = PlantAndGardenPlantingsViewModel(plant)

    // Dimensions
    val cardSideMargin = dimensionResource(id = R.dimen.card_side_margin)
    val marginNormal = dimensionResource(id = R.dimen.margin_normal)
    val words = arrayOf("eat", "tea", "tan", "ate", "nat", "bat")
    var wordsJson by remember { mutableStateOf("") }


    ElevatedCard(
//        onClick = { onPlantClick(plant) },
        modifier = Modifier.padding(
            start = cardSideMargin,
            end = cardSideMargin,
            bottom = dimensionResource(id = R.dimen.card_bottom_margin)
        ),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.secondaryContainer)
    ) {
        Column(Modifier.fillMaxWidth()) {
            Text(
                text = plant.name,
                Modifier
                    .padding(vertical = marginNormal)
                    .align(Alignment.Start),
                style = MaterialTheme.typography.titleSmall,
                color = red2,
            )

            Item(title = "输入内容", content = GsonUtils.toJson(words) ?: "")

            Button(
                shape = MaterialTheme.shapes.medium,
                onClick = {
                    ToastModel(
                        message = GsonUtils.toJson(Algorithm.groupAnagrams5(words)) ?: "",
                        type = ToastModel.Type.Info,
                        durationTime = 60000
                    ).showToast()
                }
            ) {
                Text(text = "执行")
            }




            Text(
                text = "模式识别：一旦需要根据特征进行归类，就应该利用散列表",
                Modifier
                    .align(Alignment.CenterHorizontally)
                    .padding(top = 5.dp)
                    .clickable {
                        MainApplication.CONTEXT.toast("This is a simple toast!")
                    },
                style = MaterialTheme.typography.titleSmall,
            )


            val painter = painterResource(id = R.mipmap.ic_group_anagrams)
            val zoomState = rememberZoomState(contentSize = painter.intrinsicSize)

            Image(
                painter = painter,
                contentDescription = "Zoomable image",
                contentScale = ContentScale.Fit,
                modifier = Modifier
                    .fillMaxSize()
                    .aspectRatio(1.1415f)
                    .zoomable(zoomState),
            )

//            GlideImage(
//                model = R.mipmap.ic_group_anagrams,
//                contentDescription = plant.plant.description,
//                Modifier
//                    .fillMaxWidth()
//                    .aspectRatio(1.1415f),
//                contentScale = ContentScale.Crop,
//            )

            // Planted date
//            Text(
//                text = stringResource(id = R.string.plant_date_header),
//                Modifier.align(Alignment.CenterHorizontally),
//                style = MaterialTheme.typography.titleSmall
//            )
//            Text(
//                text = vm.plantDateString,
//                Modifier.align(Alignment.CenterHorizontally),
//                style = MaterialTheme.typography.labelSmall
//            )
//
//            // Last Watered
//            Text(
//                text = stringResource(id = R.string.watered_date_header),
//                Modifier
//                    .align(Alignment.CenterHorizontally)
//                    .padding(top = marginNormal),
//                style = MaterialTheme.typography.titleSmall
//            )
//            Text(
//                text = vm.waterDateString,
//                Modifier.align(Alignment.CenterHorizontally),
//                style = MaterialTheme.typography.labelSmall
//            )
//            Text(
//                text = pluralStringResource(
//                    id = R.plurals.watering_next,
//                    count = vm.wateringInterval,
//                    vm.wateringInterval
//                ),
//                Modifier
//                    .align(Alignment.CenterHorizontally)
//                    .padding(bottom = marginNormal),
//                style = MaterialTheme.typography.labelSmall
//            )
        }
    }
}


