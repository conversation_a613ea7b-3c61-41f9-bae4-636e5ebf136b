/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.leetcode.algorithm

class BinarySearch {
    companion object {
        @JvmStatic
        fun main(args: Array<String>) {




        }













        fun search(nums: IntArray, target: Int): Int {
            var left = 0
            var right = nums.size - 1
            while (left <= right) {
                val mid = (right - left) / 2 + left
                val num = nums[mid]
                if (num == target) {
                    return mid
                } else if (num > target) {
                    right = mid - 1
                } else {
                    left = mid + 1
                }
            }
            return -1
        }

        fun search2(nums: IntArray, target: Int): Int {

            var left = 0
            var right = nums.size - 1
            while (left <= right) {
                var mid = (right - left) / 2 + left
                var num = nums[mid]
                if (num == target) {
                    return mid
                } else if (target > num) {
                    left = mid + 1
                } else {
                    right = mid - 1
                }
            }

            return -1
        }

        fun binarySearch4(nums: IntArray,target: Int):Int{
            var left = 0
            var right = nums.size -1
            while (left<=right){
                var mid = (right -left)/2 + left
                var midNum = nums[mid]
                if (target == midNum){
                    return mid
                }else if(target > midNum){
                    left = mid + 1
                }else{
                    right = mid -1
                }
            }
            return -1
        }


//        fun binarySearch3(nums: IntArray, target: Int): Int {
//            var left = 0
//            var right = nums.size - 1
//
//            for (num in nums) {
//                var mid = (right - left) / 2 + left
//                var midNum = nums[mid]
//                if (num == target) {
//                    return mid
//                } else if (num < target) {
//                    left = mid + 1
//                }else{
//                    right = mid -1
//                }
//            }
//
//            return -1
//        }


    }
}