/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.leetcode.algorithm

import com.google.samples.apps.sunflower.leetcode.algorithm.SingleNum.Companion.singleNum
import com.google.samples.apps.sunflower.leetcode.algorithm.TwoSum.Companion.towSum

class MajorityElement {
    companion object {
        @JvmStatic
        fun main(args: Array<String>) {
            val nums1 = intArrayOf(3, 2, 3)
            val nums2 = intArrayOf(2, 2, 1, 1, 1, 2, 2)
            println(majorityElement2(nums1))
            println(majorityElement2(nums2))

        }


        fun majorityElement(nums: IntArray): Int {
            val countMap = mutableMapOf<Int, Int>()
            for (num in nums) {
                countMap[num] = countMap.getOrDefault(num, 0) + 1
            }
            val half = nums.size / 2
            for ((key, value) in countMap) {
                if (value > half) {
                    return key
                }
            }
            return -1
        }


        fun majorityElement2(nums: IntArray): Int {
            var countMap = mutableMapOf<Int, Int>()
            for (num in nums) {
                countMap[num] = countMap.getOrDefault(num, 0) + 1
            }
            var half = nums.size / 2
            for ((key, value) in countMap) {
                if (value > half) {
                    return key
                }
            }
            return -1
        }


    }
}