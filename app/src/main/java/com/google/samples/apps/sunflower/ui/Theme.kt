/*
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.ui

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import com.google.samples.apps.sunflower.ui.AppColor.themeAccent

private val LightColors = lightColorScheme(
    primary = md_theme_light_primary,
    onPrimary = md_theme_light_onPrimary,
    primaryContainer = md_theme_light_primaryContainer,
    onPrimaryContainer = md_theme_light_onPrimaryContainer,
    secondary = md_theme_light_secondary,
    onSecondary = md_theme_light_onSecondary,
    secondaryContainer = md_theme_light_secondaryContainer,
    onSecondaryContainer = md_theme_light_onSecondaryContainer,
    tertiary = md_theme_light_tertiary,
    onTertiary = md_theme_light_onTertiary,
    tertiaryContainer = md_theme_light_tertiaryContainer,
    onTertiaryContainer = md_theme_light_onTertiaryContainer,
    error = md_theme_light_error,
    errorContainer = md_theme_light_errorContainer,
    onError = md_theme_light_onError,
    onErrorContainer = md_theme_light_onErrorContainer,
    background = md_theme_light_background,
    onBackground = md_theme_light_onBackground,
    surface = md_theme_light_surface,
    onSurface = md_theme_light_onSurface,
    surfaceVariant = md_theme_light_surfaceVariant,
    onSurfaceVariant = md_theme_light_onSurfaceVariant,
    outline = md_theme_light_outline,
    inverseOnSurface = md_theme_light_inverseOnSurface,
    inverseSurface = md_theme_light_inverseSurface,
    inversePrimary = md_theme_light_inversePrimary,
    surfaceTint = md_theme_light_surfaceTint,
    outlineVariant = md_theme_light_outlineVariant,
    scrim = md_theme_light_scrim,
)


private val DarkColors = darkColorScheme(
    primary = md_theme_dark_primary,
    onPrimary = md_theme_dark_onPrimary,
    primaryContainer = md_theme_dark_primaryContainer,
    onPrimaryContainer = md_theme_dark_onPrimaryContainer,
    secondary = md_theme_dark_secondary,
    onSecondary = md_theme_dark_onSecondary,
    secondaryContainer = md_theme_dark_secondaryContainer,
    onSecondaryContainer = md_theme_dark_onSecondaryContainer,
    tertiary = md_theme_dark_tertiary,
    onTertiary = md_theme_dark_onTertiary,
    tertiaryContainer = md_theme_dark_tertiaryContainer,
    onTertiaryContainer = md_theme_dark_onTertiaryContainer,
    error = md_theme_dark_error,
    errorContainer = md_theme_dark_errorContainer,
    onError = md_theme_dark_onError,
    onErrorContainer = md_theme_dark_onErrorContainer,
    background = md_theme_dark_background,
    onBackground = md_theme_dark_onBackground,
    surface = md_theme_dark_surface,
    onSurface = md_theme_dark_onSurface,
    surfaceVariant = md_theme_dark_surfaceVariant,
    onSurfaceVariant = md_theme_dark_onSurfaceVariant,
    outline = md_theme_dark_outline,
    inverseOnSurface = md_theme_dark_inverseOnSurface,
    inverseSurface = md_theme_dark_inverseSurface,
    inversePrimary = md_theme_dark_inversePrimary,
    surfaceTint = md_theme_dark_surfaceTint,
    outlineVariant = md_theme_dark_outlineVariant,
    scrim = md_theme_dark_scrim,
)

@Composable
fun SunflowerTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = false,
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }

        darkTheme -> DarkColors
        else -> LightColors
    }
    val view = LocalView.current
    if (!view.isInEditMode) {
        val systemUiController = rememberSystemUiController()
        val useDarkIcons = !isSystemInDarkTheme()
        val window = (view.context as Activity).window
        WindowCompat.setDecorFitsSystemWindows(window, false)
        DisposableEffect(systemUiController, useDarkIcons) {
            // Update all of the system bar colors to be transparent, and use
            // dark icons if we're in light theme
            systemUiController.setSystemBarsColor(
                color = Color.Transparent,
                darkIcons = useDarkIcons
            )
            onDispose {}
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        shapes = Shapes,
        typography = Typography,
        content = content
    )
}


@Stable
object WordsFairyTheme {
    val colors: SunflowerColors
        @Composable
        get() = LocalAppColors.current

    enum class Theme {
        Light, Dark
    }
}

@Stable
class SunflowerColors(
    statusBarColor: Color,
    navigationBarColor: Color,
    themeUi: Color,
    themeAccent: Color,
    background: Color,
    backgroundSecondary: Color,
    whiteBackground: Color,
    immerseBackground: Color,
    dialogBackground: Color,
    itemBackground: Color,
    itemImmerse: Color,
    editBackground: Color,
    textPrimary: Color,
    textSecondary: Color,
    textWhite: Color,
    textBlack: Color,
    selectColor: Color,
    icon: Color,
    iconBlack: Color,
    success: Color,
    info: Color,
    error: Color,
    btnBgPrimary: Color,
    btnBgSecond: Color,
    placeholder: Color,
) {
    var statusBarColor: Color by mutableStateOf(statusBarColor)
        internal set
    var navigationBarColor: Color by mutableStateOf(navigationBarColor)
        internal set

    var themeUi: Color by mutableStateOf(themeUi)
        internal set
    var themeAccent: Color by mutableStateOf(themeAccent)
        internal set
    var background: Color by mutableStateOf(background)
        internal set
    var backgroundSecondary: Color by mutableStateOf(backgroundSecondary)
        private set
    var whiteBackground: Color by mutableStateOf(whiteBackground)
        private set
    var immerseBackground: Color by mutableStateOf(immerseBackground)
        private set
    var dialogBackground: Color by mutableStateOf(dialogBackground)
        private set
    var itemBackground: Color by mutableStateOf(itemBackground)
        private set
    var itemImmerse: Color by mutableStateOf(itemImmerse)
        private set
    var editBackground: Color by mutableStateOf(editBackground)
        private set
    var textPrimary: Color by mutableStateOf(textPrimary)
        internal set
    var textSecondary: Color by mutableStateOf(textSecondary)
        private set
    var textWhite: Color by mutableStateOf(textWhite)
        private set
    var textBlack: Color by mutableStateOf(textBlack)
        private set
    var selectColor: Color by mutableStateOf(selectColor)
        private set
    var icon: Color by mutableStateOf(icon)
        private set
    var iconBlack: Color by mutableStateOf(iconBlack)
        private set
    var success: Color by mutableStateOf(success)
        private set
    var info: Color by mutableStateOf(info)
        private set
    var error: Color by mutableStateOf(error)
        private set
    var primaryBtnBg: Color by mutableStateOf(btnBgPrimary)
        internal set
    var secondBtnBg: Color by mutableStateOf(btnBgSecond)
        private set
    var placeholder: Color by mutableStateOf(placeholder)
        private set
}

var LocalAppColors = compositionLocalOf {
    LightColorScheme
}

private val LightColorScheme = SunflowerColors(
    statusBarColor = statusBarColorLight,
    navigationBarColor = white,
    themeUi = themeColor,
    themeAccent = themeAccent,
    background = backgroundColorLight,
    backgroundSecondary = backgroundSecondaryColorLight,
    whiteBackground = whiteBackgroundColorLight,
    immerseBackground = immerseBackgroundColorLight,
    dialogBackground = dialogBackgroundLight,
    itemBackground = itemBackgroundLight,
    itemImmerse = immerseBackgroundColorLight,
    editBackground = editBackgroundLight,
    textPrimary = textPrimaryLight,
    textSecondary = textSecondaryLight,
    textWhite = textWhite,
    textBlack = textBlack,
    selectColor = themeAccentColor,
    icon = grey,
    iconBlack = black,
    success = green,
    info = blue,
    error = red2,
    btnBgPrimary = themeColor,
    btnBgSecond = themeColor,
    placeholder = white3,

    )