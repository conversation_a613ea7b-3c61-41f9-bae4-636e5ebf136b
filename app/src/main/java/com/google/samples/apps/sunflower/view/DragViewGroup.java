/*
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.customview.widget.ViewDragHelper;

public class DragViewGroup extends RelativeLayout {

    private final ViewDragHelper mDragHelper;
    private float startX, startY;
    private boolean isDragging = false;

    public DragViewGroup(Context context) {
        this(context, null);
    }

    public DragViewGroup(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public DragViewGroup(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mDragHelper = ViewDragHelper.create(this, 1.0f, new ViewDragCallback());
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                // 记录初始触摸位置
                startX = ev.getX();
                startY = ev.getY();
                isDragging = false;
                // 将事件传递给 ViewDragHelper
                mDragHelper.shouldInterceptTouchEvent(ev);
                break;
            case MotionEvent.ACTION_MOVE:
                // 计算滑动距离
                float dx = Math.abs(ev.getX() - startX);
                float dy = Math.abs(ev.getY() - startY);
                // 如果滑动距离超过 5px，拦截事件
                if (dx > 5 || dy > 5) {
                    isDragging = true;
//                    mDragHelper.processTouchEvent(ev);
                    return true;
                }
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                // 重置状态
                isDragging = false;
                break;
        }
        return super.onInterceptTouchEvent(ev);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (isDragging) {
            // 将事件传递给 ViewDragHelper
            mDragHelper.processTouchEvent(event);
            return true;
        }
        return super.onTouchEvent(event);
    }

    private class ViewDragCallback extends ViewDragHelper.Callback {
        @Override
        public boolean tryCaptureView(@NonNull View child, int pointerId) {
            // 仅捕获子 View
            return true;
        }

        @Override
        public int clampViewPositionHorizontal(@NonNull View child, int left, int dx) {
            // 限制子 View 的水平移动范围
            int paddingLeft = getPaddingLeft();
            int paddingRight = getPaddingRight();
            int parentWidth = getWidth();
            int childWidth = child.getWidth();
            int minLeft = paddingLeft;
            int maxLeft = parentWidth - childWidth - paddingRight;
            return Math.min(Math.max(left, minLeft), maxLeft);
        }

        @Override
        public int clampViewPositionVertical(@NonNull View child, int top, int dy) {
            // 限制子 View 的垂直移动范围
            int paddingTop = getPaddingTop();
            int paddingBottom = getPaddingBottom();
            int parentHeight = getHeight();
            int childHeight = child.getHeight();
            int minTop = paddingTop;
            int maxTop = parentHeight - childHeight - paddingBottom;
            return Math.min(Math.max(top, minTop), maxTop);
        }

        @Override
        public void onViewReleased(@NonNull View releasedChild, float xvel, float yvel) {
            // 释放子 View 时，重置状态
            isDragging = false;
        }
    }
}

//public class DragViewGroup extends RelativeLayout {
//
//    ViewDragHelper mDragHelper;
//
//    public DragViewGroup(Context context) {
//        this(context, null);
//    }
//
//    public DragViewGroup(Context context, AttributeSet attrs) {
//        this(context, attrs, 0);
//    }
//
//    public DragViewGroup(Context context, AttributeSet attrs, int defStyleAttr) {
//        super(context, attrs, defStyleAttr);
//        mDragHelper = ViewDragHelper.create(this, 1.0f, new ViewDragCallback());
//    }
//
//    @Override
//    public boolean onInterceptHoverEvent(MotionEvent event) {
//        return mDragHelper.shouldInterceptTouchEvent(event);
//    }
//
//    @SuppressLint("ClickableViewAccessibility")
//    @Override
//    public boolean onTouchEvent(MotionEvent event) {
//        mDragHelper.processTouchEvent(event);
//        return true;
//    }
//
//    private static class ViewDragCallback extends ViewDragHelper.Callback {
//        @Override
//        public boolean tryCaptureView(@NonNull View child, int pointerId) {
//            return true;
//        }
//
//        @Override
//        public int clampViewPositionHorizontal(@NonNull View child, int left, int dx) {
//            return left;
//        }
//
//        @Override
//        public int clampViewPositionVertical(@NonNull View child, int top, int dy) {
//            return top;
//        }
//    }
//}
