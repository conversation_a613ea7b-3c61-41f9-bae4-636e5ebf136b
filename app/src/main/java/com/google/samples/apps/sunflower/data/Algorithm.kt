/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.data

class Algorithm {
    companion object {
        const val ID_10001 = "10001"
        const val ID_10002 = "10002"
        const val ID_10003 = "10003"
        const val ID_10004 = "10004"
        const val ID_10005 = "10005"
        const val ID_10006 = "10006"
        const val ID_10007 = "10007"
        const val ID_10008 = "10008"
        const val ID_10009 = "10009"
    }
}