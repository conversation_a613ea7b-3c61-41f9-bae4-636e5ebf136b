/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.leetcode.algorithm

class BinarySearch2 {
    companion object {
        @JvmStatic
        fun main(args: Array<String>) {

            val nums = intArrayOf(1, 3, 5, 7, 9)
            var list = listOf(2,2,3)
            val target = 9
            val result = search(nums, target)
            if (result != -1) {
                println("目标值 $target 在数组中的索引为 $result")
            } else {
                println("未找到目标值 $target")
            }
        }


        fun search(nums: IntArray, target: Int): Int {
            var left = 0
            var right = nums.size - 1
            while (left <= right) {
                var mid = left + (right - left) / 2
                when {
                    nums[mid] == target -> return mid
                    target > nums[mid] -> left = mid + 1
                    else -> right = mid - 1
                }
            }
            return -1
        }


    }
}