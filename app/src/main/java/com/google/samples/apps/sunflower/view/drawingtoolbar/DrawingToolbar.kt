package com.google.samples.apps.sunflower.view.drawingtoolbar

import android.content.Context
import android.content.res.ColorStateList
import android.content.res.Resources
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.samples.apps.sunflower.R
import com.google.samples.apps.sunflower.databinding.KlineViewDrawingToolbarBinding

class KLineDrawingToolbar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val binding by lazy { KlineViewDrawingToolbarBinding.inflate(LayoutInflater.from(context), this, true) }

    private var mAdapter = ToolAdapter()

    init {
        setupRecyclerView()
    }

    private fun setupRecyclerView() {

        val items = listOf(
            ToolItem.Handle(iconResId = R.mipmap.ic_handle),
            ToolItem.PaintColor(iconResId = R.mipmap.icon_favorite_edit, false, ContextCompat.getColor(context, android.R.color.holo_blue_dark)),
            ToolItem.LineThickness(iconResId = R.mipmap.ic_line_solid_01, false),
            ToolItem.LineDashed(iconResId = R.mipmap.ic_line_deshed_01, false),
            ToolItem.Lock(iconResId = R.mipmap.ic_lock_open, true),
            ToolItem.Delete(iconResId = R.mipmap.ic_drawing_delete),
        )

        binding.rvList.layoutManager = object : LinearLayoutManager(context, HORIZONTAL, false) {
            override fun canScrollVertically(): Boolean = false // 禁用垂直滚动
            override fun canScrollHorizontally(): Boolean = false // 禁用水平滚动
        }
        binding.rvList.adapter = mAdapter
        binding.rvList.itemAnimator = null
        binding.rvList.layoutAnimation = null
        binding.rvList.isNestedScrollingEnabled = false
        binding.rvList.setHasFixedSize(true)

        binding.rvList.addOnItemTouchListener(object : RecyclerView.OnItemTouchListener {
            override fun onInterceptTouchEvent(rv: RecyclerView, e: MotionEvent): Boolean {
                return false
            }

            override fun onTouchEvent(rv: RecyclerView, e: MotionEvent) {}
            override fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean) {}
        })

        setToolItems(items)

    }

    fun setOnToolClickListener(listener: (adapter: ToolAdapter, view: View, position: Int, item: ToolItem) -> Unit) {
        mAdapter.onItemClickListener = listener
    }

    // 批量添加工具项
    private fun setToolItems(items: List<ToolItem>) {
        mAdapter.setNewInstance(items.toMutableList())
    }

    // 获取所有工具项
    fun getToolItems(): List<ToolItem> = mAdapter.data
}

class ToolAdapter(var onItemClickListener: ((adapter: ToolAdapter, view: View, position: Int, item: ToolItem) -> Unit)? = null) : BaseQuickAdapter<ToolItem, BaseViewHolder>(R.layout.kline_item_drawing_toolbar) {

    fun constraintWH(root: ConstraintLayout, id: Int, width: Int, height: Int) {
        val set = ConstraintSet()
        set.clone(root)
        set.constrainWidth(id, width)
        set.constrainHeight(id, height)
        set.applyTo(root)
    }

    override fun convert(holder: BaseViewHolder, item: ToolItem) {

        // 设置item数据
        holder.setImageResource(R.id.ivFunction, item.iconResId)
        holder.setVisible(R.id.ivState, item.isExpandable())
        holder.setImageResource(R.id.ivState, if (item.isExpanded()) R.mipmap.icon_strategy_arrow_triangle_down else R.mipmap.icon_strategy_arrow_triangle_up)
        when (item) {
            is ToolItem.Handle -> {
                constraintWH(holder.getView(R.id.clItemRoot), R.id.ivFunction, 20.dp2px(), 20.dp2px())
            }

            is ToolItem.PaintColor -> {
                constraintWH(holder.getView(R.id.clItemRoot), R.id.ivFunction, 20.dp2px(), 20.dp2px())
                holder.getView<ImageView>(R.id.ivFunction).setColorFilter(item.color)
                holder.getView<ImageView>(R.id.ivFunction).imageTintList = ColorStateList.valueOf(item.color)
            }

            is ToolItem.LineThickness -> {
                constraintWH(holder.getView(R.id.clItemRoot), R.id.ivFunction, 20.dp2px(), 2.dp2px())
            }

            is ToolItem.LineDashed -> {
                constraintWH(holder.getView(R.id.clItemRoot), R.id.ivFunction, 20.dp2px(), 2.dp2px())
            }

            is ToolItem.Lock -> {
                constraintWH(holder.getView(R.id.clItemRoot), R.id.ivFunction, 20.dp2px(), 20.dp2px())
            }

            is ToolItem.Delete -> {
                constraintWH(holder.getView(R.id.clItemRoot), R.id.ivFunction, 20.dp2px(), 20.dp2px())
            }

        }

        holder.getView<ConstraintLayout>(R.id.clItemRoot).setOnClickListener {
            onItemClickListener?.invoke(this, holder.itemView, holder.getBindingAdapterPosition(), item)
            if (item is Expandable && !item.expand) {
                item.expand = true
                holder.getView<ImageView>(R.id.ivState).setImageResource(R.mipmap.icon_strategy_arrow_triangle_down)
            }
        }
    }

}

/**
 * 可展开子菜单的工具项
 */
interface Expandable {
    var expand: Boolean
}

/**
 * 工具栏工具项
 */
sealed class ToolItem(
    open var iconResId: Int
) {

    /**
     * 手柄
     */
    data class Handle(override var iconResId: Int) : ToolItem(iconResId)

    /**
     * 画笔颜色
     */
    data class PaintColor(
        override var iconResId: Int,
        override var expand: Boolean = false,
        var color: Int
    ) : ToolItem(iconResId), Expandable

    /**
     * 线的粗细
     */
    data class LineThickness(
        override var iconResId: Int,
        override var expand: Boolean = false
    ) : ToolItem(iconResId), Expandable

    /**
     * 线的样式（虚线、实线）
     */
    data class LineDashed(
        override var iconResId: Int,
        override var expand: Boolean = false
    ) : ToolItem(iconResId), Expandable

    /**
     * 锁定
     */
    data class Lock(
        override var iconResId: Int,
        var open: Boolean = true
    ) : ToolItem(iconResId)

    /**
     * 删除
     */
    data class Delete(override var iconResId: Int) : ToolItem(iconResId)

    /**
     * 是否可展开
     */
    fun isExpandable(): Boolean = this is Expandable

    /**
     * 是否展开
     */
    fun isExpanded(): Boolean = (this as? Expandable)?.expand ?: false
}

fun <T : Number> T.dp2px(): T {
    val result = TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_DIP,
        this.toFloat(),
        Resources.getSystem().displayMetrics
    )

    return when (this) {
        is Int -> result.toInt() as T
        is Long -> result.toLong() as T
        is Float -> result as T
        is Double -> result.toDouble() as T
        else -> result as T
    }
}