/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.leetcode.algorithm

/**
 * 判断是否是回文链表
 */
class PalindromeList {
    companion object {
        @JvmStatic
        fun main(args: Array<String>) {

            //创建链表 A：1->2->3->4->5
            val nodeA1 = ListNode5(1)
            val nodeA2 = ListNode5(2)
//            val nodeA3 = ListNode5(3)
            val nodeA4 = ListNode5(2)
            val nodeA5 = ListNode5(1)
            nodeA1.next = nodeA2
            nodeA2.next = nodeA4
//            nodeA2.next = nodeA3
//            nodeA3.next = nodeA4
            nodeA4.next = nodeA5

            println("是否是回文链表：${isPalindrome(nodeA1)}")
        }

        private fun isPalindrome(head: ListNode5?): Boolean {
            if (head == null) {
                return true
            }

            val list: MutableList<Int> = mutableListOf()
            var current = head
            while (current != null) {
                list.add(current.value)
                current = current.next
            }

            var left = 0
            var right = list.size - 1
            while (left < right) {
                if (list[left] != list[right]) {
                    return false
                }
                left++
                right--
            }
            return true
        }


    }
}

class ListNode5(var value: Int) {
    var next: ListNode5? = null
}














