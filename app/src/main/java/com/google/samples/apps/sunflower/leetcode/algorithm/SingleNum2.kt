/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.leetcode.algorithm

import com.google.samples.apps.sunflower.leetcode.algorithm.TwoSum.Companion.towSum

class SingleNum2 {
    companion object {
        @JvmStatic
        fun main(args: Array<String>) {
            val nums = intArrayOf(1,1,2,2,55,55,6)
            println("只出现一次的数字是${singleNum(nums)}")
        }


        fun singleNum(nums:IntArray):Int{
            var result = 0
            for(num in nums){
                result = num xor result
            }
            return result
        }

    }
}