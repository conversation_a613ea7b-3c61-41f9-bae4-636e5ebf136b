/*
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.view.drawingtoolbar

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.ViewTreeObserver
import android.widget.FrameLayout
import androidx.customview.widget.ViewDragHelper

class DragLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : FrameLayout(context, attrs, defStyle) {

    private var lastX = 0
    private var lastY = 0

    private val dragHelper = ViewDragHelper.create(this, 1f, DragCallback())

    init {
        viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                viewTreeObserver.removeOnGlobalLayoutListener(this)
                if (childCount > 0) {
                    centerChild()
                }
            }
        })
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        if (childCount > 0 && w > 0 && h > 0) {
            centerChild()
        }
    }

    /**
     * 设置子View的位置
     * @param x 水平位置（像素）
     * @param y 垂直位置（像素）
     */
    private fun setChildPosition(x: Int, y: Int) {
        if (childCount == 0) return

        val child = getChildAt(0)
        val clampedX = x.coerceIn(0, measuredWidth - child.measuredWidth)
        val clampedY = y.coerceIn(0, measuredHeight - child.measuredHeight)

        lastX = clampedX
        lastY = clampedY
        requestLayout()
    }

    /**
     * 将子View居中显示
     */
    fun centerChild() {
        if (childCount == 0) return

        val child = getChildAt(0)
        val centerX = (measuredWidth - child.measuredWidth) / 2
        val centerY = (measuredHeight - child.measuredHeight) / 2

        setChildPosition(centerX, centerY)
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        // 保持子View的拖动位置
        for (i in 0 until childCount) {
            val child = getChildAt(i)
            child.layout(
                lastX,
                lastY,
                lastX + child.measuredWidth,
                lastY + child.measuredHeight
            )
        }
    }

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        // 只在触摸点在子View（RecyclerView）范围内时拦截事件
        if (childCount > 0) {
            val child = getChildAt(0)
            val touchX = ev.x
            val touchY = ev.y
            val inChildBounds = touchX >= child.left && touchX <= child.right &&
                    touchY >= child.top && touchY <= child.bottom

            // 仅拦截子View范围内的触摸事件（避免影响下层视图）
            return inChildBounds && dragHelper.shouldInterceptTouchEvent(ev)
        }
        return false
    }

//    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
//        return dragHelper.shouldInterceptTouchEvent(ev)
//    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        dragHelper.processTouchEvent(event)
//        return true
        return dragHelper.viewDragState != ViewDragHelper.STATE_IDLE
    }

    private inner class DragCallback : ViewDragHelper.Callback() {
        override fun tryCaptureView(child: View, pointerId: Int): Boolean {
            return true
        }

        override fun clampViewPositionHorizontal(child: View, left: Int, dx: Int): Int {
            lastX = left.coerceIn(0, measuredWidth - child.measuredWidth)
            return lastX
        }

        override fun clampViewPositionVertical(child: View, top: Int, dy: Int): Int {
            lastY = top.coerceIn(0, measuredHeight - child.measuredHeight)
            return lastY
        }

        override fun onViewReleased(releasedChild: View, xvel: Float, yvel: Float) {

        }

        override fun getViewHorizontalDragRange(child: View) = measuredWidth - child.measuredWidth
        override fun getViewVerticalDragRange(child: View) = measuredHeight - child.measuredHeight
    }

}

//class DragLayout @JvmOverloads constructor(
//    context: Context,
//    attrs: AttributeSet? = null,
//    defStyle: Int = 0
//) : FrameLayout(context, attrs, defStyle) {
//
//    private var lastX = 0
//    private var lastY = 0
//
//    private val dragHelper = ViewDragHelper.create(this, 1f, DragCallback())
//
//    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
//        // 保持子View的拖动位置
//        for (i in 0 until childCount) {
//            val child = getChildAt(i)
//            child.layout(
//                lastX,
//                lastY,
//                lastX + child.measuredWidth,
//                lastY + child.measuredHeight
//            )
//        }
//    }
//
//    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
//        return dragHelper.shouldInterceptTouchEvent(ev)
//    }
//
//    @SuppressLint("ClickableViewAccessibility")
//    override fun onTouchEvent(event: MotionEvent): Boolean {
//        dragHelper.processTouchEvent(event)
//        return true
//    }
//
//    private inner class DragCallback : ViewDragHelper.Callback() {
//        override fun tryCaptureView(child: View, pointerId: Int): Boolean {
//            return true
//        }
//
//        override fun clampViewPositionHorizontal(child: View, left: Int, dx: Int): Int {
//            lastX = left.coerceIn(0, measuredWidth - child.measuredWidth)
//            return lastX
//        }
//
//        override fun clampViewPositionVertical(child: View, top: Int, dy: Int): Int {
//            lastY = top.coerceIn(0, measuredHeight - child.measuredHeight)
//            return lastY
//        }
//
//        override fun onViewReleased(releasedChild: View, xvel: Float, yvel: Float) {
//
//        }
//
//        override fun getViewHorizontalDragRange(child: View) = measuredWidth - child.measuredWidth
//        override fun getViewVerticalDragRange(child: View) = measuredHeight - child.measuredHeight
//    }
//
//}