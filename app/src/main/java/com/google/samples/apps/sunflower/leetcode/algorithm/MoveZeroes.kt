/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.leetcode.algorithm

/**
 * 移动0
 * 给定一个数组 nums，编写一个函数将所有 0 移动到数组的末尾，同时保持非零元素的相对顺序。
 * 请注意 ，必须在不复制数组的情况下原地对数组进行操作。
 */
class MoveZeroes {
    companion object {
        @JvmStatic
        fun main(args: Array<String>) {

            val nums = intArrayOf(0, 1, 0, 3, 1, 2)
            moveZeroes3(nums)
            println("After moving zeros: ${nums.contentToString()}")


        }

        /**
         * 移动0
         */
        fun moveZeroes(nums: IntArray) {
            var lastNonZeroFountAt = 0
            for (i in nums.indices) {
                if (nums[i] != 0) {
                    nums[lastNonZeroFountAt] = nums[i]
                    lastNonZeroFountAt++
                }
            }
            for (i in lastNonZeroFountAt until nums.size) {
                nums[i] = 0
            }
        }


        fun moveZeroes2(nums: IntArray) {
            var nonZeroIndex = 0
            for (num in nums) {
                if (num != 0) {
                    nums[nonZeroIndex] = num
                    nonZeroIndex++
                }
            }

            while (nonZeroIndex < nums.size) {
                nums[nonZeroIndex] = 0
                nonZeroIndex++
            }
        }


        fun moveZeroes3(nums: IntArray) {
            var nonZeroIndex = 0
            for (num in nums) {
                if (num != 0) {
                    nums[nonZeroIndex] = num
                    nonZeroIndex++
                }
            }

            while (nonZeroIndex < nums.size) {
                nums[nonZeroIndex] = 0
                nonZeroIndex++
            }
        }
    }


}


















