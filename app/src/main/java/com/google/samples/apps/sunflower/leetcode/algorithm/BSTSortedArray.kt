/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.leetcode.algorithm

/**
 * 将有序数组转换为二叉搜索树
 */
class BSTSortedArray {
    companion object {
        @JvmStatic
        fun main(args: Array<String>) {

            val nums = intArrayOf(-10, -3, 0, 5, 9)
            val root = sortedArrayToBST(nums)
            println("前序遍历")
            preOrder(root)
            println("中序遍历")
            inOrder(root)
            println("后序遍历")
            postOrder(root)

        }

        private fun sortedArrayToBST(nums: IntArray): TreeNode3? {
            return buildTree(0, nums.size - 1, nums)
        }

        private fun buildTree(start: Int, end: Int, nums: IntArray): TreeNode3? {
            if (start > end) {
                return null
            }

            val mid = start + (end - start) / 2
            val root = TreeNode3(nums[mid])
            root.left = buildTree(start, mid - 1, nums)
            root.right = buildTree(mid + 1, end, nums)
            return root
        }


        /**
         * 前序遍历
         */
        private fun preOrder(root: TreeNode3?) {
            if (root == null) {
                return
            }
            println(root.value)
            preOrder(root.left)
            preOrder(root.right)
        }

        private fun inOrder(root: TreeNode3?) {
            if (root == null) {
                return
            }
            inOrder(root.left)
            println(root.value)
            inOrder(root.right)
        }

        private fun postOrder(root: TreeNode3?) {
            if (root == null) {
                return
            }
            inOrder(root.left)
            inOrder(root.right)
            println(root.value)

        }
    }
}

class TreeNode3(var value: Int) {
    var left: TreeNode3? = null
    var right: TreeNode3? = null
}















