package com.google.samples.apps.sunflower.utils

import android.content.Context
import android.util.TypedValue

/**
 * 属性资源
 */
object AttrResourceUtil {

    @JvmStatic
    fun getColor(context: Context, resId: Int): Int {
        val typedValue = TypedValue()
        context.theme.resolveAttribute(resId, typedValue, true)
        return typedValue.data
    }

    @JvmStatic
    fun getDrawable(context: Context, resId: Int): Int {
        val typedValue = TypedValue()
        context.theme.resolveAttribute(resId, typedValue, true)
        return typedValue.resourceId
    }

}