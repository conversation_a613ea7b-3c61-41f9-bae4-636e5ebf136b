/*
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower

import android.annotation.SuppressLint
import android.app.Application
import android.content.Context
import androidx.work.Configuration
import com.google.samples.apps.sunflower.ext.flowbus.FlowBusInitializer
import com.kit.AppKitManager
import dagger.hilt.android.HiltAndroidApp

@HiltAndroidApp
class MainApplication : Application(), Configuration.Provider {

    companion object {
        @JvmStatic
        @SuppressLint("StaticFieldLeak")
        lateinit var CONTEXT: Context
    }

    override val workManagerConfiguration: Configuration
        get() = Configuration.Builder()
            .setMinimumLoggingLevel(if (BuildConfig.DEBUG) android.util.Log.DEBUG else android.util.Log.ERROR)
            .build()

    override fun onCreate() {
        super.onCreate()
        CONTEXT = applicationContext
        FlowBusInitializer.init(this)
        if (BuildConfig.DEBUG) {
            AppKitManager.getInstance().installKit(this)
        }

    }

    override fun attachBaseContext(base: Context?) {
        super.attachBaseContext(base)
        // Dex 分包
//    MultiDex.install(this)
    }

}
