/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.leetcode.algorithm

/**
 * 合并两个有序链表
 */
class MergeList {
    companion object {
        @JvmStatic
        fun main(args: Array<String>) {

            //创建链表 A：1->2->3->4->5
            val nodeA1 = ListNode7(1)
            val nodeA2 = ListNode7(2)
            val nodeA3 = ListNode7(3)
            val nodeA4 = ListNode7(4)
            val nodeA5 = ListNode7(5)
            nodeA1.next = nodeA2
            nodeA2.next = nodeA3
            nodeA3.next = nodeA4
            nodeA4.next = nodeA5

            //创建链表 B：1->2->3->4->5
            val nodeB1 = ListNode7(1)
            val nodeB2 = ListNode7(3)
            val nodeB3 = ListNode7(5)
            val nodeB4 = ListNode7(6)
            val nodeB5 = ListNode7(7)
            nodeB1.next = nodeB2
            nodeB2.next = nodeB3
            nodeB3.next = nodeB4
            nodeB4.next = nodeB5

            val mergeList = merge(nodeA1, nodeB1)
            var current = mergeList
            while (current != null) {
                print("${current.value}->")
                current = current.next
            }
            println("null")
        }

        private fun merge(l1: ListNode7?, l2: ListNode7?): ListNode7? {

            var dummyHead = ListNode7(0)
            var current = dummyHead
            var p1 = l1
            var p2 = l2

            while (p1 != null && p2 != null) {
                if (p1.value < p2.value) {
                    current.next = p1
                    p1 = p1.next
                } else {
                    current.next = p2
                    p2 = p2.next
                }
                current = current.next!!
            }

            if (p1 != null) {
                current.next = p1
            }
            if (p2 != null) {
                current.next = p2
            }

            return dummyHead.next

        }


    }
}

class ListNode7(var value: Int) {
    var next: ListNode7? = null
}














