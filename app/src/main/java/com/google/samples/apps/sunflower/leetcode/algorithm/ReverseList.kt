/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.leetcode.algorithm

import com.google.samples.apps.sunflower.leetcode.Demo.Companion.reverseList2
import com.google.samples.apps.sunflower.leetcode.bean.ListNode
import okhttp3.internal.http2.Header

/**
 * 反转链表
 * 1->2->3->4->5->null
 * 5->4->3->2->1->null
 */
class ReverseList {
    companion object {
        @JvmStatic
        fun main(args: Array<String>) {

            //创建链表 A：1->2->3->4->5
            val nodeA1 = ListNode3(1)
            val nodeA2 = ListNode3(2)
            val nodeA3 = ListNode3(3)
            val nodeA4 = ListNode3(4)
            val nodeA5 = ListNode3(5)
            nodeA1.next = nodeA2
            nodeA2.next = nodeA3
            nodeA3.next = nodeA4
            nodeA4.next = nodeA5

            var reverseList = reverseList4(nodeA1)
            while (reverseList != null) {
                println("reverseList.value=${reverseList.value}")
                reverseList = reverseList.next
            }

        }


        /**
         * 反转链表
         * 1->2->3->4->5->null
         * 5->4->3->2->1->null
         */
        fun reverseList(head: ListNode?): ListNode? {
            var prev: ListNode? = null
            var curr: ListNode? = head
            while (curr != null) {
                println("curr.value= ${curr.value} ")
                val next: ListNode? = curr.next
                curr.next = prev
                prev = curr
                curr = next
            }
            return prev

        }

        /**
         * 这段代码实现了一个链表反转的功能。给定一个单链表的头节点 `head`，函数 `reverseList` 会返回一个新的链表，这个新链表是原链表元素顺序的反转。下面是这个算法的工作原理和逐步分析：
         *
         * 1. **初始化**:
         *    - `prev` 被初始化为 `null`。在反转过程中，它将指向当前节点的前一个节点。
         *    - `curr` 初始化为 `head`，即链表的第一个节点。随着循环的进行，`curr` 将遍历整个链表。
         *
         * 2. **循环**:
         *    - 循环条件是 `curr != null`，意味着只要当前节点不是 `null`（即还未到达链表末尾），循环就继续执行。
         *    - 在每次循环中：
         *      - `next` 临时保存 `curr.next` 的值，也就是当前节点指向的下一个节点。这样做是为了不丢失对剩余链表部分的引用。
         *      - `curr.next = prev;` 这一步是关键步骤，它改变了当前节点 `curr` 的 `next` 指针的方向，使其指向前一个节点 `prev`，而不是原本的下一个节点。这实现了链表局部的反转。
         *      - 更新 `prev` 为当前的 `curr`，因为下一次迭代时，当前节点 `curr` 将成为新的“前一个”节点。
         *      - 更新 `curr` 为之前保存的 `next`，这样 `curr` 就移动到了下一个待处理的节点上。
         *
         * 3. **结束条件**:
         *    - 当 `curr` 变成 `null` 时，意味着已经遍历完整个链表，并且所有的节点都已经被正确地反转了它们的 `next` 指针方向。
         *    - 此时，`prev` 将指向原来链表的最后一个节点，而这个节点现在成为了反转后的新链表的头节点。
         *
         * 4. **返回结果**:
         *    - 函数最后返回 `prev`，它就是反转后的链表的新头节点。
         *
         * 该算法的时间复杂度是 O(n)，其中 n 是链表中的节点数，因为它需要遍历整个链表一次。空间复杂度是 O(1)，因为它只使用了几个额外的变量来完成任务，没有使用与输入规模相关的额外存储空间。这是一个非常高效的方法来反转单链表。
         */
        fun reverseList2(head: ListNode?): ListNode? {
            var prev: ListNode? = null
            var curr: ListNode? = head
            while (curr != null) {
                val next = curr.next
                curr.next = prev
                prev = curr
                curr = next
            }
            return prev

        }


        fun reverseList3(head: ListNode?): ListNode? {
            var previousNode: ListNode? = null
            var currentNode: ListNode? = head
            while (currentNode != null) {
                val nextNode = currentNode.next
                currentNode.next = previousNode
                previousNode = currentNode
                currentNode = nextNode
            }
            return previousNode

        }




        fun reverseList4(header:ListNode3?):ListNode3?{
            var preNode:ListNode3? = null
            var curNode:ListNode3? = header
            while (curNode !=null){
                var nextNode = curNode.next
                curNode.next = preNode
                preNode = curNode
                curNode = nextNode

            }
            return preNode
        }



















    }
}


class ListNode3(var value: Int){
    var next:ListNode3? = null
}


















