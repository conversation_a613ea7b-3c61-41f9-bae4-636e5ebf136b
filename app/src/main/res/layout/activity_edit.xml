<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <EditText
        android:id="@+id/etInput"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:autofillHints="请输入内容"
        android:background="#f5f5f5"
        android:gravity="top|start"
        android:hint="请输入内容~"
        android:inputType="number|numberDecimal"
        android:lines="5"
        android:padding="10dp"
        android:textAlignment="textStart"
        android:textSize="16sp" />

    <com.google.samples.apps.sunflower.view.VolSeekBar
        android:id="@+id/volSeekBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="12dp"
        android:layout_marginTop="-12dp"
        android:paddingBottom="9dp" />

    <com.google.samples.apps.sunflower.view.VolSeekBar2
        android:id="@+id/volSeekBar2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="12dp"
        android:layout_marginTop="-12dp"
        android:paddingBottom="9dp" />

    <com.google.samples.apps.sunflower.view.ThreeLayerCircleView
        android:layout_width="100dp"
        android:layout_height="100dp" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clAnimator"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="#55ff0000">

        <ProgressBar
            android:id="@+id/loadingView"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:progressDrawable="@drawable/ic_launcher_circle"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tvAnimator"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvAnimator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="设置"
            android:textColor="@color/cffffff"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/loadingView"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tvRun"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:ellipsize="marquee"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:marqueeRepeatLimit="marquee_forever"
        android:singleLine="true"
        android:text="这是一段很长很长的文本，用于展示跑马灯效果" />

    <Button
        android:id="@+id/btnInput"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="切换输入类型" />

    <Button
        android:id="@+id/btnInput2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="按钮2" />

    <TextView
        android:id="@+id/tvDemo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="这是一段很长很长的文本" />

</LinearLayout>
