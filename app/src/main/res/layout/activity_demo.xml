<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tvRun"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:ellipsize="marquee"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:marqueeRepeatLimit="marquee_forever"
        android:singleLine="true"
        android:text="这是一段很长很长的文本，用于展示跑马灯效果" />

    <com.google.samples.apps.sunflower.view.drawingtoolbar.DragLayout
        android:id="@+id/dlToolbar"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="#dedede"
        android:visibility="visible"
        tools:visibility="visible">

        <com.google.samples.apps.sunflower.view.drawingtoolbar.KLineDrawingToolbar
            android:id="@+id/viewToolbar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center" />

    </com.google.samples.apps.sunflower.view.drawingtoolbar.DragLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:divider="@drawable/divider_grid"
        android:gravity="center"
        android:orientation="horizontal"
        android:showDividers="middle">

        <Button
            android:id="@+id/btnLandscape"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="切横竖屏" />

        <Button
            android:id="@+id/btnToolbar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Toolbar"
            android:textAllCaps="false" />

        <Button
            android:id="@+id/btnInput"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="弹窗吧" />
    </LinearLayout>

</LinearLayout>
