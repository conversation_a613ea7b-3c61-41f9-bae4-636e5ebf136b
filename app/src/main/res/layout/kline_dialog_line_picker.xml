<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:paddingHorizontal="5dp"
    app:cardBackgroundColor="#ffffff"
    app:cardCornerRadius="2dp"
    app:cardElevation="0dp">

    <com.google.samples.apps.sunflower.view.drawingtoolbar.LinePickerView
        android:id="@+id/viewLinePicker"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.cardview.widget.CardView>