<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright 2018 Google LLC
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<resources>

    <!-- Per Material Design specs: -->
    <!-- https://material.io/design/components/buttons-floating-action-button.html#specs -->
    <dimen name="fab_margin">16dp</dimen>

    <!-- Padding on the bottom of a screen so that the FAB won't overlap scrolled text. -->
    <dimen name="fab_bottom_padding">72dp</dimen>

    <dimen name="plant_detail_app_bar_height">278dp</dimen>

    <dimen name="margin_normal">16dp</dimen>
    <dimen name="margin_small">8dp</dimen>
    <dimen name="margin_extra_small">4dp</dimen>

    <dimen name="padding_large">48dp</dimen>

    <dimen name="plant_item_image_height">95dp</dimen>

    <dimen name="card_side_margin">12dp</dimen>
    <dimen name="card_bottom_margin">26dp</dimen>
    <dimen name="card_elevation">2dp</dimen>
    <dimen name="card_corner_radius">12dp</dimen>

    <!-- elevation for Plant Detail toolbar - used to convert to pixels when hiding/showing -->
    <dimen name="toolbar_elevation">5dp</dimen>

    <!-- Plant List Header margin -->
    <dimen name="header_margin">24dp</dimen>

    <dimen name="gallery_header_margin">72dp</dimen>

    <!-- minimum height of plant detail page so that the collapsing toolbar can be shown on every page -->
    <dimen name="plant_description_min_height">555dp</dimen>

</resources>
