<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="VolSeekBar" >
        <!-- 进度 -->
        <!--最小值，当为0时，显示最小值-->
        <attr name="vsb_min" format="integer|reference"/>
        <!--最大值-->
        <attr name="vsb_max" format="integer|reference"/>
        <!--进度值-->
        <attr name="vsb_progress" format="integer|reference"/>
        <!-- 轨道 track -->
        <!--背景颜色-->
        <attr name="vsb_track_backgroundColor" format="color|reference"/>
        <!--前景颜色-->
        <attr name="vsb_track_progressColor" format="color|reference"/>
        <!--轨道宽度-->
        <attr name="vsb_track_width" format="dimension|reference"/>
        <!-- 平分进度的小圆点 -->
        <!--是否显示小圆点-->
        <attr name="vsb_dot_isShow" format="boolean|reference" />
        <!--小圆点数量-->
        <attr name="vsb_dot_count" format="integer|reference"/>
        <!--小圆点半径-->
        <attr name="vsb_dot_radius" format="dimension|reference"/>
        <!--小圆点边框宽度-->
        <attr name="vsb_dot_strokeWidth" format="dimension|reference"/>
        <!--小圆点边框颜色-->
        <attr name="vsb_dot_strokeColor" format="color|reference"/>
        <!--小圆点背景色-->
        <attr name="vsb_dot_solidBackColor" format="color|reference"/>
        <!--是否需要震动-->
        <attr name="vsb_isNeedVibrate" format="boolean|reference" />
        <!-- 滑块 -->
        <!--滑块半径-->
        <attr name="vsb_thumb_radius" format="dimension|reference"/>
        <!--滑块填充色-->
        <attr name="vsb_thumb_solidColor" format="color|reference"/>
        <!--滑块边大小-->
        <attr name="vsb_thumb_strokeWidth" format="dimension|reference"/>
        <!-- 底部百分比文字 -->
        <!--是否显示底部百分比数字-->
        <attr name="vsb_percent_isShow" format="boolean|reference"/>
        <!--底部百分比数字文字大小-->
        <attr name="vsb_percent_textSize" format="dimension|reference"/>
        <!--底部百分比数字文字颜色-->
        <attr name="vsb_percent_textColor" format="color|reference"/>
        <!--底部百分比单位-->
        <attr name="vsb_percent_unit" format="string|reference"/>
        <!--底部百分比数字距滑块的间距-->
        <attr name="vsb_percent_margin" format="dimension|reference"/>
        <!-- 气泡 -->
        <!--是否显示气泡-->
        <attr name="vsb_bubble_isShow" format="boolean|reference" />
        <!--气泡高度-->
        <attr name="vsb_bubble_height" format="dimension|reference" />
        <!--气泡圆角-->
        <attr name="vsb_bubble_radius" format="dimension|reference" />
        <!--气泡与滑块的间距-->
        <attr name="vsb_bubble_margin" format="dimension|reference" />
        <!--气泡横向内间距-->
        <attr name="vsb_bubble_horizontalPadding" format="dimension|reference" />
        <!--气泡文字大小-->
        <attr name="vsb_bubble_textSize" format="dimension|reference" />
        <!--气泡文字颜色-->
        <attr name="vsb_bubble_textColor" format="dimension|reference" />
        <!--气泡的背景色-->
        <attr name="vsb_bubble_color" format="color|reference" />
    </declare-styleable>

    <declare-styleable name="VolSeekBar2" >
        <!-- 进度 -->
        <!--最小值，当为0时，显示最小值-->
        <attr name="vsb2_min" format="integer|reference"/>
        <!--最大值-->
        <attr name="vsb2_max" format="integer|reference"/>
        <!--进度值-->
        <attr name="vsb2_progress" format="integer|reference"/>
        <!-- 轨道 track -->
        <!--背景颜色-->
        <attr name="vsb2_track_backgroundColor" format="color|reference"/>
        <!--前景颜色-->
        <attr name="vsb2_track_progressColor" format="color|reference"/>
        <!--轨道宽度-->
        <attr name="vsb2_track_width" format="dimension|reference"/>
        <!-- 平分进度的小圆点 -->
        <!--是否显示小圆点-->
        <attr name="vsb2_dot_isShow" format="boolean|reference" />
        <!--小圆点数量-->
        <attr name="vsb2_dot_count" format="integer|reference"/>
        <!--小圆点半径-->
        <attr name="vsb2_dot_radius" format="dimension|reference"/>
        <!--小圆点边框宽度-->
        <attr name="vsb2_dot_strokeWidth" format="dimension|reference"/>
        <!--小圆点边框颜色-->
        <attr name="vsb2_dot_strokeColor" format="color|reference"/>
        <!--小圆点背景色-->
        <attr name="vsb2_dot_solidBackColor" format="color|reference"/>
        <!--是否需要震动-->
        <attr name="vsb2_isNeedVibrate" format="boolean|reference" />
        <!-- 滑块 -->
        <!--滑块半径-->
        <attr name="vsb2_thumb_radius" format="dimension|reference"/>
        <!--滑块填充色-->
        <attr name="vsb2_thumb_solidColor" format="color|reference"/>
        <!--滑块边大小-->
        <attr name="vsb2_thumb_strokeWidth" format="dimension|reference"/>
        <!-- 底部百分比文字 -->
        <!--是否显示底部百分比数字-->
        <attr name="vsb2_percent_isShow" format="boolean|reference"/>
        <!--底部百分比数字文字大小-->
        <attr name="vsb2_percent_textSize" format="dimension|reference"/>
        <!--底部百分比数字文字颜色-->
        <attr name="vsb2_percent_textColor" format="color|reference"/>
        <!--底部百分比单位-->
        <attr name="vsb2_percent_unit" format="string|reference"/>
        <!--底部百分比数字距滑块的间距-->
        <attr name="vsb2_percent_margin" format="dimension|reference"/>
        <!-- 气泡 -->
        <!--是否显示气泡-->
        <attr name="vsb2_bubble_isShow" format="boolean|reference" />
        <!--气泡高度-->
        <attr name="vsb2_bubble_height" format="dimension|reference" />
        <!--气泡圆角-->
        <attr name="vsb2_bubble_radius" format="dimension|reference" />
        <!--气泡与滑块的间距-->
        <attr name="vsb2_bubble_margin" format="dimension|reference" />
        <!--气泡横向内间距-->
        <attr name="vsb2_bubble_horizontalPadding" format="dimension|reference" />
        <!--气泡文字大小-->
        <attr name="vsb2_bubble_textSize" format="dimension|reference" />
        <!--气泡文字颜色-->
        <attr name="vsb2_bubble_textColor" format="dimension|reference" />
        <!--气泡的背景色-->
        <attr name="vsb2_bubble_color" format="color|reference" />
    </declare-styleable>



</resources>