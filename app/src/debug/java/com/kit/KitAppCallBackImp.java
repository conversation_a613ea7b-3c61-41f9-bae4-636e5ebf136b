package com.kit;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.kit.baselibrary.APMConfigBean;
import com.kit.baselibrary.APiFilterBean;
import com.kit.baselibrary.APiFilterConfigBean;
import com.kit.baselibrary.AdvertiseJumpBean;
import com.kit.baselibrary.ApiReportConfigBean;
import com.kit.baselibrary.AppCallBackItf;
import com.kit.baselibrary.AppInfoBean;
import com.kit.baselibrary.ButtonConfigBean;
import com.kit.baselibrary.DetailConfigBean;
import com.kit.baselibrary.EnvironmentConfigBean;
import com.kit.baselibrary.IExtraBean;
import com.kit.baselibrary.MessageLoginBean;
import com.kit.baselibrary.MiniProgramBean;
import com.kit.baselibrary.MockConfigBean;
import com.kit.baselibrary.SwitchConfigBean;
import com.kit.extra.ProtocolHelperExtraEntry;
import com.kit.extra.QinSanjieExtraEntry;
import com.kit.extra.WXExtraEntry;
import com.kit.extra.WelcomePageExtraEntry;
import com.kit.extra.ZLExtraEntry;
import com.kit.extra.ZhangzhengExtraEntry;

import org.json.JSONArray;

import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Headers;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;


public class KitAppCallBackImp implements AppCallBackItf {
    public static final String TAG = "KitAppCallBackImp";

    @Override
    public void openHtmp5UrlListener(@Nullable String url) {
//        Context appContext = App.getApp();
//        if (appContext == null) return;
//        WebUtils.jump2Url(appContext,url,false);
    }

    @Override
    public AppInfoBean getAppInfo() {
        AppInfoBean appInfoBean = new AppInfoBean();
//        Account account = AccountHelper.getInstance().getAccount();
//        if(account != null){
//            appInfoBean.setUserId(String.valueOf(account.getApmUid()));
//        }else{
//            appInfoBean.setUserId("未登录");
//        }
        return appInfoBean;
    }


    @Override
    public List<String> getAnalysisUrlList() {
        final List<String> result = new ArrayList<>();
        return result;
    }


    @Override
    public MockConfigBean getMockConfigBean() {
        MockConfigBean mockConfigBean = new MockConfigBean();

        return mockConfigBean;
    }

    @Override
    public MiniProgramBean getMiniProgramBean() {
        MiniProgramBean miniProgramBean = new MiniProgramBean();
        return miniProgramBean;
    }


    @Override
    public ApiReportConfigBean getApiReportConfigBean() {
        ApiReportConfigBean apiReportConfigBean = new ApiReportConfigBean();
        apiReportConfigBean.setReportUrl("http://qa.kanzhun-inc.com/boss/log");
        return apiReportConfigBean;
    }


    /**
     * 业务层webviewActivity,用于加载h5的界面
     */
    @Override
    public List<Class<?>> getWebViewActivityList() {
        final List<Class<?>> result = new ArrayList<>();
//        result.add(WebViewActivity.class);
        return result;
    }

    @Override
    public List<AdvertiseJumpBean> getAdvertiseJumpList() {
        List<AdvertiseJumpBean> result = new ArrayList<>();
//        AdvertiseJumpBean advertiseJumpBean = new AdvertiseJumpBean();
//        advertiseJumpBean.setClassName(WelAct.class.getSimpleName());
//        advertiseJumpBean.setJumpText("跳过");
//        result.add(advertiseJumpBean);
        return result;
    }

    /**
     * 后续待boss 直聘调整完毕使用
     *
     * @return
     */
    @Override
    public EnvironmentConfigBean getEnvironmentConfigBean() {

        final EnvironmentConfigBean environmentConfigBean = new EnvironmentConfigBean();
//        CustomConfigActivity.show(App.getApp());

        return environmentConfigBean;
    }


    @Override
    public List<ButtonConfigBean> getButtonConfigList() {

        List<ButtonConfigBean> result = new ArrayList<>();


//        result.add(new ButtonConfigBean("LeakCanary", v -> {
//            try {
//                Activity topActivity = (Activity) App.getApp().getTopContext();
//                Intent intent = new Intent();
//                intent.setClassName(topActivity.getPackageName(), "leakcanary.internal.activity.LeakActivity");
//                topActivity.startActivity(intent);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }));

        return result;
    }

    @Override
    public List<SwitchConfigBean> getSwitchConfigList() {
        ArrayList<SwitchConfigBean> list = new ArrayList();
        //开启网络请求格式化开关，请勿反复切换
//        SwitchConfigBean openJsonLog = new SwitchConfigBean(GCommonSharedPreferences.get(SPConstants.OPEN_DEBUG_NETWORK_LOG_FORMAT,false), "网络格式化", new CompoundButton.OnCheckedChangeListener() {
//            @Override
//            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
//                GCommonSharedPreferences.set(SPConstants.OPEN_DEBUG_NETWORK_LOG_FORMAT,isChecked);
//            }
//        });
//        list.add(openJsonLog);
        return list;
    }


    private List<DetailConfigBean> detailConfigList;

    @Override
    public List<DetailConfigBean> getDetailConfigList() {
        if (detailConfigList == null) {
            detailConfigList = new ArrayList<>();
            detailConfigList.add(getDetailConfigBean("三方库信息", getLibraryInfo()));
        }
        return detailConfigList;
    }

    @Override
    public APiFilterConfigBean getGroupApiFilterList() {
        APiFilterConfigBean configBean = new APiFilterConfigBean();
        List<APiFilterBean> result = new ArrayList<>();
        result.add(getAPMFilter());
        result.add(getBgActionFilter());
        result.add(getStatisticFilter());
        result.add(getBusinessFilter());
        configBean.setaPiFilterBeanList(result);
        configBean.setDefaultSelectIndex(result.size() - 1);
        return configBean;
    }

    @Override
    public List<IExtraBean> getExtraConfigList() {
        List<IExtraBean> list = new ArrayList<>();
        list.add(new QinSanjieExtraEntry());
        list.add(new ZhangzhengExtraEntry());
        list.add(new ZLExtraEntry());
        list.add(new ProtocolHelperExtraEntry());
        list.add(new WelcomePageExtraEntry());
//        list.add(new DialogExtraEntry());
        list.add(new WXExtraEntry());
        return list;
    }


    private APiFilterBean getBusinessFilter() {
        APiFilterBean filterBean = new APiFilterBean();
        filterBean.setGroupName("查看业务接口");

        List<APiFilterBean.APiUrlBean> result = new ArrayList<>();

        APiFilterBean.APiUrlBean statisticBean = new APiFilterBean.APiUrlBean();
        statisticBean.setUnLikeUrl("%pay/finish%");
        result.add(statisticBean);


        filterBean.setaPiUrlList(result);
        return filterBean;
    }


    private APiFilterBean getStatisticFilter() {
        APiFilterBean filterBean = new APiFilterBean();
        filterBean.setGroupName("查看统计接口");

        List<APiFilterBean.APiUrlBean> result = new ArrayList<>();

        APiFilterBean.APiUrlBean aPiUrlBean = new APiFilterBean.APiUrlBean();
        aPiUrlBean.setLikeUrl("%statistics/common%");
        result.add(aPiUrlBean);

        filterBean.setaPiUrlList(result);
        return filterBean;
    }


    private APiFilterBean getBgActionFilter() {
        APiFilterBean filterBean = new APiFilterBean();
        filterBean.setGroupName("查看埋点接口");

        List<APiFilterBean.APiUrlBean> result = new ArrayList<>();

        APiFilterBean.APiUrlBean aPiUrlBean = new APiFilterBean.APiUrlBean();
        aPiUrlBean.setLikeUrl("%zpCommon/log/collector%");
        result.add(aPiUrlBean);

        filterBean.setaPiUrlList(result);
        return filterBean;
    }

    private APiFilterBean getAPMFilter() {
        APiFilterBean filterBean = new APiFilterBean();
        filterBean.setGroupName("查看APM接口");

        List<APiFilterBean.APiUrlBean> result = new ArrayList<>();

        APiFilterBean.APiUrlBean aPiUrlBean = new APiFilterBean.APiUrlBean();
        aPiUrlBean.setLikeUrl("%zpApm%");
        result.add(aPiUrlBean);

        filterBean.setaPiUrlList(result);
        return filterBean;
    }

    @Override
    public APMConfigBean getApmConfigInfo() {
        APMConfigBean apmConfigBean = new APMConfigBean();
//        apmConfigBean.setApmKey(Apm.get().getConfig().getsKey());
//        apmConfigBean.setApmParams(Apm.get().getConfig().getIvParameter());
        return apmConfigBean;
    }

    @Override
    public String decryHttpPostParam(String encryText, String url) {
        try {
            if (encryText.contains("sp")
                    && encryText.contains("sig")
                    && encryText.contains("app_id")
            ) {
                StringBuilder stringBuilder = new StringBuilder();
                Map<String, String> stringStringMap = splitText2Map(encryText);
                Set<String> keySet = stringStringMap.keySet();
                Iterator<String> iterator = keySet.iterator();
                while (iterator.hasNext()) {
                    String key = iterator.next();
                    if ("sp".equals(key)) {
                        String value = stringStringMap.get(key);
                        String decryValue = decryText(value, url);

                        if (TextUtils.equals(decryValue, value)) {
                            //解密失败
                            if (stringBuilder.length() != 0) {
                                stringBuilder.append("&");
                            }
                            stringBuilder.append("sp");
                            stringBuilder.append("=");
                            stringBuilder.append(decryValue);
                        } else {
                            Map<String, String> decodeMap = splitText2Map(decryValue);
                            Set<String> spKeySet = decodeMap.keySet();
                            Iterator<String> spIterator = spKeySet.iterator();
                            while (spIterator.hasNext()) {
                                String spKey = spIterator.next();
                                String spValue = decodeMap.get(spKey);
                                String spDecodeValue = URLDecoder.decode(spValue, "utf-8");

                                if (stringBuilder.length() != 0) {
                                    stringBuilder.append("&");
                                }
                                stringBuilder.append(spKey);
                                stringBuilder.append("=");
                                stringBuilder.append(spDecodeValue);
                            }
                        }


                    } else {
                        String value = stringStringMap.get(key);
                        if (stringBuilder.length() != 0) {
                            stringBuilder.append("&");
                        }
                        stringBuilder.append(key);
                        stringBuilder.append("=");
                        stringBuilder.append(value);
                    }
                }
                return stringBuilder.toString();

            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return encryText;
    }

    @Override
    public String decryHttpGetParams(String encryText, String url) {
        if (encryText != null && encryText.contains("?sp=")
                && !encryText.contains("client_info=")
                && encryText.contains("sig=")
                && encryText.contains("app_id=")
        ) {
            String[] split = encryText.split("\"?sp=");
            String preText = split[0];
            String endText = "sp=" + split[1];
            Map<String, String> stringStringMap = splitText2Map(endText);

            Set<String> keySet = stringStringMap.keySet();
            Iterator<String> iterator = keySet.iterator();

            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(preText);

            try {
                while (iterator.hasNext()) {
                    String k = iterator.next();
                    String v = stringStringMap.get(k);

                    if ("sp".equals(k)) {
                        String dV = decryText(v, url);
                        if (dV.equals(v)) {
                            //解密失败
                            if (!stringBuilder.toString().endsWith("&")) {
                                stringBuilder.append("&");
                            }
                            stringBuilder.append(k);
                            stringBuilder.append("=");
                            stringBuilder.append(v);
                        } else {
                            Map<String, String> splitText2Map = splitText2Map(dV);
                            Set<String> spKeySet = splitText2Map.keySet();
                            Iterator<String> spIterator = spKeySet.iterator();
                            while (spIterator.hasNext()) {
                                String key = spIterator.next();
                                String spValue = splitText2Map.get(key);
                                String decodeValue = URLDecoder.decode(spValue, "utf-8");
                                if (!stringBuilder.toString().endsWith("&")) {
                                    stringBuilder.append("&");
                                }
                                stringBuilder.append(key);
                                stringBuilder.append("=");
                                stringBuilder.append(decodeValue);
                            }
                        }


                    } else {
                        if (!stringBuilder.toString().endsWith("&")) {
                            stringBuilder.append("&");
                        }
                        stringBuilder.append(k);
                        stringBuilder.append("=");
                        stringBuilder.append(v);
                    }

                }
                return stringBuilder.toString();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return encryText;
    }

    @Override
    public String getReportDecryParams(String encryText, String url) {
//        TLog.info(TAG, "getReportDecryParams: url=" + url);

        if (url.startsWith("http")) {
            String[] split = url.split("/api/");
            if (split.length == 2) {
                url = "/api/" + split[1];
            }
        }

        String dV = decryText(encryText, url);
        if (!TextUtils.isEmpty(dV)) {

            StringBuilder stringBuilder = new StringBuilder();
            try {
                if (dV.contains("&")) {
                    String[] split = dV.split("&");
                    for (String item : split) {
                        if (item == null) continue;
                        String[] substring = item.split("=");
                        if (substring.length != 2) continue;
                        String k = substring[0];
                        String v = substring[1];
                        String decodeV = URLDecoder.decode(v, "utf-8");

                        if (!TextUtils.isEmpty(stringBuilder.toString())) {
                            stringBuilder.append("&");
                        }
                        stringBuilder.append(k);
                        stringBuilder.append("=");
                        stringBuilder.append(decodeV);
                    }
                    dV = stringBuilder.toString();
                }

            } catch (Exception e) {
                e.printStackTrace();
            }

//            TLog.info(TAG, "getReportDecryParams: " + dV);

            return dV;
        }
        return encryText;
    }

    @Override
    public String getDecryBgAction(String encryText, String url) {
        return "";
    }

    @Override
    public boolean isInterceptRequest(Headers header, ResponseBody responseBody, String url) {
        return false;
    }

    @Override
    public MessageLoginBean getMessageCodeInfo() {
        return null;
    }

    @Override
    public void onAutoInputMessageCode(boolean unLimitSendCode, String encryUrl) {

    }

    @Override
    public void reportHttpResponse(Request request) {
//        OkHttpClientFactory.getInstance().getGeneralClient().newCall(request).enqueue(new Callback() {
//            @Override
//            public void onFailure(Call call, IOException e) {
//                e.printStackTrace();
//            }
//
//            @Override
//            public void onResponse(Call call, Response response) throws IOException {
//            }
//        });
    }

    private DetailConfigBean getDetailConfigBean(String name, String info) {
        DetailConfigBean detailConfigBean = new DetailConfigBean();
        detailConfigBean.setName(name);
        detailConfigBean.setDetailText(info);
        return detailConfigBean;
    }

    private Map<String, String> splitText2Map(String text) {
        if (text == null) {
            return null;
        }

        String[] split = text.split("&");
        if (split == null) {
            return null;
        }

        Map<String, String> result = new HashMap<>();

        for (String item : split) {

            if (item == null) {
                continue;
            }
            String[] keyValue = item.split("=");
            if (keyValue != null && keyValue.length == 2) {
                String k = keyValue[0];
                String v = keyValue[1];
                result.put(k, v);
            }
        }

        return result;
    }

    /**
     * 解密sp参数
     *
     * @param encryText
     * @param url
     * @return
     */
    private String decryText(String encryText, String url) {

//        TLog.info(TAG, "decryText" + url);

        String secretKey = "";

        if (url != null) {
            if (url.contains("?")) {
                int index = url.indexOf("?");
                url = url.substring(0, index);
            }

            if (url.endsWith("/")) {
                url = url.substring(0, url.length() - 1);
            }
        }


//        boolean needLoginUrl = HttpUtils.INSTANCE.needLogin(url);
//        if (needLoginUrl) {
//            secretKey = LBase.getSecretKey();
//        } else {
//            secretKey = "";
//        }


        //登录接口的解密key是secretKey，非登陆接口的解密key是""
//        if (encryText != null) {
//            //非base64加日志
//            if (!isBase64Encode(encryText)) {
//                TLog.info(TAG, encryText);
//            }
//            byte[] resultBytes = Signer.getResultBytes(encryText.getBytes(StandardCharsets.UTF_8),
//                    secretKey, 1, 1, 2);
//
//            if (resultBytes != null && resultBytes.length > 0) {
//                return new String(resultBytes);
//            }
//
//        }
        return encryText;
    }

    private static boolean isBase64Encode(String content) {
        if (content.length() % 4 != 0) {
            return false;
        }
        String pattern = "^[a-zA-Z0-9/+]*={0,2}$";
        return Pattern.matches(pattern, content);
    }

    private String getLibraryInfo() {
        String info = "";

        try {
            JSONArray jsonObject = new JSONArray();
            String[] split = info.split("\n");
            for (String text : split) {
                jsonObject.put(text);
            }
            return jsonObject.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }


}
