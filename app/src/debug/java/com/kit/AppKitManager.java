package com.kit;

import android.app.Application;
import android.content.ContentValues;
import android.net.Uri;

import com.google.samples.apps.sunflower.MainApplication;
import com.twf.develophelpertools.Kit;
import com.twf.develophelpertools.chunk.internal.data.ChuckContentProvider;
import com.twf.develophelpertools.chunk.internal.data.HttpHeader;
import com.twf.develophelpertools.chunk.internal.data.HttpTransaction;
import com.twf.develophelpertools.chunk.internal.data.LocalCupboard;
import com.twf.develophelpertools.model.fragment.SwitchConfigFragment;
import com.twf.develophelpertools.util.KitLocationUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import okhttp3.Interceptor;


public class AppKitManager {

    private static final AppKitManager instance = new AppKitManager();

    public static AppKitManager getInstance() {
        return instance;
    }

    public void installKit(Application application) {
        Kit.getInstance().install(application, new KitAppCallBackImp());
    }

//    public Interceptor getChuckInterceptor() {
//        return Kit.getInstance().getChuckInterceptor();
//    }

    public Interceptor getReportInterceptor() {
        return Kit.getInstance().getReportInterceptor();
    }


    public void inflateMockLocation(Class<?> tClass, Object object) {
        new KitLocationUtil().inflateMockLocation(tClass, object);
    }

    public  boolean isOpenH5WaterMark(){
        return SwitchConfigFragment.isOpenH5WaterMark();
    }

//    public void printFlutterHttpInfo(String url,int statusCode,String params ,String data){
//        HttpTransaction transaction = new HttpTransaction();
//        transaction.setRequestDate(new Date());
//        transaction.setRequestBody(params);
//
//        List<HttpHeader> headers = new ArrayList<>();
//        HttpHeader httpHeader =new HttpHeader("params",params);
//        headers.add(httpHeader);
//        transaction.setRequestHeaders(headers);
//
//        transaction.setResponseCode(statusCode);
//        transaction.setMethod("POST");
//        transaction.setUrl(url);
//        transaction.setResponseBody(data);
//        ContentValues values = LocalCupboard.getInstance().withEntity(HttpTransaction.class).toContentValues(transaction);
//        Uri uri = MainApplication.CONTEXT.getContentResolver().insert(ChuckContentProvider.TRANSACTION_URI, values);
//        MainApplication.CONTEXT.getContentResolver().update(uri, values, null, null);
//    }

}